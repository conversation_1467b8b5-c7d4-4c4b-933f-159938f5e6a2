"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.adaptor = adaptor;
var utils_1 = require("../../utils");
/**
 * @param chart
 * @param options
 */
function adaptor(params) {
    /**
     * @description 当 angleField 总算为 0 时，设置默认样式
     * @link https://github.com/ant-design/ant-design-charts/issues/2324
     */
    var emptyData = function (params) {
        var options = params.options;
        var angleField = options.angleField, data = options.data, label = options.label, tooltip = options.tooltip, colorField = options.colorField;
        var getColorValue = (0, utils_1.fieldAdapter)(colorField);
        if ((0, utils_1.isArray)(data) && data.length > 0) {
            var sum = data.reduce(function (a, b) { return a + b[angleField]; }, 0);
            if (sum === 0) {
                var normalization = data.map(function (item) {
                    var _a;
                    return (__assign(__assign({}, item), (_a = {}, _a[angleField] = 1, _a)));
                });
                (0, utils_1.set)(options, 'data', normalization);
                if (label) {
                    var isColorField = colorField === (0, utils_1.get)(label, 'text');
                    (0, utils_1.set)(options, 'label', __assign(__assign({}, label), (isColorField ? {} : { formatter: function () { return 0; } })));
                }
                if (tooltip !== false) {
                    if ((0, utils_1.isFunction)(tooltip)) {
                        (0, utils_1.set)(options, 'tooltip', function (arg, index, items) {
                            var _a;
                            return tooltip(__assign(__assign({}, arg), (_a = {}, _a[angleField] = 0, _a)), index, items.map(function (item) {
                                var _a;
                                return (__assign(__assign({}, item), (_a = {}, _a[angleField] = 0, _a)));
                            }));
                        });
                    }
                    else {
                        (0, utils_1.set)(options, 'tooltip', __assign(__assign({}, tooltip), { items: [
                                function (arg, i, d) {
                                    return {
                                        name: getColorValue(arg, i, d),
                                        value: 0,
                                    };
                                },
                            ] }));
                    }
                }
            }
        }
        return params;
    };
    return (0, utils_1.flow)(emptyData, utils_1.transformOptions)(params);
}
