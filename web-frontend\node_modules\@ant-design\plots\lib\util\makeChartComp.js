"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeChartComp = makeChartComp;
var react_1 = __importStar(require("react"));
var base_1 = require("../components/base");
var useConfig_1 = __importDefault(require("../hooks/useConfig"));
var scale_1 = __importDefault(require("./scale"));
var util_1 = require("../util");
function makeChartComp(chartType) {
    var configKey = chartType.charAt(0).toLowerCase() + chartType.slice(1);
    return (0, react_1.forwardRef)(function (props, ref) {
        var config = (0, useConfig_1.default)();
        var flowProps = (0, util_1.flow)([scale_1.default])(props);
        if (!config || !config[configKey]) {
            return react_1.default.createElement(base_1.BaseChart, __assign({}, flowProps, { chartType: chartType, ref: ref }));
        }
        return react_1.default.createElement(base_1.BaseChart, __assign({}, config.common, config[configKey], flowProps, { chartType: chartType, ref: ref }));
    });
}
