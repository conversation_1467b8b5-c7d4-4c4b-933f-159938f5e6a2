{"version": 3, "file": "fisheye.js", "sourceRoot": "src/", "sources": ["transforms/fisheye.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,sDAAsD;AACtD,kEAAkE;AAClE,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAGrC,SAAS,gBAAgB,CAAC,CAAS,EAAE,KAAa,EAAE,UAAkB,EAAE,GAAW,EAAE,GAAW;IAC9F,IAAM,IAAI,GAAG,CAAC,GAAG,KAAK,CAAC;IACvB,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;IAC1D,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACnF,CAAC;AAED,SAAS,kBAAkB,CAAC,EAAU,EAAE,KAAa,EAAE,UAAkB,EAAE,GAAW,EAAE,GAAW;IACjG,IAAM,IAAI,GAAG,EAAE,GAAG,KAAK,CAAC;IACxB,IAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,IAAI,GAAG,GAAG,GAAG,CAAC;IAC1D,IAAM,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;AAC9E,CAAC;AAED;;GAEG;AACH,SAAS,SAAS,CAAC,KAAa,EAAE,MAAc,EAAE,QAAiB;IACjE,IAAI,CAAC,QAAQ;QAAE,OAAO,KAAK,CAAC;IAE5B,IAAM,CAAC,GAAG,IAAI,MAAM,CAAC;QACnB,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QACb,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;KACpB,CAAC,CAAC;IACH,OAAO,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtB,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,QAAQ,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC/D,IAAA,KAAA,OAAwC,MAAmC,IAAA,EAA1E,KAAK,QAAA,EAAE,UAAU,QAAA,EAAE,UAAgB,EAAhB,QAAQ,mBAAG,KAAK,KAAuC,CAAC;IAClF,IAAM,gBAAgB,GAAG,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC3D,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,kBAAkB,CAAC,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACtE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,QAAQ,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC/D,IAAA,KAAA,OAAwC,MAAmC,IAAA,EAA1E,KAAK,QAAA,EAAE,UAAU,QAAA,EAAE,UAAgB,EAAhB,QAAQ,mBAAG,KAAK,KAAuC,CAAC;IAClF,IAAM,gBAAgB,GAAG,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAE5D,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACpE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,kBAAkB,CAAC,EAAE,EAAE,gBAAgB,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACtE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,OAAO,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC9D,IAAA,KAAA,OAA+D,MAMpE,IAAA,EANM,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,WAAW,QAAA,EAAE,WAAW,QAAA,EAAE,UAAgB,EAAhB,QAAQ,mBAAG,KAAK,KAMhE,CAAC;IACF,IAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC5D,IAAM,gBAAgB,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;IAE7D,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,IAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,kBAAkB,CAAC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,IAAM,EAAE,GAAG,kBAAkB,CAAC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACvE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,eAAe,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IACtE,IAAA,KAAA,OAAyD,MAAmD,IAAA,EAA3G,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,MAAM,QAAA,EAAE,UAAU,QAAA,EAAE,UAAgB,EAAhB,QAAQ,mBAAG,KAAK,KAAuD,CAAC;IAEnH,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;QACxB,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;KAClB,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;QACxB,KAAK,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;KACnB,CAAC,CAAC;IACH,8BAA8B;IAC9B,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAClD,IAAM,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAElD,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAS,MAAM,IAAA,EAAd,CAAC,QAAA,EAAE,CAAC,QAAU,CAAC;YACtB,8BAA8B;YAC9B,IAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9B,IAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;YAC9B,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAExC,IAAI,EAAE,GAAG,MAAM;gBAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/B,IAAM,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YACzD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEjC,IAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpC,IAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpC,8BAA8B;YAC9B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAC/B,IAAM,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;YAC/B,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YACxC,IAAI,EAAE,GAAG,MAAM;gBAAE,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEjC,IAAM,CAAC,GAAG,kBAAkB,CAAC,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;YAC3D,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;YAEjC,IAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpC,IAAM,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YACpC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAChD,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}