{"version": 3, "file": "parallel.js", "sourceRoot": "src/", "sources": ["transforms/parallel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,sDAAsD;AACtD,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,aAAa,CAAC;AAG5C;;;;;;;;GAQG;AACH,MAAM,CAAC,IAAM,QAAQ,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC/D,IAAA,KAAA,OAAmB,MAAkB,IAAA,EAApC,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAA,EAAE,EAAE,QAAsB,CAAC;IAC5C,IAAM,EAAE,GAAG,IAAI,MAAM,CAAC;QACpB,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;KAChB,CAAC,CAAC;IACH,OAAO;QACL,SAAS,YAAC,MAAc;YACtB,IAAM,CAAC,GAAG,EAAE,CAAC;YACb,IAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;YAC1B,IAAM,EAAE,GAAG,IAAI,KAAK,CAAC;gBACnB,MAAM,EAAE,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,EAAD,CAAC,CAAC;gBAC/C,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;aAChB,CAAC,CAAC;YACH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,IAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAM,GAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAM,GAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACpB,CAAC,CAAC,IAAI,CAAC,GAAC,EAAE,GAAC,CAAC,CAAC;aACd;YACD,OAAO,CAAC,CAAC;QACX,CAAC;QACD,WAAW,YAAC,MAAc;YACxB,IAAM,CAAC,GAAG,EAAE,CAAC;YACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBACzC,IAAM,GAAC,GAAG,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,GAAC,CAAC,CAAC,CAAC;aACtB;YACD,OAAO,CAAC,CAAC;QACX,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}