#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化预测流程测试脚本

测试同步管理器的自动预测生成功能。

执行ID: execute_006
创建时间: 2025-08-12 03:50:00
"""

import sys
import os
from pathlib import Path
import sqlite3
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_sync_manager_prediction():
    """测试同步管理器的预测生成功能"""
    print("🔧 测试同步管理器自动预测生成")
    print("=" * 50)
    
    try:
        # 导入同步管理器
        from src.sync.sync_manager import SyncManager
        
        # 创建同步管理器实例
        sync_manager = SyncManager()
        
        print("✅ 同步管理器创建成功")
        
        # 测试获取最新期号
        latest_issue = sync_manager._get_latest_issue_from_db()
        print(f"📊 最新期号: {latest_issue}")
        
        if latest_issue:
            next_issue = str(int(latest_issue) + 1)
            print(f"📊 下一期期号: {next_issue}")
            
            # 检查预测数据是否存在
            exists = sync_manager._check_prediction_exists(next_issue)
            print(f"📊 预测数据存在: {exists}")
            
            # 测试预测生成功能
            print(f"\n🎯 测试预测生成功能...")
            result = sync_manager._step5_generate_predictions()
            
            print(f"📋 预测生成结果:")
            print(f"  步骤: {result.get('step')}")
            print(f"  成功: {result.get('success')}")
            print(f"  期号: {result.get('issue', 'N/A')}")
            print(f"  消息: {result.get('message', result.get('error', 'N/A'))}")
            
            if result.get('prediction_count'):
                print(f"  预测数量: {result.get('prediction_count')}")
            
            return result.get('success', False)
        else:
            print("❌ 无法获取最新期号")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_auto_prediction_config():
    """测试自动预测配置"""
    print(f"\n🔧 测试自动预测配置")
    print("=" * 30)
    
    try:
        from src.sync.sync_manager import SyncManager
        
        sync_manager = SyncManager()
        
        print(f"📊 自动生成预测: {sync_manager.auto_generate_predictions}")
        
        if sync_manager.auto_generate_predictions:
            print("✅ 自动预测功能已启用")
        else:
            print("⚠️ 自动预测功能已禁用")
        
        return sync_manager.auto_generate_predictions
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_prediction_trigger():
    """测试预测触发机制"""
    print(f"\n🔧 测试预测触发机制")
    print("=" * 30)
    
    try:
        from src.sync.sync_manager import SyncManager
        
        sync_manager = SyncManager()
        
        # 模拟完整同步流程
        print("🔄 执行完整同步流程...")
        sync_result = sync_manager.execute_sync(force=False)
        
        print(f"📋 同步结果:")
        print(f"  成功: {sync_result.get('success')}")
        print(f"  消息: {sync_result.get('message')}")
        
        # 检查步骤结果
        steps = sync_result.get('steps', [])
        for step in steps:
            step_name = step.get('step', 'unknown')
            step_success = step.get('success', False)
            step_msg = step.get('message', step.get('error', 'N/A'))
            
            status = "✅" if step_success else "❌"
            print(f"  {status} {step_name}: {step_msg}")
            
            if step_name == 'generate_predictions' and step_success:
                print(f"    期号: {step.get('issue', 'N/A')}")
                print(f"    预测数量: {step.get('prediction_count', 'N/A')}")
        
        return sync_result.get('success', False)
        
    except Exception as e:
        print(f"❌ 触发测试失败: {e}")
        return False

def verify_prediction_quality():
    """验证预测数据质量"""
    print(f"\n🔧 验证预测数据质量")
    print("=" * 30)
    
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 获取最新预测期号
        cursor.execute("SELECT MAX(issue) FROM final_predictions")
        latest_pred_issue = cursor.fetchone()[0]
        
        if latest_pred_issue:
            print(f"📊 最新预测期号: {latest_pred_issue}")
            
            # 检查预测数据质量
            cursor.execute("""
                SELECT COUNT(*) as total,
                       AVG(combined_probability) as avg_prob,
                       MIN(combined_probability) as min_prob,
                       MAX(combined_probability) as max_prob
                FROM final_predictions 
                WHERE issue = ?
            """, (latest_pred_issue,))
            
            stats = cursor.fetchone()
            total, avg_prob, min_prob, max_prob = stats
            
            print(f"📊 预测统计:")
            print(f"  总数量: {total}")
            print(f"  平均概率: {avg_prob:.2f}%")
            print(f"  概率范围: {min_prob:.2f}% - {max_prob:.2f}%")
            
            # 检查数据完整性
            cursor.execute("""
                SELECT COUNT(*) 
                FROM final_predictions 
                WHERE issue = ? AND (
                    hundreds IS NULL OR tens IS NULL OR units IS NULL OR
                    combined_probability IS NULL
                )
            """, (latest_pred_issue,))
            
            incomplete = cursor.fetchone()[0]
            
            if incomplete == 0:
                print("✅ 预测数据完整性检查通过")
            else:
                print(f"⚠️ 发现 {incomplete} 条不完整的预测数据")
            
            conn.close()
            return incomplete == 0 and total > 0
        else:
            print("❌ 无预测数据")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 质量验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 自动化预测流程测试工具")
    print("=" * 60)
    
    try:
        # 1. 测试配置
        config_ok = test_auto_prediction_config()
        
        # 2. 测试预测生成功能
        prediction_ok = test_sync_manager_prediction()
        
        # 3. 测试触发机制
        trigger_ok = test_prediction_trigger()
        
        # 4. 验证预测质量
        quality_ok = verify_prediction_quality()
        
        # 5. 综合结果
        print(f"\n🎉 测试结果总结:")
        print(f"  配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
        print(f"  预测生成: {'✅ 通过' if prediction_ok else '❌ 失败'}")
        print(f"  触发机制: {'✅ 通过' if trigger_ok else '❌ 失败'}")
        print(f"  数据质量: {'✅ 通过' if quality_ok else '❌ 失败'}")
        
        overall_success = config_ok and prediction_ok and trigger_ok and quality_ok
        print(f"\n🏆 总体结果: {'✅ 自动化预测流程正常' if overall_success else '⚠️ 需要进一步调试'}")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 测试过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
