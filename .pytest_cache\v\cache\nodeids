["tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_all_accuracy_types_consistency", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_comprehensive_accuracy_weighted_calculation", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_direct_accuracy_no_match", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_direct_accuracy_partial_match", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_direct_accuracy_perfect_match", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_empty_actual_handling", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_empty_predictions_handling", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_group_accuracy_same_digits", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_group_accuracy_with_permutations", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_invalid_format_handling", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_number_padding", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_position_accuracy_all_positions", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_span_accuracy_matching_spans", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_span_accuracy_zero_span", "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_sum_accuracy_matching_sums", "tests/test_accuracy_calculator.py::TestAccuracyCalculatorEdgeCases::test_duplicate_predictions", "tests/test_accuracy_calculator.py::TestAccuracyCalculatorEdgeCases::test_large_prediction_list", "tests/test_all_modules.py::TestDataCollector::test_anti_crawler_config", "tests/test_all_modules.py::TestDataCollector::test_url_validation", "tests/test_all_modules.py::TestDataParser::test_number_type_detection", "tests/test_all_modules.py::TestDataParser::test_text_parsing", "tests/test_all_modules.py::TestDataValidator::test_format_validation", "tests/test_all_modules.py::TestDataValidator::test_logic_validation", "tests/test_all_modules.py::TestDatabaseModels::test_data_validation", "tests/test_all_modules.py::TestDatabaseModels::test_database_creation", "tests/test_all_modules.py::TestIncrementalUpdater::test_get_latest_issue", "tests/test_all_modules.py::TestIncrementalUpdater::test_record_insertion", "tests/test_all_modules.py::TestIntegration::test_end_to_end_workflow", "tests/test_api.py::TestCacheAPI::test_cache_health", "tests/test_api.py::TestCacheAPI::test_cache_performance", "tests/test_api.py::TestCacheAPI::test_get_cache_stats", "tests/test_api.py::TestErrorHandling::test_invalid_endpoint", "tests/test_api.py::TestErrorHandling::test_invalid_parameters", "tests/test_api.py::TestErrorHandling::test_invalid_task_type", "tests/test_api.py::TestHealthEndpoints::test_health_check", "tests/test_api.py::TestHealthEndpoints::test_readiness_check", "tests/test_api.py::TestMonitoringAPI::test_get_optimization_tasks", "tests/test_api.py::TestMonitoringAPI::test_get_performance_metrics", "tests/test_api.py::TestMonitoringAPI::test_get_system_status", "tests/test_api.py::TestOptimizationAPI::test_get_optimization_config", "tests/test_api.py::TestOptimizationAPI::test_system_diagnostics", "tests/test_api.py::TestOptimizationAPI::test_trigger_optimization", "tests/test_api.py::TestPerformance::test_api_response_time", "tests/test_api.py::TestPerformance::test_concurrent_requests", "tests/test_api.py::TestPredictionAPI::test_get_accuracy_trends", "tests/test_api.py::TestPredictionAPI::test_get_latest_predictions", "tests/test_api.py::TestPredictionAPI::test_get_performance_comparison", "tests/test_api.py::TestPredictionAPI::test_get_prediction_statistics", "tests/test_features.py::TestFeatureCalculator::test_basic_features", "tests/test_features.py::TestFeatureCalculator::test_big_medium_small_features", "tests/test_features.py::TestFeatureCalculator::test_convenience_function", "tests/test_features.py::TestFeatureCalculator::test_edge_cases", "tests/test_features.py::TestFeatureCalculator::test_feature_completeness", "tests/test_features.py::TestFeatureCalculator::test_lottery_data_integration", "tests/test_features.py::TestFeatureCalculator::test_pattern_features", "tests/test_features.py::TestFeatureCalculator::test_route_012_features", "tests/test_features.py::TestFeatureCalculator::test_trend_features", "tests/test_features.py::TestFeatureValidation::test_feature_value_ranges", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_invalid_format", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_network_error", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_success", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_format_result", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_connection", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_latest_result_from_database", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_result_by_issue_from_database", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_result_by_issue_nonexistent", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_save_to_database", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_save_to_database_duplicate", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_date", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_issue", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_numbers", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_missing_fields", "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_valid", "tests/test_lottery_query.py::TestLotteryQueryEngineIntegration::test_full_query_workflow_with_network_fallback", "tests/test_p2_basic.py::test_advanced_feature_engineer", "tests/test_p2_performance.py::test_p2_performance", "tests/test_p8_benchmark.py::TestP8Benchmark::test_01_fusion_predictor_initialization_benchmark", "tests/test_p8_benchmark.py::TestP8Benchmark::test_02_data_access_performance_benchmark", "tests/test_p8_benchmark.py::TestP8Benchmark::test_03_performance_monitoring_benchmark", "tests/test_p8_benchmark.py::TestP8Benchmark::test_04_concurrent_access_benchmark", "tests/test_p8_benchmark.py::TestP8Benchmark::test_05_memory_usage_benchmark", "tests/test_p8_integration.py::TestP8Integration::test_01_fusion_predictor_initialization", "tests/test_p8_integration.py::TestP8Integration::test_02_data_access_functionality", "tests/test_p8_integration.py::TestP8Integration::test_03_performance_monitoring", "tests/test_p8_integration.py::TestP8Integration::test_04_report_generation", "tests/test_p8_integration.py::TestP8Integration::test_05_auto_adjustment_trigger", "tests/test_p8_integration.py::TestP8Integration::test_06_end_to_end_prediction", "tests/test_p8_integration.py::TestP8Integration::test_07_error_handling", "tests/test_p8_integration.py::TestP8Integration::test_08_performance_evaluation", "tests/test_p9_basic.py::TestP9Basic::test_p9_component_imports", "tests/test_p9_basic.py::TestP9Basic::test_p9_manager_initialization", "tests/test_p9_basic.py::TestP9Basic::test_p9_optimizer_initialization", "tests/test_p9_basic.py::TestP9Basic::test_p9_system_lifecycle", "tests/test_p9_basic.py::TestP9Basic::test_p9_system_status", "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_empty_results", "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_with_data", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_direct_hit", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_empty_input", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_group_hit", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_invalid_format", "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_position_hits", "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary", "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary_empty_data", "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions", "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions_nonexistent_issue", "tests/test_review_engine.py::TestReviewEngine::test_lottery_query_integration", "tests/test_review_engine.py::TestReviewEngineIntegration::test_full_review_workflow", "tests/test_review_integration.py::TestClosedLoopSystemIntegration::test_closed_loop_review_integration", "tests/test_review_integration.py::TestReviewIntegration::test_accuracy_calculator_integration", "tests/test_review_integration.py::TestReviewIntegration::test_complete_review_workflow", "tests/test_review_integration.py::TestReviewIntegration::test_data_storage_and_query", "tests/test_review_integration.py::TestReviewIntegration::test_error_handling_and_recovery", "tests/test_review_integration.py::TestReviewIntegration::test_large_dataset_performance", "tests/test_review_integration.py::TestReviewIntegration::test_performance_benchmark", "tests/test_span_predictor.py::TestIntegration::test_full_workflow", "tests/test_span_predictor.py::TestSpanDataAccess::test_analyze_span_patterns", "tests/test_span_predictor.py::TestSpanDataAccess::test_get_data_statistics", "tests/test_span_predictor.py::TestSpanDataAccess::test_load_lottery_data", "tests/test_span_predictor.py::TestSpanDataAccess::test_save_prediction_result", "tests/test_span_predictor.py::TestSpanModels::test_classification_model_interface", "tests/test_span_predictor.py::TestSpanModels::test_constraint_model_interface", "tests/test_span_predictor.py::TestSpanModels::test_ensemble_model_interface", "tests/test_span_predictor.py::TestSpanModels::test_lgb_model_interface", "tests/test_span_predictor.py::TestSpanModels::test_lstm_model_interface", "tests/test_span_predictor.py::TestSpanModels::test_xgb_model_interface", "tests/test_span_predictor.py::TestSpanPredictor::test_build_model", "tests/test_span_predictor.py::TestSpanPredictor::test_calculate_constraint_consistency_score", "tests/test_span_predictor.py::TestSpanPredictor::test_constraint_configuration", "tests/test_span_predictor.py::TestSpanPredictor::test_get_available_models", "tests/test_span_predictor.py::TestSpanPredictor::test_get_model_info", "tests/test_span_predictor.py::TestSpanPredictor::test_initialization", "tests/test_span_predictor.py::TestSpanPredictor::test_pattern_analysis_configuration", "tests/test_span_predictor.py::TestSpanPredictor::test_predict_pattern_probability", "tests/test_span_predictor.py::TestSpanPredictor::test_switch_model", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_complete_training_workflow", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_constraint_optimization_workflow", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_data_persistence_workflow", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_end_to_end_integration", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_model_evaluation_workflow", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_model_persistence_workflow", "tests/test_sum_integration.py::TestSumPredictorIntegration::test_prediction_workflow", "tests/test_sum_predictor.py::TestSumDataAccess::test_get_performance_history", "tests/test_sum_predictor.py::TestSumDataAccess::test_get_prediction_history", "tests/test_sum_predictor.py::TestSumDataAccess::test_save_performance_metrics", "tests/test_sum_predictor.py::TestSumDataAccess::test_save_prediction_result", "tests/test_sum_predictor.py::TestSumModels::test_distribution_sum_model_basic", "tests/test_sum_predictor.py::TestSumModels::test_lgb_sum_model_basic", "tests/test_sum_predictor.py::TestSumModels::test_xgb_sum_model_basic", "tests/test_sum_predictor.py::TestSumPredictor::test_build_model", "tests/test_sum_predictor.py::TestSumPredictor::test_get_available_models", "tests/test_sum_predictor.py::TestSumPredictor::test_model_switching", "tests/test_sum_predictor.py::TestSumPredictor::test_sum_predictor_initialization", "tests/test_sum_predictor.py::TestSumPredictorIntegration::test_end_to_end_workflow", "tests/test_unified_interface.py::TestUnifiedInterface::test_constraint_matrix", "tests/test_unified_interface.py::TestUnifiedInterface::test_data_format_standardization", "tests/test_unified_interface.py::TestUnifiedInterface::test_predictor_loading", "tests/test_unified_interface.py::TestUnifiedInterface::test_sum_predictor_new_interface", "tests/test_unified_interface.py::TestUnifiedInterface::test_unified_predictions", "tests/test_unified_interface.py::TestUnifiedInterface::test_validation_functions"]