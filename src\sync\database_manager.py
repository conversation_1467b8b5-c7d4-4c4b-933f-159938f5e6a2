"""
数据库管理器
负责管理主数据库和从数据库的同步操作
"""

import sqlite3
import logging
import os
import shutil
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path

from .utils import get_database_info, ensure_directory

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('databases', {})
        self.master_db = self.config.get('master', {})
        self.slave_dbs = self.config.get('slaves', [])
        
        # 主数据库路径
        self.master_path = self.master_db.get('path', 'data/lottery.db')
        
        # 确保数据库目录存在
        ensure_directory(os.path.dirname(self.master_path))
        
        logger.info(f"数据库管理器初始化完成")
        logger.info(f"主数据库: {self.master_path}")
        logger.info(f"从数据库数量: {len(self.slave_dbs)}")
    
    def get_master_latest_issue(self) -> Optional[str]:
        """获取主数据库最新期号"""
        try:
            conn = sqlite3.connect(self.master_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT MAX(issue) FROM lottery_data")
            result = cursor.fetchone()
            conn.close()
            
            return result[0] if result and result[0] else None
            
        except Exception as e:
            logger.error(f"获取主数据库最新期号失败: {e}")
            return None
    
    def get_master_data_range(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取主数据库最新N条记录"""
        try:
            conn = sqlite3.connect(self.master_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT issue, draw_date, hundreds, tens, units, sum_value, span, number_type
                FROM lottery_data 
                ORDER BY issue DESC 
                LIMIT ?
            """, (limit,))
            
            results = cursor.fetchall()
            conn.close()
            
            records = []
            for row in results:
                records.append({
                    'issue': row[0],
                    'draw_date': row[1],
                    'hundreds': row[2],
                    'tens': row[3],
                    'units': row[4],
                    'sum_value': row[5],
                    'span': row[6],
                    'number_type': row[7]
                })
            
            return records
            
        except Exception as e:
            logger.error(f"获取主数据库数据失败: {e}")
            return []
    
    def update_master_database(self, records: List[Dict[str, Any]]) -> int:
        """更新主数据库"""
        if not records:
            return 0
        
        updated_count = 0
        
        try:
            conn = sqlite3.connect(self.master_path)
            cursor = conn.cursor()
            
            # 确保表结构存在
            self._ensure_master_table_structure(cursor)
            
            for record in records:
                # 检查记录是否已存在
                cursor.execute("SELECT id FROM lottery_data WHERE issue = ?", (record['issue'],))
                exists = cursor.fetchone()
                
                if not exists:
                    # 计算衍生字段
                    sum_value = record.get('sum_value', record['hundreds'] + record['tens'] + record['units'])
                    span = record.get('span', max(record['hundreds'], record['tens'], record['units']) - 
                                                min(record['hundreds'], record['tens'], record['units']))
                    
                    # 判断号码类型
                    numbers = [record['hundreds'], record['tens'], record['units']]
                    unique_count = len(set(numbers))
                    if unique_count == 1:
                        number_type = "豹子"
                    elif unique_count == 2:
                        number_type = "对子"
                    else:
                        number_type = "组六"
                    
                    # 插入新记录
                    cursor.execute("""
                        INSERT INTO lottery_data (
                            issue, draw_date, hundreds, tens, units,
                            sum_value, span, number_type, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record['issue'], record['draw_date'],
                        record['hundreds'], record['tens'], record['units'],
                        sum_value, span, number_type,
                        datetime.now().isoformat(), datetime.now().isoformat()
                    ))
                    
                    updated_count += 1
                    logger.info(f"新增记录到主数据库: 期号{record['issue']}, 号码{record['hundreds']}{record['tens']}{record['units']}")
                else:
                    logger.debug(f"记录已存在: 期号{record['issue']}")
            
            conn.commit()
            conn.close()
            
            logger.info(f"主数据库更新完成，新增 {updated_count} 条记录")
            return updated_count
            
        except Exception as e:
            logger.error(f"更新主数据库失败: {e}")
            return 0
    
    def _ensure_master_table_structure(self, cursor: sqlite3.Cursor):
        """确保主数据库表结构正确"""
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS lottery_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                issue TEXT UNIQUE NOT NULL,
                draw_date TEXT NOT NULL,
                hundreds INTEGER NOT NULL CHECK (hundreds >= 0 AND hundreds <= 9),
                tens INTEGER NOT NULL CHECK (tens >= 0 AND tens <= 9),
                units INTEGER NOT NULL CHECK (units >= 0 AND units <= 9),
                trial_hundreds INTEGER CHECK (trial_hundreds IS NULL OR (trial_hundreds >= 0 AND trial_hundreds <= 9)),
                trial_tens INTEGER CHECK (trial_tens IS NULL OR (trial_tens >= 0 AND trial_tens <= 9)),
                trial_units INTEGER CHECK (trial_units IS NULL OR (trial_units >= 0 AND trial_units <= 9)),
                machine_number TEXT,
                sales_amount REAL CHECK (sales_amount IS NULL OR sales_amount >= 0),
                prize_info TEXT,
                sum_value INTEGER CHECK (sum_value >= 0 AND sum_value <= 27),
                span INTEGER CHECK (span >= 0 AND span <= 9),
                number_type TEXT CHECK (number_type IN ('豹子', '对子', '组六')),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def sync_to_slave_databases(self) -> Dict[str, bool]:
        """同步数据到所有从数据库"""
        results = {}
        
        # 获取主数据库的最新数据
        master_data = self.get_master_data_range(50)  # 获取最新50条记录用于同步
        
        if not master_data:
            logger.warning("主数据库没有数据可同步")
            return results
        
        for slave_config in self.slave_dbs:
            slave_path = slave_config.get('path')
            if not slave_path:
                continue
            
            try:
                success = self._sync_to_single_slave(slave_path, master_data)
                results[slave_path] = success
                
                if success:
                    logger.info(f"成功同步到从数据库: {slave_path}")
                else:
                    logger.error(f"同步到从数据库失败: {slave_path}")
                    
            except Exception as e:
                logger.error(f"同步到从数据库 {slave_path} 时发生异常: {e}")
                results[slave_path] = False
        
        return results
    
    def _sync_to_single_slave(self, slave_path: str, master_data: List[Dict[str, Any]]) -> bool:
        """同步数据到单个从数据库"""
        try:
            # 确保从数据库目录存在
            ensure_directory(os.path.dirname(slave_path))
            
            conn = sqlite3.connect(slave_path)
            cursor = conn.cursor()
            
            # 确保表结构存在
            self._ensure_master_table_structure(cursor)
            
            synced_count = 0
            for record in master_data:
                # 检查记录是否已存在
                cursor.execute("SELECT id FROM lottery_data WHERE issue = ?", (record['issue'],))
                exists = cursor.fetchone()
                
                if not exists:
                    cursor.execute("""
                        INSERT INTO lottery_data (
                            issue, draw_date, hundreds, tens, units,
                            sum_value, span, number_type, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        record['issue'], record['draw_date'],
                        record['hundreds'], record['tens'], record['units'],
                        record.get('sum_value', 0), record.get('span', 0), 
                        record.get('number_type', '组六'),
                        datetime.now().isoformat(), datetime.now().isoformat()
                    ))
                    synced_count += 1
            
            conn.commit()
            conn.close()
            
            logger.info(f"同步到 {slave_path} 完成，新增 {synced_count} 条记录")
            return True
            
        except Exception as e:
            logger.error(f"同步到 {slave_path} 失败: {e}")
            return False
    
    def check_database_consistency(self) -> Dict[str, Any]:
        """检查数据库一致性"""
        consistency_report = {
            'master': get_database_info(self.master_path),
            'slaves': {},
            'consistent': True,
            'issues': []
        }
        
        master_info = consistency_report['master']
        master_latest = master_info.get('latest_issue')
        master_count = master_info.get('record_count', 0)
        
        for slave_config in self.slave_dbs:
            slave_path = slave_config.get('path')
            if not slave_path:
                continue
            
            slave_info = get_database_info(slave_path)
            consistency_report['slaves'][slave_path] = slave_info
            
            slave_latest = slave_info.get('latest_issue')
            slave_count = slave_info.get('record_count', 0)
            
            # 检查最新期号一致性
            if master_latest != slave_latest:
                consistency_report['consistent'] = False
                consistency_report['issues'].append(
                    f"期号不一致: 主库{master_latest} vs {slave_path}:{slave_latest}"
                )
            
            # 检查记录数量差异（允许小幅差异）
            if abs(master_count - slave_count) > 5:
                consistency_report['consistent'] = False
                consistency_report['issues'].append(
                    f"记录数量差异过大: 主库{master_count} vs {slave_path}:{slave_count}"
                )
        
        return consistency_report
    
    def backup_databases(self, backup_dir: str = "backups/sync") -> bool:
        """备份数据库"""
        try:
            ensure_directory(backup_dir)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 备份主数据库
            if os.path.exists(self.master_path):
                backup_path = os.path.join(backup_dir, f"master_{timestamp}.db")
                shutil.copy2(self.master_path, backup_path)
                logger.info(f"主数据库备份完成: {backup_path}")
            
            # 备份从数据库
            for slave_config in self.slave_dbs:
                slave_path = slave_config.get('path')
                if slave_path and os.path.exists(slave_path):
                    slave_name = os.path.basename(slave_path).replace('.db', '')
                    backup_path = os.path.join(backup_dir, f"{slave_name}_{timestamp}.db")
                    shutil.copy2(slave_path, backup_path)
                    logger.info(f"从数据库备份完成: {backup_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False
