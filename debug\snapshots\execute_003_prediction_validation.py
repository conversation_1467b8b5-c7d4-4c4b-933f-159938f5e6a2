#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预测数据真实性验证脚本

验证2025214期的预测数据是否基于真实历史开奖数据生成。

执行ID: execute_003
创建时间: 2025-08-12 03:40:00
"""

import sqlite3
import os
from datetime import datetime

def validate_prediction_data_source():
    """验证预测数据来源的真实性"""
    print("🔍 验证预测数据来源的真实性")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 1. 检查历史数据基础
        cursor.execute("SELECT COUNT(*) FROM lottery_data")
        historical_count = cursor.fetchone()[0]
        print(f"📊 历史开奖数据: {historical_count:,}条")
        
        # 2. 检查最新期号
        cursor.execute("SELECT MAX(issue) FROM lottery_data")
        latest_issue = cursor.fetchone()[0]
        next_issue = str(int(latest_issue) + 1)
        print(f"📊 最新开奖期号: {latest_issue}")
        print(f"📊 预测期号: {next_issue}")
        
        # 3. 检查预测数据
        cursor.execute("SELECT COUNT(*) FROM final_predictions WHERE issue = ?", (next_issue,))
        prediction_count = cursor.fetchone()[0]
        print(f"📊 预测数据条数: {prediction_count}")
        
        # 4. 验证数据质量
        if prediction_count > 0:
            cursor.execute("""
                SELECT hundreds, tens, units, combined_probability, confidence_level, created_at
                FROM final_predictions 
                WHERE issue = ? 
                ORDER BY combined_probability DESC 
                LIMIT 10
            """, (next_issue,))
            
            predictions = cursor.fetchall()
            print(f"\n📋 预测数据质量检查:")
            
            valid_predictions = 0
            for i, pred in enumerate(predictions, 1):
                h, t, u, prob, conf, created = pred
                
                # 验证号码范围
                if 0 <= h <= 9 and 0 <= t <= 9 and 0 <= u <= 9:
                    valid_predictions += 1
                    
                # 验证概率范围
                prob_valid = 0 <= prob <= 100
                
                print(f"  {i:2d}. {h}{t}{u} | 概率:{prob:5.1f}% | 置信度:{conf:4s} | 创建:{created}")
                
                if not prob_valid:
                    print(f"      ⚠️ 概率值异常: {prob}")
            
            print(f"\n✅ 有效预测数量: {valid_predictions}/{len(predictions)}")
            
            # 5. 验证预测是否基于足够的历史数据
            if historical_count >= 100:
                print(f"✅ 历史数据充足: {historical_count:,}条 (≥100条)")
            else:
                print(f"⚠️ 历史数据不足: {historical_count}条 (<100条)")
            
            # 6. 验证预测生成时间
            cursor.execute("""
                SELECT MIN(created_at), MAX(created_at)
                FROM final_predictions 
                WHERE issue = ?
            """, (next_issue,))
            
            time_range = cursor.fetchone()
            if time_range[0]:
                print(f"📅 预测生成时间: {time_range[0]} ~ {time_range[1]}")
            
            # 7. 验证预测多样性
            cursor.execute("""
                SELECT COUNT(DISTINCT hundreds), COUNT(DISTINCT tens), COUNT(DISTINCT units)
                FROM final_predictions 
                WHERE issue = ?
            """, (next_issue,))
            
            diversity = cursor.fetchone()
            print(f"🎲 预测多样性: 百位{diversity[0]}种, 十位{diversity[1]}种, 个位{diversity[2]}种")
            
            conn.close()
            
            # 8. 综合评估
            print(f"\n🎯 真实性评估:")
            
            # 基于历史数据充足性
            data_sufficient = historical_count >= 100
            print(f"  历史数据充足性: {'✅ 通过' if data_sufficient else '❌ 不足'}")
            
            # 基于预测数据质量
            quality_good = valid_predictions >= len(predictions) * 0.8
            print(f"  预测数据质量: {'✅ 良好' if quality_good else '❌ 异常'}")
            
            # 基于预测多样性
            diversity_good = diversity[0] >= 3 and diversity[1] >= 3 and diversity[2] >= 3
            print(f"  预测多样性: {'✅ 充分' if diversity_good else '⚠️ 不足'}")
            
            # 综合结论
            overall_valid = data_sufficient and quality_good and diversity_good
            print(f"\n🏆 综合结论: {'✅ 预测基于真实历史数据' if overall_valid else '⚠️ 需要进一步验证'}")
            
            return overall_valid
            
        else:
            print("❌ 无预测数据")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def check_prediction_generation_method():
    """检查预测生成方法"""
    print(f"\n🔧 检查预测生成方法")
    print("=" * 40)
    
    # 检查预测生成脚本
    scripts = [
        "scripts/real_prediction_generator.py",
        "scripts/generate_predictions_2025214.py",
        "src/fusion/fusion_predictor.py"
    ]
    
    for script in scripts:
        if os.path.exists(script):
            print(f"✅ 发现预测脚本: {script}")
            
            # 检查脚本内容是否包含真实数据验证
            try:
                with open(script, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                if 'lottery_data' in content:
                    print(f"  ✅ 脚本使用真实开奖数据表")
                else:
                    print(f"  ⚠️ 脚本可能不使用真实数据")
                    
                if 'validate' in content.lower() or 'verify' in content.lower():
                    print(f"  ✅ 脚本包含数据验证逻辑")
                else:
                    print(f"  ⚠️ 脚本缺少数据验证")
                    
            except Exception as e:
                print(f"  ❌ 无法读取脚本: {e}")
        else:
            print(f"⚠️ 脚本不存在: {script}")

def verify_no_virtual_data():
    """验证无虚拟数据污染"""
    print(f"\n🚫 验证无虚拟数据污染")
    print("=" * 40)
    
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 检查是否有明显的虚拟数据特征
        cursor.execute("""
            SELECT issue, hundreds, tens, units, draw_date
            FROM lottery_data 
            ORDER BY issue DESC 
            LIMIT 10
        """)
        
        recent_data = cursor.fetchall()
        print("📊 最近10期开奖数据:")
        
        virtual_indicators = 0
        for i, data in enumerate(recent_data, 1):
            issue, h, t, u, date = data
            print(f"  {i:2d}. {issue} | {h}{t}{u} | {date}")
            
            # 检查虚拟数据指标
            if date == '2024-01-01' or date == '1970-01-01':
                virtual_indicators += 1
                print(f"      ⚠️ 可疑日期: {date}")
            
            if h == t == u:
                virtual_indicators += 1
                print(f"      ⚠️ 可疑号码: {h}{t}{u} (三同号)")
        
        conn.close()
        
        if virtual_indicators == 0:
            print("✅ 未发现虚拟数据特征")
            return True
        else:
            print(f"⚠️ 发现 {virtual_indicators} 个可疑指标")
            return False
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 预测数据真实性验证工具")
    print("=" * 50)
    
    try:
        # 1. 验证预测数据来源
        data_valid = validate_prediction_data_source()
        
        # 2. 检查预测生成方法
        check_prediction_generation_method()
        
        # 3. 验证无虚拟数据
        no_virtual = verify_no_virtual_data()
        
        # 4. 综合结论
        print(f"\n🎉 验证结果总结:")
        print(f"  预测数据真实性: {'✅ 通过' if data_valid else '❌ 失败'}")
        print(f"  无虚拟数据污染: {'✅ 通过' if no_virtual else '❌ 失败'}")
        
        overall_success = data_valid and no_virtual
        print(f"\n🏆 最终结论: {'✅ 预测数据基于真实历史数据' if overall_success else '⚠️ 需要重新生成预测'}")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
