import { CreateTransformer } from '../type';
/**
 * Add custom functional transformation for current vector.
 * @param params [callback]
 * @param x x of the the bounding box of coordinate
 * @param y y of the the bounding box of coordinate
 * @param width width of the the bounding box of coordinate
 * @param height height of the the bounding box of coordinate
 * @returns transformer
 */
export declare const custom: CreateTransformer;
