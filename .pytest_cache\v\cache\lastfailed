{"tests/test_cache_optimizer.py": true, "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_empty_results": true, "tests/test_review_engine.py::TestReviewEngine::test_analyze_prediction_quality_with_data": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_direct_hit": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_empty_input": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_group_hit": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_invalid_format": true, "tests/test_review_engine.py::TestReviewEngine::test_compare_predictions_position_hits": true, "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary": true, "tests/test_review_engine.py::TestReviewEngine::test_generate_review_summary_empty_data": true, "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions": true, "tests/test_review_engine.py::TestReviewEngine::test_get_recent_predictions_nonexistent_issue": true, "tests/test_review_engine.py::TestReviewEngine::test_lottery_query_integration": true, "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_position_accuracy_all_positions": true, "tests/test_accuracy_calculator.py::TestAccuracyCalculator::test_span_accuracy_zero_span": true, "tests/test_api.py::TestPredictionAPI::test_get_accuracy_trends": true, "tests/test_api.py::TestPredictionAPI::test_get_performance_comparison": true, "tests/test_api.py::TestErrorHandling::test_invalid_task_type": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_invalid_format": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_network_error": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_fetch_from_source_success": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_format_result": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_connection": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_latest_result_from_database": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_result_by_issue_from_database": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_get_result_by_issue_nonexistent": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_save_to_database": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_save_to_database_duplicate": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_date": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_issue": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_invalid_numbers": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_missing_fields": true, "tests/test_lottery_query.py::TestLotteryQueryEngine::test_validate_result_data_valid": true, "tests/test_lottery_query.py::TestLotteryQueryEngineIntegration::test_full_query_workflow_with_network_fallback": true, "tests/test_p8_integration.py::TestP8Integration::test_02_data_access_functionality": true, "tests/test_review_integration.py::TestReviewIntegration::test_accuracy_calculator_integration": true, "tests/test_review_integration.py::TestReviewIntegration::test_complete_review_workflow": true, "tests/test_review_integration.py::TestReviewIntegration::test_data_storage_and_query": true, "tests/test_review_integration.py::TestReviewIntegration::test_error_handling_and_recovery": true, "tests/test_review_integration.py::TestReviewIntegration::test_large_dataset_performance": true, "tests/test_review_integration.py::TestReviewIntegration::test_performance_benchmark": true, "tests/test_review_integration.py::TestClosedLoopSystemIntegration::test_closed_loop_review_integration": true, "tests/test_span_predictor.py::TestSpanDataAccess::test_get_data_statistics": true, "tests/test_span_predictor.py::TestSpanDataAccess::test_load_lottery_data": true, "tests/test_span_predictor.py::TestSpanDataAccess::test_save_prediction_result": true, "tests/test_span_predictor.py::TestSpanPredictor::test_build_model": true, "tests/test_span_predictor.py::TestSpanPredictor::test_calculate_constraint_consistency_score": true, "tests/test_span_predictor.py::TestSpanPredictor::test_get_available_models": true, "tests/test_span_predictor.py::TestSpanPredictor::test_get_model_info": true, "tests/test_span_predictor.py::TestSpanPredictor::test_switch_model": true, "tests/test_span_predictor.py::TestIntegration::test_full_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_complete_training_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_constraint_optimization_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_data_persistence_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_end_to_end_integration": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_model_evaluation_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_model_persistence_workflow": true, "tests/test_sum_integration.py::TestSumPredictorIntegration::test_prediction_workflow": true, "tests/test_sum_predictor.py::TestSumModels::test_xgb_sum_model_basic": true, "tests/test_sum_predictor.py::TestSumPredictor::test_build_model": true, "tests/test_sum_predictor.py::TestSumPredictor::test_get_available_models": true, "tests/test_sum_predictor.py::TestSumPredictor::test_model_switching": true, "tests/test_sum_predictor.py::TestSumPredictor::test_sum_predictor_initialization": true, "tests/test_sum_predictor.py::TestSumPredictorIntegration::test_end_to_end_workflow": true}