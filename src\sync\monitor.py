"""
健康监控器
监控数据同步系统的健康状态和性能指标
"""

import logging
import os
import psutil
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import threading
import time

from .utils import get_database_info, parse_issue_date

logger = logging.getLogger(__name__)

class HealthMonitor:
    """健康监控器"""
    
    def __init__(self, config: Dict[str, Any], sync_manager):
        self.config = config.get('monitoring', {})
        self.sync_manager = sync_manager
        
        # 监控配置
        self.enabled = self.config.get('enabled', True)
        self.data_freshness_threshold = self.config.get('data_freshness_threshold', 24)  # 小时
        self.sync_failure_threshold = self.config.get('sync_failure_threshold', 3)
        self.health_check_interval = self.config.get('health_check_interval', 30)  # 分钟
        
        # 告警配置
        self.alerts_config = self.config.get('alerts', {})
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self._stop_event = threading.Event()
        
        # 历史记录
        self.health_history = []
        self.alert_history = []
        
        logger.info("健康监控器初始化完成")
    
    def start_monitoring(self):
        """启动监控"""
        if not self.enabled:
            logger.info("健康监控已禁用")
            return
        
        if self.is_monitoring:
            logger.warning("健康监控已在运行")
            return
        
        logger.info("🔍 启动健康监控")
        
        self.is_monitoring = True
        self._stop_event.clear()
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        logger.info("健康监控启动完成")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        logger.info("🛑 停止健康监控")
        
        self.is_monitoring = False
        self._stop_event.set()
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
        
        logger.info("健康监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        logger.info("监控线程启动")
        
        while self.is_monitoring and not self._stop_event.is_set():
            try:
                # 执行健康检查
                health_report = self.check_system_health()
                
                # 记录健康状态
                self._record_health_status(health_report)
                
                # 检查告警条件
                self._check_alert_conditions(health_report)
                
                # 等待下次检查
                self._stop_event.wait(self.health_check_interval * 60)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                self._stop_event.wait(60)  # 出错时等待1分钟
        
        logger.info("监控线程结束")
    
    def check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'checks': {}
        }
        
        try:
            # 数据新鲜度检查
            freshness_check = self._check_data_freshness()
            health_report['checks']['data_freshness'] = freshness_check
            
            # 数据库一致性检查
            consistency_check = self._check_database_consistency()
            health_report['checks']['database_consistency'] = consistency_check
            
            # 同步状态检查
            sync_status_check = self._check_sync_status()
            health_report['checks']['sync_status'] = sync_status_check
            
            # 系统资源检查
            resource_check = self._check_system_resources()
            health_report['checks']['system_resources'] = resource_check
            
            # 数据源可用性检查
            data_source_check = self._check_data_source_availability()
            health_report['checks']['data_source'] = data_source_check
            
            # 计算整体状态
            health_report['overall_status'] = self._calculate_overall_status(health_report['checks'])
            
        except Exception as e:
            logger.error(f"健康检查失败: {e}")
            health_report['overall_status'] = 'error'
            health_report['error'] = str(e)
        
        return health_report
    
    def _check_data_freshness(self) -> Dict[str, Any]:
        """检查数据新鲜度"""
        try:
            # 获取主数据库最新期号
            latest_issue = self.sync_manager.database_manager.get_master_latest_issue()
            
            if not latest_issue:
                return {
                    'status': 'error',
                    'message': '无法获取最新期号',
                    'severity': 'high'
                }
            
            # 解析期号对应的日期
            issue_date = parse_issue_date(latest_issue)
            
            if not issue_date:
                return {
                    'status': 'warning',
                    'message': f'无法解析期号日期: {latest_issue}',
                    'severity': 'medium'
                }
            
            # 计算数据延迟
            now = datetime.now()
            delay_hours = (now - issue_date).total_seconds() / 3600
            
            if delay_hours > self.data_freshness_threshold:
                return {
                    'status': 'warning',
                    'message': f'数据延迟 {delay_hours:.1f} 小时',
                    'latest_issue': latest_issue,
                    'issue_date': issue_date.isoformat(),
                    'delay_hours': delay_hours,
                    'severity': 'high' if delay_hours > 48 else 'medium'
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'数据新鲜，延迟 {delay_hours:.1f} 小时',
                    'latest_issue': latest_issue,
                    'issue_date': issue_date.isoformat(),
                    'delay_hours': delay_hours,
                    'severity': 'low'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据新鲜度检查失败: {e}',
                'severity': 'high'
            }
    
    def _check_database_consistency(self) -> Dict[str, Any]:
        """检查数据库一致性"""
        try:
            consistency_report = self.sync_manager.database_manager.check_database_consistency()
            
            if consistency_report['consistent']:
                return {
                    'status': 'healthy',
                    'message': '数据库一致性正常',
                    'details': consistency_report,
                    'severity': 'low'
                }
            else:
                return {
                    'status': 'warning',
                    'message': f'数据库不一致: {len(consistency_report["issues"])} 个问题',
                    'details': consistency_report,
                    'severity': 'medium'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据库一致性检查失败: {e}',
                'severity': 'high'
            }
    
    def _check_sync_status(self) -> Dict[str, Any]:
        """检查同步状态"""
        try:
            sync_stats = self.sync_manager.sync_stats
            
            # 检查同步失败率
            total_syncs = sync_stats.get('total_syncs', 0)
            failed_syncs = sync_stats.get('failed_syncs', 0)
            
            if total_syncs == 0:
                return {
                    'status': 'warning',
                    'message': '尚未执行过同步',
                    'severity': 'medium'
                }
            
            failure_rate = failed_syncs / total_syncs
            
            if failed_syncs >= self.sync_failure_threshold:
                return {
                    'status': 'warning',
                    'message': f'同步失败次数过多: {failed_syncs}/{total_syncs}',
                    'failure_rate': failure_rate,
                    'last_error': sync_stats.get('last_error'),
                    'severity': 'high'
                }
            elif failure_rate > 0.2:  # 失败率超过20%
                return {
                    'status': 'warning',
                    'message': f'同步失败率较高: {failure_rate:.1%}',
                    'failure_rate': failure_rate,
                    'severity': 'medium'
                }
            else:
                return {
                    'status': 'healthy',
                    'message': f'同步状态正常: {failed_syncs}/{total_syncs} 失败',
                    'failure_rate': failure_rate,
                    'severity': 'low'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'同步状态检查失败: {e}',
                'severity': 'high'
            }
    
    def _check_system_resources(self) -> Dict[str, Any]:
        """检查系统资源"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('.')
            disk_percent = disk.percent
            
            # 判断资源状态
            issues = []
            severity = 'low'
            
            if cpu_percent > 80:
                issues.append(f'CPU使用率过高: {cpu_percent:.1f}%')
                severity = 'medium'
            
            if memory_percent > 85:
                issues.append(f'内存使用率过高: {memory_percent:.1f}%')
                severity = 'medium'
            
            if disk_percent > 90:
                issues.append(f'磁盘使用率过高: {disk_percent:.1f}%')
                severity = 'high'
            
            status = 'warning' if issues else 'healthy'
            message = '; '.join(issues) if issues else '系统资源正常'
            
            return {
                'status': status,
                'message': message,
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'severity': severity
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'系统资源检查失败: {e}',
                'severity': 'medium'
            }
    
    def _check_data_source_availability(self) -> Dict[str, Any]:
        """检查数据源可用性"""
        try:
            data_source_status = self.sync_manager.data_source.get_data_source_status()
            
            available_sources = sum(1 for source in data_source_status['sources_status'] if source['available'])
            total_sources = len(data_source_status['sources_status'])
            
            if available_sources == 0:
                return {
                    'status': 'error',
                    'message': '所有数据源不可用',
                    'details': data_source_status,
                    'severity': 'high'
                }
            elif available_sources < total_sources:
                return {
                    'status': 'warning',
                    'message': f'部分数据源不可用: {available_sources}/{total_sources}',
                    'details': data_source_status,
                    'severity': 'medium'
                }
            else:
                return {
                    'status': 'healthy',
                    'message': '所有数据源可用',
                    'details': data_source_status,
                    'severity': 'low'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'message': f'数据源可用性检查失败: {e}',
                'severity': 'high'
            }

    def _calculate_overall_status(self, checks: Dict[str, Any]) -> str:
        """计算整体健康状态"""
        error_count = sum(1 for check in checks.values() if check.get('status') == 'error')
        warning_count = sum(1 for check in checks.values() if check.get('status') == 'warning')

        if error_count > 0:
            return 'error'
        elif warning_count > 0:
            return 'warning'
        else:
            return 'healthy'

    def _record_health_status(self, health_report: Dict[str, Any]):
        """记录健康状态历史"""
        self.health_history.append(health_report)

        # 保留最近100条记录
        if len(self.health_history) > 100:
            self.health_history = self.health_history[-100:]

    def _check_alert_conditions(self, health_report: Dict[str, Any]):
        """检查告警条件"""
        try:
            overall_status = health_report.get('overall_status', 'unknown')

            # 系统错误告警
            if overall_status == 'error':
                self._send_alert('system_error', '系统健康检查发现错误', health_report)

            # 数据新鲜度告警
            freshness_check = health_report.get('checks', {}).get('data_freshness', {})
            if freshness_check.get('severity') == 'high':
                self._send_alert('data_freshness', freshness_check.get('message', '数据新鲜度告警'), freshness_check)

            # 同步失败告警
            sync_check = health_report.get('checks', {}).get('sync_status', {})
            if sync_check.get('severity') == 'high':
                self._send_alert('sync_failure', sync_check.get('message', '同步失败告警'), sync_check)

            # 数据库一致性告警
            consistency_check = health_report.get('checks', {}).get('database_consistency', {})
            if consistency_check.get('status') == 'warning':
                self._send_alert('database_inconsistency', consistency_check.get('message', '数据库不一致'), consistency_check)

        except Exception as e:
            logger.error(f"检查告警条件失败: {e}")

    def _send_alert(self, alert_type: str, message: str, details: Dict[str, Any]):
        """发送告警"""
        alert = {
            'type': alert_type,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat(),
            'severity': details.get('severity', 'medium')
        }

        # 记录告警历史
        self.alert_history.append(alert)

        # 保留最近50条告警记录
        if len(self.alert_history) > 50:
            self.alert_history = self.alert_history[-50:]

        # 发送告警通知
        if self.alerts_config.get('log', True):
            logger.warning(f"🚨 告警: {alert_type} - {message}")

        if self.alerts_config.get('console', True):
            print(f"🚨 [{datetime.now().strftime('%H:%M:%S')}] 告警: {message}")

        # 未来可以扩展其他告警方式：邮件、webhook等

    def get_health_summary(self) -> Dict[str, Any]:
        """获取健康状态摘要"""
        if not self.health_history:
            return {
                'status': 'unknown',
                'message': '暂无健康检查记录'
            }

        latest_report = self.health_history[-1]

        # 统计最近的健康状态
        recent_reports = self.health_history[-10:]  # 最近10次检查
        status_counts = {}
        for report in recent_reports:
            status = report.get('overall_status', 'unknown')
            status_counts[status] = status_counts.get(status, 0) + 1

        # 统计告警
        recent_alerts = [alert for alert in self.alert_history
                        if datetime.fromisoformat(alert['timestamp']) > datetime.now() - timedelta(hours=24)]

        return {
            'current_status': latest_report.get('overall_status', 'unknown'),
            'last_check_time': latest_report.get('timestamp'),
            'recent_status_distribution': status_counts,
            'recent_alerts_count': len(recent_alerts),
            'monitoring_enabled': self.enabled,
            'monitoring_running': self.is_monitoring,
            'checks_summary': {
                check_name: check_result.get('status', 'unknown')
                for check_name, check_result in latest_report.get('checks', {}).items()
            }
        }

    def get_detailed_report(self) -> Dict[str, Any]:
        """获取详细健康报告"""
        return {
            'summary': self.get_health_summary(),
            'latest_health_report': self.health_history[-1] if self.health_history else None,
            'health_history': self.health_history[-10:],  # 最近10次检查
            'recent_alerts': self.alert_history[-20:],    # 最近20条告警
            'config': {
                'enabled': self.enabled,
                'data_freshness_threshold': self.data_freshness_threshold,
                'sync_failure_threshold': self.sync_failure_threshold,
                'health_check_interval': self.health_check_interval
            }
        }
