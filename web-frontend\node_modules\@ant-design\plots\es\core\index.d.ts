export type { AreaOptions } from './plots/area';
export type { BarOptions } from './plots/bar';
export type { ColumnOptions } from './plots/column';
export type { DualAxesOptions } from './plots/dual-axes';
export type { MixOptions } from './plots/mix';
export type { FunnelOptions } from './plots/funnel';
export type { LineOptions } from './plots/line';
export type { PieOptions } from './plots/pie';
export type { ScatterOptions } from './plots/scatter';
export type { RadarOptions } from './plots/radar';
export type { StockOptions } from './plots/stock';
export type { TinyLineOptions } from './plots/tiny-line';
export type { TinyAreaOptions } from './plots/tiny-area';
export type { TinyColumnOptions } from './plots/tiny-column';
export type { TinyProgressOptions } from './plots/tiny-progress';
export type { TinyRingOptions } from './plots/tiny-ring';
export type { RoseOptions } from './plots/rose';
export type { WaterfallOptions } from './plots/waterfall';
export type { HistogramOptions } from './plots/histogram';
export type { HeatmapOptions } from './plots/heatmap';
export type { BoxOptions } from './plots/box';
export type { SankeyOptions } from './plots/sankey';
export type { BulletOptions } from './plots/bullet';
export type { GaugeOptions } from './plots/gauge';
export type { LiquidOptions } from './plots/liquid';
export type { WordCloudOptions } from './plots/wordCloud';
export type { TreemapOptions } from './plots/treemap';
export type { RadialBarOptions } from './plots/radial-bar';
export type { CirclePackingOptions } from './plots/circle-packing';
export type { ViolinOptions } from './plots/violin';
export type { BidirectionalBarOptions } from './plots/bidirectional-bar';
export type { VennOptions } from './plots/venn';
export type { SunburstOptions } from './plots/sunburst';
export * from './types';
import * as G2 from '@antv/g2';
import { Base } from './plots/base';
import { Area } from './plots/area';
import { Bar } from './plots/bar';
import { Column } from './plots/column';
import { DualAxes } from './plots/dual-axes';
import { Funnel } from './plots/funnel';
import { Line } from './plots/line';
import { Pie } from './plots/pie';
import { Scatter } from './plots/scatter';
import { Radar } from './plots/radar';
import { Stock } from './plots/stock';
import { TinyLine } from './plots/tiny-line';
import { TinyArea } from './plots/tiny-area';
import { TinyColumn } from './plots/tiny-column';
import { TinyProgress } from './plots/tiny-progress';
import { TinyRing } from './plots/tiny-ring';
import { Rose } from './plots/rose';
import { Waterfall } from './plots/waterfall';
import { Histogram } from './plots/histogram';
import { Heatmap } from './plots/heatmap';
import { Box } from './plots/box';
import { Sankey } from './plots/sankey';
import { Bullet } from './plots/bullet';
import { Gauge } from './plots/gauge';
import { Liquid } from './plots/liquid';
import { WordCloud } from './plots/wordCloud';
import { Treemap } from './plots/treemap';
import { RadialBar } from './plots/radial-bar';
import { CirclePacking } from './plots/circle-packing';
import { Violin } from './plots/violin';
import { BidirectionalBar } from './plots/bidirectional-bar';
import { Venn } from './plots/venn';
import { Sunburst } from './plots/sunburst';
export { G2 };
export declare const Plots: {
    Base: typeof Base;
    Line: typeof Line;
    Column: typeof Column;
    Pie: typeof Pie;
    Area: typeof Area;
    Bar: typeof Bar;
    DualAxes: typeof DualAxes;
    Funnel: typeof Funnel;
    Scatter: typeof Scatter;
    Radar: typeof Radar;
    Rose: typeof Rose;
    Stock: typeof Stock;
    TinyLine: typeof TinyLine;
    TinyArea: typeof TinyArea;
    TinyColumn: typeof TinyColumn;
    TinyProgress: typeof TinyProgress;
    TinyRing: typeof TinyRing;
    Waterfall: typeof Waterfall;
    Histogram: typeof Histogram;
    Heatmap: typeof Heatmap;
    Box: typeof Box;
    Sankey: typeof Sankey;
    Bullet: typeof Bullet;
    Gauge: typeof Gauge;
    Liquid: typeof Liquid;
    WordCloud: typeof WordCloud;
    Treemap: typeof Treemap;
    RadialBar: typeof RadialBar;
    CirclePacking: typeof CirclePacking;
    Violin: typeof Violin;
    BidirectionalBar: typeof BidirectionalBar;
    Venn: typeof Venn;
    Mix: typeof DualAxes;
    Sunburst: typeof Sunburst;
};
