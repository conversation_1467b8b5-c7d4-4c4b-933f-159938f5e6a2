2025-08-12 02:55:01,352 - src.sync.data_source - INFO - 数据源管理器初始化完成，主数据源: https://data.17500.cn/3d_asc.txt
2025-08-12 02:55:01,353 - src.sync.database_manager - INFO - 数据库管理器初始化完成
2025-08-12 02:55:01,353 - src.sync.database_manager - INFO - 主数据库: data/lottery.db
2025-08-12 02:55:01,353 - src.sync.database_manager - INFO - 从数据库数量: 2
2025-08-12 02:55:01,353 - src.sync.sync_manager - INFO - 同步管理器初始化完成
2025-08-12 02:55:01,357 - src.sync.scheduler - INFO - 定时任务管理器初始化完成
2025-08-12 02:55:01,357 - src.sync.scheduler - INFO - 启用状态: True
2025-08-12 02:55:01,358 - src.sync.scheduler - INFO - 每日同步时间: 21:30
2025-08-12 02:55:01,358 - src.sync.scheduler - INFO - 健康检查间隔: 6小时
2025-08-12 02:55:01,358 - src.sync.monitor - INFO - 健康监控器初始化完成
2025-08-12 02:55:01,358 - src.sync.recovery - INFO - 恢复管理器初始化完成
2025-08-12 02:55:01,359 - src.sync.sync_manager - INFO - 🚀 开始执行数据同步
2025-08-12 02:55:01,359 - src.sync.sync_manager - INFO - 📥 步骤1: 获取最新数据
2025-08-12 02:55:01,359 - src.sync.data_source - INFO - 开始获取最新 10 期数据
2025-08-12 02:55:01,359 - src.sync.data_source - INFO - 尝试数据源 1/1: https://data.17500.cn/3d_asc.txt
2025-08-12 02:55:01,359 - src.sync.data_source - INFO - 正在获取数据: https://data.17500.cn/3d_asc.txt
2025-08-12 02:55:01,707 - src.sync.data_source - INFO - 成功获取数据，内容长度: 562905 字符
2025-08-12 02:55:01,710 - src.sync.data_source - INFO - 开始解析数据，共 8370 行
2025-08-12 02:55:01,795 - src.sync.data_source - INFO - 解析完成，有效记录: 8370 条
2025-08-12 02:55:01,796 - src.sync.data_source - INFO - 成功获取 10 条最新记录
2025-08-12 02:55:01,796 - src.sync.data_source - INFO - 最新期号: 2025213, 开奖日期: 2025-08-11
2025-08-12 02:55:01,797 - src.sync.data_source - INFO - 开始验证数据一致性
2025-08-12 02:55:01,797 - src.sync.data_source - INFO - 数据一致性验证通过
2025-08-12 02:55:01,798 - src.sync.sync_manager - INFO - 成功获取 10 条数据
2025-08-12 02:55:01,798 - src.sync.sync_manager - INFO - 🔍 步骤2: 检查是否需要更新
2025-08-12 02:55:01,800 - src.sync.sync_manager - INFO - 数据源最新期号: 2025213
2025-08-12 02:55:01,800 - src.sync.sync_manager - INFO - 数据库最新期号: 2025213
2025-08-12 02:55:01,800 - src.sync.sync_manager - INFO - 数据已是最新，无需更新
2025-08-12 02:55:01,800 - src.sync.scheduler - INFO - 🕐 启动定时任务管理器
2025-08-12 02:55:01,801 - src.sync.scheduler - INFO - 已设置每日同步任务: 21:30
2025-08-12 02:55:01,801 - src.sync.scheduler - INFO - 已设置健康检查任务: 每6小时
2025-08-12 02:55:01,801 - src.sync.scheduler - INFO - 已设置每小时检查任务
2025-08-12 02:55:01,801 - src.sync.scheduler - INFO - 执行启动时数据检查
2025-08-12 02:55:01,802 - src.sync.scheduler - INFO - 🚀 执行启动时数据同步检查
2025-08-12 02:55:01,802 - src.sync.scheduler - INFO - 调度器线程启动
2025-08-12 02:55:01,802 - src.sync.scheduler - INFO - 定时任务管理器启动完成
2025-08-12 02:55:01,802 - src.sync.sync_manager - INFO - 🚀 开始执行数据同步
2025-08-12 02:55:01,803 - src.sync.sync_manager - INFO - 📥 步骤1: 获取最新数据
2025-08-12 02:55:01,803 - src.sync.data_source - INFO - 开始获取最新 10 期数据
2025-08-12 02:55:01,803 - src.sync.data_source - INFO - 尝试数据源 1/1: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:10,324 - src.sync.data_source - INFO - 数据源管理器初始化完成，主数据源: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:10,325 - src.sync.database_manager - INFO - 数据库管理器初始化完成
2025-08-12 02:56:10,325 - src.sync.database_manager - INFO - 主数据库: data/lottery.db
2025-08-12 02:56:10,325 - src.sync.database_manager - INFO - 从数据库数量: 2
2025-08-12 02:56:10,325 - src.sync.sync_manager - INFO - 同步管理器初始化完成
2025-08-12 02:56:10,329 - src.sync.scheduler - INFO - 定时任务管理器初始化完成
2025-08-12 02:56:10,330 - src.sync.scheduler - INFO - 启用状态: True
2025-08-12 02:56:10,330 - src.sync.scheduler - INFO - 每日同步时间: 21:30
2025-08-12 02:56:10,330 - src.sync.scheduler - INFO - 健康检查间隔: 6小时
2025-08-12 02:56:10,330 - src.sync.monitor - INFO - 健康监控器初始化完成
2025-08-12 02:56:10,331 - src.sync.recovery - INFO - 恢复管理器初始化完成
2025-08-12 02:56:10,331 - src.sync.sync_manager - INFO - 🚀 开始执行数据同步
2025-08-12 02:56:10,331 - src.sync.sync_manager - INFO - 📥 步骤1: 获取最新数据
2025-08-12 02:56:10,331 - src.sync.data_source - INFO - 开始获取最新 10 期数据
2025-08-12 02:56:10,332 - src.sync.data_source - INFO - 尝试数据源 1/1: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:10,332 - src.sync.data_source - INFO - 正在获取数据: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:10,686 - src.sync.data_source - INFO - 成功获取数据，内容长度: 562905 字符
2025-08-12 02:56:10,690 - src.sync.data_source - INFO - 开始解析数据，共 8370 行
2025-08-12 02:56:10,777 - src.sync.data_source - INFO - 解析完成，有效记录: 8370 条
2025-08-12 02:56:10,777 - src.sync.data_source - INFO - 成功获取 10 条最新记录
2025-08-12 02:56:10,777 - src.sync.data_source - INFO - 最新期号: 2025213, 开奖日期: 2025-08-11
2025-08-12 02:56:10,778 - src.sync.data_source - INFO - 开始验证数据一致性
2025-08-12 02:56:10,779 - src.sync.data_source - INFO - 数据一致性验证通过
2025-08-12 02:56:10,779 - src.sync.sync_manager - INFO - 成功获取 10 条数据
2025-08-12 02:56:10,779 - src.sync.sync_manager - INFO - 🔍 步骤2: 检查是否需要更新
2025-08-12 02:56:10,781 - src.sync.sync_manager - INFO - 数据源最新期号: 2025213
2025-08-12 02:56:10,781 - src.sync.sync_manager - INFO - 数据库最新期号: 2025213
2025-08-12 02:56:10,781 - src.sync.sync_manager - INFO - 数据已是最新，无需更新
2025-08-12 02:56:10,782 - src.sync.scheduler - INFO - 🕐 启动定时任务管理器
2025-08-12 02:56:10,782 - src.sync.scheduler - INFO - 已设置每日同步任务: 21:30
2025-08-12 02:56:10,782 - src.sync.scheduler - INFO - 已设置健康检查任务: 每6小时
2025-08-12 02:56:10,783 - src.sync.scheduler - INFO - 已设置每小时检查任务
2025-08-12 02:56:10,783 - src.sync.scheduler - INFO - 执行启动时数据检查
2025-08-12 02:56:10,783 - src.sync.scheduler - INFO - 🚀 执行启动时数据同步检查
2025-08-12 02:56:10,783 - src.sync.scheduler - INFO - 调度器线程启动
2025-08-12 02:56:10,783 - src.sync.scheduler - INFO - 定时任务管理器启动完成
2025-08-12 02:56:10,783 - src.sync.sync_manager - INFO - 🚀 开始执行数据同步
2025-08-12 02:56:10,784 - src.sync.monitor - INFO - 🔍 启动健康监控
2025-08-12 02:56:10,784 - src.sync.sync_manager - INFO - 📥 步骤1: 获取最新数据
2025-08-12 02:56:10,784 - src.sync.monitor - INFO - 监控线程启动
2025-08-12 02:56:10,784 - src.sync.monitor - INFO - 健康监控启动完成
2025-08-12 02:56:10,784 - src.sync.data_source - INFO - 开始获取最新 10 期数据
2025-08-12 02:56:10,785 - src.sync.data_source - INFO - 尝试数据源 1/1: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:10,785 - src.sync.data_source - INFO - 正在获取数据: https://data.17500.cn/3d_asc.txt
2025-08-12 02:56:11,132 - src.sync.data_source - INFO - 成功获取数据，内容长度: 562905 字符
2025-08-12 02:56:11,136 - src.sync.data_source - INFO - 开始解析数据，共 8370 行
2025-08-12 02:56:11,218 - src.sync.data_source - INFO - 解析完成，有效记录: 8370 条
2025-08-12 02:56:11,218 - src.sync.data_source - INFO - 成功获取 10 条最新记录
2025-08-12 02:56:11,218 - src.sync.data_source - INFO - 最新期号: 2025213, 开奖日期: 2025-08-11
2025-08-12 02:56:11,219 - src.sync.data_source - INFO - 开始验证数据一致性
2025-08-12 02:56:11,220 - src.sync.data_source - INFO - 数据一致性验证通过
2025-08-12 02:56:11,220 - src.sync.sync_manager - INFO - 成功获取 10 条数据
2025-08-12 02:56:11,220 - src.sync.sync_manager - INFO - 🔍 步骤2: 检查是否需要更新
2025-08-12 02:56:11,222 - src.sync.sync_manager - INFO - 数据源最新期号: 2025213
2025-08-12 02:56:11,223 - src.sync.sync_manager - INFO - 数据库最新期号: 2025213
2025-08-12 02:56:11,223 - src.sync.sync_manager - INFO - 数据已是最新，无需更新
2025-08-12 02:56:11,223 - src.sync.scheduler - INFO - ✅ 启动时数据检查完成
2025-08-12 02:56:12,031 - src.sync.monitor - WARNING - 🚨 告警: data_freshness - 数据延迟 266.9 小时
