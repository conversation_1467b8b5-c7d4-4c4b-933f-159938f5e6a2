"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.adjustAngle = exports.extend3D = exports.extend = exports.isMatrix = exports.compose = void 0;
var compose_1 = require("./compose");
Object.defineProperty(exports, "compose", { enumerable: true, get: function () { return compose_1.compose; } });
var isMatrix_1 = require("./isMatrix");
Object.defineProperty(exports, "isMatrix", { enumerable: true, get: function () { return isMatrix_1.isMatrix; } });
var extend_1 = require("./extend");
Object.defineProperty(exports, "extend", { enumerable: true, get: function () { return extend_1.extend; } });
Object.defineProperty(exports, "extend3D", { enumerable: true, get: function () { return extend_1.extend3D; } });
var adjustAngle_1 = require("./adjustAngle");
Object.defineProperty(exports, "adjustAngle", { enumerable: true, get: function () { return adjustAngle_1.adjustAngle; } });
//# sourceMappingURL=index.js.map