{"version": 3, "file": "helix.js", "sourceRoot": "src/", "sources": ["transforms/helix.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA,sDAAsD;AACtD,qCAAqC;AAErC,kCAAuC;AAEvC;;;;;;;;GAQG;AACI,IAAM,KAAK,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5D,IAAA,KAAA,OAAmD,MAAkB,IAAA,EAApE,UAAU,QAAA,EAAE,QAAQ,QAAA,EAAE,WAAW,QAAA,EAAE,WAAW,QAAsB,CAAC;IAE5E,2BAA2B;IAC3B,iBAAiB;IACjB,kCAAkC;IAClC,6BAA6B;IAC7B,IAAM,KAAK,GAAG,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACjD,IAAM,CAAC,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC,GAAG,KAAK,CAAC;IAC9C,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IAE5B,qBAAqB;IACrB,IAAM,IAAI,GAAG,IAAI,cAAM,CAAC;QACtB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,aAAa;KAC5D,CAAC,CAAC;IACH,IAAM,KAAK,GAAG,IAAI,cAAM,CAAC;QACvB,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;KAC9B,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IAC9B,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnC,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAEvC,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAEvB,uBAAuB;YACvB,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YACjD,IAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAEjD,8BAA8B;YAC9B,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACzB,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACzB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAChC,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAEhC,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAA,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,EAAI,CAAC,CAAA,CAAC,CAAC;YACrC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;YAC7D,IAAM,KAAK,GAAG,IAAA,mBAAW,EAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,IAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAExB,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AApDW,QAAA,KAAK,SAoDhB"}