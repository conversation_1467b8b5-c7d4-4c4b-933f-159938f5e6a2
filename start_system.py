#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D系统标准启动脚本
防止错误启动方式，确保系统正确运行
"""

import os
import sys
import subprocess
import time
import signal
import platform

class FucaiSystemStarter:
    """福彩3D系统启动器"""
    
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.system_name = "福彩3D智能预测系统"
        
    def check_environment(self):
        """检查运行环境"""
        print("🔍 检查运行环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major != 3 or python_version.minor < 11:
            print(f"❌ Python版本不符合要求: {python_version.major}.{python_version.minor}")
            print("✅ 需要Python 3.11或更高版本")
            return False
            
        # 检查项目文件
        required_files = [
            'src/web/app.py',
            'web-frontend/package.json',
            'requirements.txt',
            'README.md'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
                
        if missing_files:
            print(f"❌ 缺少必要文件: {missing_files}")
            print("✅ 请确保在福彩3D项目根目录下运行")
            return False
            
        print("✅ 环境检查通过")
        return True
        
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        print("📍 命令: python src/web/app.py")

        try:
            # 使用正确的启动方式
            self.backend_process = subprocess.Popen(
                [sys.executable, "src/web/app.py"],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )

            # 等待后端启动并收集输出
            print("⏳ 等待后端启动...")
            startup_output = []
            start_time = time.time()
            timeout = 20  # 增加超时时间到20秒

            while time.time() - start_time < timeout:
                if self.backend_process.poll() is not None:
                    # 进程已退出，收集所有输出
                    remaining_output = self.backend_process.stdout.read()
                    if remaining_output:
                        startup_output.append(remaining_output)
                    break

                # 检查是否有输出
                try:
                    line = self.backend_process.stdout.readline()
                    if line:
                        startup_output.append(line.strip())
                        # 检查成功启动的标志
                        if "Uvicorn running on" in line:
                            print("✅ 后端服务启动成功")
                            print("🌐 后端地址: http://127.0.0.1:8000")
                            print("📚 API文档: http://127.0.0.1:8000/api/docs")
                            return True
                except:
                    pass

                time.sleep(0.5)

            # 检查最终状态
            if self.backend_process.poll() is None:
                print("✅ 后端服务启动成功（超时但进程仍在运行）")
                print("🌐 后端地址: http://127.0.0.1:8000")
                print("📚 API文档: http://127.0.0.1:8000/api/docs")
                return True
            else:
                print("❌ 后端服务启动失败")
                print("📋 错误详情:")
                for line in startup_output[-10:]:  # 显示最后10行输出
                    if line.strip():
                        print(f"   {line}")

                # 提供修复建议
                print("\n🔧 可能的解决方案:")
                print("   1. 检查Python环境和依赖包是否完整")
                print("   2. 确认数据库文件是否存在: data/fucai3d.db")
                print("   3. 检查端口8000是否被占用")
                print("   4. 查看完整日志: logs/system_error.log")
                return False

        except Exception as e:
            print(f"❌ 后端启动异常: {e}")
            print("🔧 建议检查:")
            print("   1. Python环境是否正确")
            print("   2. 项目依赖是否安装完整")
            print("   3. 工作目录是否正确")
            return False
            
    def start_frontend(self):
        """启动前端服务"""
        print("🎨 启动前端服务...")
        print("📍 命令: cd web-frontend && npm run dev")
        
        try:
            # 检查npm是否可用
            result = subprocess.run(["npm", "--version"], check=True, capture_output=True, text=True)
            print(f"✅ 检测到npm版本: {result.stdout.strip()}")
            
            # 启动前端
            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd="web-frontend",
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # 等待前端启动
            print("⏳ 等待前端启动...")
            time.sleep(5)
            
            if self.frontend_process.poll() is None:
                print("✅ 前端服务启动成功")
                print("🌐 前端地址: http://127.0.0.1:3000")
                return True
            else:
                print("❌ 前端服务启动失败")
                return False
                
        except FileNotFoundError:
            print("❌ 未找到npm，请安装Node.js")
            return False
        except Exception as e:
            print(f"❌ 前端启动异常: {e}")
            return False
            
    def stop_services(self):
        """停止所有服务"""
        print("🛑 停止服务...")
        
        if self.backend_process:
            self.backend_process.terminate()
            print("✅ 后端服务已停止")
            
        if self.frontend_process:
            self.frontend_process.terminate()
            print("✅ 前端服务已停止")
            
    def start_system(self):
        """启动完整系统"""
        print(f"🎯 {self.system_name} 标准启动器")
        print("=" * 50)
        
        # 检查环境
        if not self.check_environment():
            return False
            
        # 启动后端
        if not self.start_backend():
            return False
            
        # 启动前端
        if not self.start_frontend():
            self.stop_services()
            return False
            
        print("=" * 50)
        print("🎉 系统启动完成！")
        print("🌐 前端界面: http://127.0.0.1:3000")
        print("🔧 后端API: http://127.0.0.1:8000")
        print("📚 API文档: http://127.0.0.1:8000/api/docs")
        print("=" * 50)
        print("💡 按 Ctrl+C 停止系统")
        
        try:
            # 保持运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号...")
            self.stop_services()
            print("👋 系统已停止")
            
        return True

def main():
    """主函数"""
    # 禁止错误的启动方式
    if len(sys.argv) > 1 and ('-m' in sys.argv or 'module' in ' '.join(sys.argv)):
        print("🚫 错误的启动方式！")
        print("❌ 请直接运行: python start_system.py")
        print("📖 或使用标准方式: python src/web/app.py")
        sys.exit(1)
        
    starter = FucaiSystemStarter()
    success = starter.start_system()
    
    if not success:
        print("❌ 系统启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
