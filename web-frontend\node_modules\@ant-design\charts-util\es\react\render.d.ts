import * as React from 'react';
declare let createRoot: ((container: Element | DocumentFragment) => any) | undefined;
declare const MARK = "__rc_react_root__";
type ContainerType = (Element | DocumentFragment) & {
    [MARK]?: ReturnType<NonNullable<typeof createRoot>>;
};
export declare function render(node: React.ReactElement, container: ContainerType): void;
export declare function unmount(container: ContainerType): Promise<void>;
export {};
