# API修复补丁 - 动态期号获取
# 生成时间: 2025-08-12T03:36:35.422906


def get_latest_drawn_issue(conn):
    """动态获取最新已开奖期号"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT issue, hundreds, tens, units, draw_date
        FROM lottery_data 
        ORDER BY issue DESC 
        LIMIT 1
    """)
    result = cursor.fetchone()
    
    if result:
        return {
            'issue': result[0],
            'numbers': f"{result[1]}{result[2]}{result[3]}",
            'draw_date': result[4],
            'status': 'drawn'
        }
    return None

def calculate_next_issue(current_issue: str) -> str:
    """智能计算下一期期号"""
    try:
        # 解析期号格式 YYYYNNN
        year = int(current_issue[:4])
        number = int(current_issue[4:])
        
        # 简单递增
        next_number = number + 1
        
        # 处理年份切换
        if next_number > 365:
            year += 1
            next_number = 1
            
        return f"{year}{next_number:03d}"
    except:
        return str(int(current_issue) + 1)

def get_predictions_by_issue(conn, issue: str):
    """获取指定期号的预测数据"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT hundreds, tens, units, combined_probability, confidence_level
        FROM final_predictions
        WHERE issue = ?
        ORDER BY combined_probability DESC
        LIMIT 5
    """, (issue,))
    
    results = cursor.fetchall()
    predictions_list = []
    
    for result in results:
        predictions_list.append({
            'hundreds': result[0],
            'tens': result[1], 
            'units': result[2],
            'combined_probability': result[3],
            'confidence_level': result[4] if result[4] else '未知'
        })
    
    return predictions_list

def build_dashboard_response(latest_drawn, next_issue, next_predictions):
    """构建仪表盘响应数据"""
    current_data = {
        "issue": next_issue,
        "status": "predicting" if next_predictions else "ready"
    }
    
    if next_predictions:
        current_data["predictions"] = next_predictions
    
    return {
        "status": "success",
        "data": {
            "lastDrawn": latest_drawn,
            "current": current_data,
            "updateTime": datetime.now().strftime('%H:%M:%S')
        }
    }
