#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D数据自动同步系统启动脚本

启动完整的数据同步系统，确保：
1. data/lottery.db 作为主数据库自动从数据源获取最新数据
2. 所有从数据库自动同步主数据库数据
3. 定时任务自动运行（每天21:30同步）
4. 健康监控和故障恢复

作者: Augment Code AI Assistant
创建时间: 2025-08-12
"""

import sys
import os
import time
import signal
import threading
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

try:
    from src.sync import SyncManager, ScheduleManager, HealthMonitor, RecoveryManager
    from src.sync.utils import load_config, setup_logging
except ImportError as e:
    print(f"❌ 导入同步系统模块失败: {e}")
    print("请确保在项目根目录下运行此脚本")
    sys.exit(1)

class SyncSystemStarter:
    """同步系统启动器"""
    
    def __init__(self):
        self.config_path = "src/sync/config.yaml"
        self.sync_manager = None
        self.scheduler = None
        self.monitor = None
        self.recovery_manager = None
        self.running = False
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def start(self):
        """启动同步系统"""
        print("🚀 启动福彩3D数据自动同步系统")
        print("=" * 60)
        
        try:
            # 1. 初始化同步管理器
            print("📋 初始化同步管理器...")
            self.sync_manager = SyncManager(self.config_path)
            
            # 2. 初始化定时任务管理器
            print("⏰ 初始化定时任务管理器...")
            config = load_config(self.config_path)
            self.scheduler = ScheduleManager(config, self.sync_manager.execute_sync)
            
            # 3. 初始化健康监控
            print("🔍 初始化健康监控...")
            self.monitor = HealthMonitor(config, self.sync_manager)
            
            # 4. 初始化恢复管理器
            print("🛠️ 初始化恢复管理器...")
            self.recovery_manager = RecoveryManager(config, self.sync_manager)
            
            # 5. 执行初始同步检查
            print("🔄 执行初始数据同步检查...")
            initial_result = self.sync_manager.execute_sync(force=False)
            
            if initial_result['success']:
                print("✅ 初始数据同步检查完成")
            else:
                print(f"⚠️ 初始数据同步发现问题: {initial_result.get('errors', [])}")
                print("🔧 尝试自动修复...")
                repair_result = self.sync_manager.execute_sync(force=True)
                if repair_result['success']:
                    print("✅ 自动修复成功")
                else:
                    print("❌ 自动修复失败，请检查配置")
            
            # 6. 启动定时任务
            print("🕐 启动定时任务...")
            self.scheduler.start()
            
            # 7. 启动健康监控
            print("📊 启动健康监控...")
            self.monitor.start_monitoring()
            
            # 8. 显示系统状态
            self._show_system_status()
            
            # 9. 保持运行
            self.running = True
            print("✅ 同步系统启动完成！")
            print("💡 按 Ctrl+C 停止系统")
            print("=" * 60)
            
            self._keep_running()
            
        except Exception as e:
            print(f"❌ 启动同步系统失败: {e}")
            self.stop()
            return False
        
        return True
    
    def stop(self):
        """停止同步系统"""
        if not self.running:
            return
        
        print("\n🛑 正在停止同步系统...")
        self.running = False
        
        # 停止各个组件
        if self.scheduler:
            self.scheduler.stop()
            print("⏰ 定时任务管理器已停止")

        if self.monitor:
            self.monitor.stop_monitoring()
            print("📊 健康监控已停止")
        
        print("✅ 同步系统已完全停止")
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 接收到信号 {signum}，正在优雅停止...")
        self.stop()
        sys.exit(0)
    
    def _show_system_status(self):
        """显示系统状态"""
        print("\n📊 系统状态:")
        print(f"  🗄️  主数据库: data/lottery.db")
        print(f"  🔄 从数据库: fucai3d.db, data/fucai3d.db")
        print(f"  🌐 数据源: https://data.17500.cn/3d_asc.txt")
        print(f"  ⏰ 定时同步: 每天21:30")
        print(f"  🔍 健康检查: 每6小时")
        print(f"  📝 日志文件: logs/sync_system.log")
        
        # 显示最近的同步统计
        if self.sync_manager:
            stats = self.sync_manager.sync_stats
            print(f"\n📈 同步统计:")
            print(f"  总同步次数: {stats['total_syncs']}")
            print(f"  成功次数: {stats['successful_syncs']}")
            print(f"  失败次数: {stats['failed_syncs']}")
            if stats['last_error']:
                print(f"  最后错误: {stats['last_error']}")
    
    def _keep_running(self):
        """保持系统运行"""
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass

def main():
    """主函数"""
    print("福彩3D数据自动同步系统")
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查工作目录
    if not os.path.exists("src/sync/config.yaml"):
        print("❌ 配置文件不存在，请在项目根目录下运行此脚本")
        sys.exit(1)
    
    # 检查数据目录
    if not os.path.exists("data"):
        print("📁 创建数据目录...")
        os.makedirs("data", exist_ok=True)
    
    # 检查日志目录
    if not os.path.exists("logs"):
        print("📁 创建日志目录...")
        os.makedirs("logs", exist_ok=True)
    
    # 启动同步系统
    starter = SyncSystemStarter()
    success = starter.start()
    
    if not success:
        print("❌ 同步系统启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
