# 福彩3D数据同步系统配置文件
# 以data/lottery.db为主数据库的自动同步配置

# 数据源配置
data_sources:
  primary: "https://data.17500.cn/3d_asc.txt"
  backup: []  # 备用数据源（如有）
  timeout: 30
  retry_count: 3
  retry_delay: 5  # 秒

# 数据库配置
databases:
  master:
    path: "data/lottery.db"
    description: "主数据库 - 唯一数据源"
    readonly: false
  
  slaves:
    - path: "fucai3d.db"
      description: "根目录预测数据库"
      readonly: true
    - path: "data/fucai3d.db"
      description: "智能推荐引擎数据库"
      readonly: true

# 同步配置
sync:
  # 定时同步
  schedule:
    enabled: true
    # 每天21:30执行（开奖后）
    daily_time: "21:30"
    # 启动时检查
    startup_check: true
    # 检查间隔（小时）
    health_check_interval: 6
  
  # 同步策略
  strategy:
    # 获取最新N期数据进行验证
    fetch_latest_count: 10
    # 数据验证
    validate_data: true
    # 自动生成预测
    auto_generate_predictions: true
    # 清理过期预测（天数）
    cleanup_old_predictions: 7

# 监控配置
monitoring:
  enabled: true
  # 数据新鲜度告警阈值（小时）
  data_freshness_threshold: 24
  # 同步失败告警阈值（次数）
  sync_failure_threshold: 3
  # 健康检查间隔（分钟）
  health_check_interval: 30
  
  # 告警方式
  alerts:
    log: true
    console: true
    # 未来可扩展：email, webhook等

# 日志配置
logging:
  level: "INFO"
  file: "logs/sync_system.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 恢复配置
recovery:
  # 自动恢复
  auto_recovery: true
  # 备份配置
  backup:
    enabled: true
    path: "backups/sync"
    keep_days: 30
  
  # 数据修复
  repair:
    # 数据不一致时自动修复
    auto_fix_inconsistency: true
    # 缺失数据自动补全
    auto_fill_missing: true

# 性能配置
performance:
  # 数据库连接池
  connection_pool_size: 5
  # 并发同步
  max_concurrent_syncs: 3
  # 批量操作大小
  batch_size: 100
