# P10-Web界面系统 FastAPI主应用
# 为福彩3D预测闭环系统提供现代化Web界面

# ⚠️ 启动方式检查 - 防止错误启动
import sys
import os
import threading
import atexit

def check_startup_method():
    """检查启动方式，禁止错误的启动方法"""
    # 获取启动命令信息
    startup_command = ' '.join(sys.argv)

    # 检查是否通过错误的模块方式启动（简化检测，避免uvicorn reload误判）
    if __name__ != "__main__":
        # 只检查明确的错误启动方式，排除uvicorn等正常场景
        if 'python -m src.web.app' in startup_command or 'python -m src\\web\\app' in startup_command:
            print("🚫 错误的启动方式被检测到！")
            print("❌ 禁止使用: python -m src.web.app")
            print("✅ 正确方式: python src/web/app.py")
            print("📖 请查看README.md或serena memory中的启动配置")
            print("🔧 或使用标准启动脚本: python start_system.py")
            sys.exit(1)
        # 对于uvicorn reload等正常场景，直接跳过检查
        return

    # 检查工作目录
    current_dir = os.getcwd()
    if current_dir.endswith('src/web') or current_dir.endswith('src\\web'):
        print("🚫 错误的工作目录！")
        print("❌ 禁止在src/web目录下启动")
        print("✅ 请在项目根目录下使用: python src/web/app.py")
        print("📖 请查看README.md或serena memory中的启动配置")
        sys.exit(1)

    # 检查是否在正确的项目根目录
    expected_files = ['README.md', 'requirements.txt', 'web-frontend']
    missing_files = [f for f in expected_files if not os.path.exists(f)]
    if missing_files:
        print("🚫 错误的工作目录！")
        print(f"❌ 缺少项目文件: {missing_files}")
        print("✅ 请在福彩3D项目根目录下启动")
        print("📖 请查看README.md或serena memory中的启动配置")
        sys.exit(1)

    print("启动方式检查通过")
    print("当前目录:", current_dir)
    print("使用正确的启动方式: python src/web/app.py")

# 执行启动检查
check_startup_method()

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
import time
import logging
import json

# 配置日志
logger = logging.getLogger(__name__)

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from .api_adapter import P9SystemAdapter
    from .websocket_manager import WebSocketManager
    from .routes.prediction import router as prediction_router
    from .routes.monitoring import router as monitoring_router
    from .routes.optimization import router as optimization_router
    from .routes.cache import router as cache_router
    from .routes.review import router as review_router
    from .routes.shap_routes import router as shap_router
    from .cache_manager import cache_manager, periodic_cleanup
except ImportError:
    # 绝对导入方式
    from src.web.api_adapter import P9SystemAdapter
    from src.web.websocket_manager import WebSocketManager
    from src.web.routes.prediction import router as prediction_router
    from src.web.routes.monitoring import router as monitoring_router
    from src.web.routes.optimization import router as optimization_router
    from src.web.routes.cache import router as cache_router
    from src.web.routes.review import router as review_router
    from src.web.routes.shap_routes import router as shap_router
    from src.web.cache_manager import cache_manager, periodic_cleanup

# 可选路由 - 如果存在则导入
try:
    from src.web.routes.smart_recommendation_routes import router as smart_recommendation_router
    SMART_RECOMMENDATION_AVAILABLE = True
except ImportError:
    smart_recommendation_router = None
    SMART_RECOMMENDATION_AVAILABLE = False

try:
    from src.web.routes.prediction_review_routes import router as prediction_review_router
    PREDICTION_REVIEW_AVAILABLE = True
except ImportError:
    prediction_review_router = None
    PREDICTION_REVIEW_AVAILABLE = False

# 创建FastAPI应用
app = FastAPI(
    title="福彩3D智能预测系统",
    description="基于P9闭环自动优化的Web界面",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React开发服务器
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(prediction_router)
app.include_router(monitoring_router)
app.include_router(optimization_router)
app.include_router(cache_router)
app.include_router(review_router)
app.include_router(shap_router)

# 可选路由
if SMART_RECOMMENDATION_AVAILABLE and smart_recommendation_router:
    app.include_router(smart_recommendation_router)

if PREDICTION_REVIEW_AVAILABLE and prediction_review_router:
    app.include_router(prediction_review_router)

# 初始化组件
api_adapter = None
websocket_manager = None
sync_system = None

# 数据同步系统管理
async def start_sync_system():
    """启动数据同步系统"""
    global sync_system

    try:
        print("🔄 启动数据自动同步系统...")

        # 导入同步系统模块
        from src.sync import SyncManager, ScheduleManager, HealthMonitor
        from src.sync.utils import load_config

        # 配置文件路径
        config_path = "src/sync/config.yaml"

        # 检查配置文件是否存在
        if not os.path.exists(config_path):
            print("⚠️ 同步系统配置文件不存在，跳过启动")
            return

        # 初始化同步管理器
        sync_manager = SyncManager(config_path)

        # 执行初始同步检查
        initial_result = sync_manager.execute_sync(force=False)
        if initial_result['success']:
            print("✅ 数据同步检查完成")
        else:
            print("⚠️ 数据同步发现问题，尝试修复...")
            repair_result = sync_manager.execute_sync(force=True)
            if repair_result['success']:
                print("✅ 数据同步修复成功")
            else:
                print("❌ 数据同步修复失败")

        # 初始化定时任务管理器
        config = load_config(config_path)
        scheduler = ScheduleManager(config, sync_manager.execute_sync)

        # 初始化健康监控
        monitor = HealthMonitor(config, sync_manager)

        # 启动定时任务和监控
        scheduler.start()
        monitor.start_monitoring()

        # 保存引用以便后续停止
        sync_system = {
            'sync_manager': sync_manager,
            'scheduler': scheduler,
            'monitor': monitor
        }

        print("✅ 数据自动同步系统启动完成")
        print("⏰ 定时同步: 每天21:30")
        print("🔍 健康检查: 每6小时")

    except Exception as e:
        print(f"❌ 启动数据同步系统失败: {e}")
        print("⚠️ 系统将继续运行，但数据同步功能不可用")

def stop_sync_system():
    """停止数据同步系统"""
    global sync_system

    if sync_system:
        try:
            print("🛑 停止数据同步系统...")

            if sync_system.get('scheduler'):
                sync_system['scheduler'].stop()

            if sync_system.get('monitor'):
                sync_system['monitor'].stop_monitoring()

            sync_system = None
            print("✅ 数据同步系统已停止")

        except Exception as e:
            print(f"❌ 停止数据同步系统失败: {e}")

# 注册退出处理器
atexit.register(stop_sync_system)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global api_adapter, websocket_manager
    
    # 初始化P9系统适配器
    # 使用绝对路径确保数据库连接正确
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(current_dir, "..", "..")
    db_path = os.path.join(project_root, "data", "fucai3d.db")
    api_adapter = P9SystemAdapter(db_path)
    
    # 初始化WebSocket管理器
    websocket_manager = WebSocketManager(api_adapter)
    
    # 启动后台任务
    asyncio.create_task(websocket_manager.start_background_tasks())

    # 启动缓存预热
    await cache_manager.warm_up_cache()

    # 启动定期清理任务
    asyncio.create_task(periodic_cleanup())

    # 启动数据同步系统
    await start_sync_system()

    print("🚀 福彩3D Web界面系统启动完成")
    print(f"📊 API文档: http://127.0.0.1:8000/api/docs")
    print(f"🔗 WebSocket: ws://127.0.0.1:8000/ws")

@app.get("/", response_class=HTMLResponse)
async def root():
    """根路径 - 返回简单的欢迎页面"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>福彩3D智能预测系统</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #1890ff; text-align: center; }
            .status { background: #f6ffed; border: 1px solid #b7eb8f; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .links { display: flex; gap: 20px; justify-content: center; margin-top: 30px; }
            .links a { background: #1890ff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            .links a:hover { background: #40a9ff; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 福彩3D智能预测系统</h1>
            <div class="status">
                <h3>✅ 系统状态：运行正常</h3>
                <p><strong>技术栈：</strong>FastAPI + React + TypeScript</p>
                <p><strong>端口：</strong>8000 (避免与Flask API冲突)</p>
                <p><strong>P9集成：</strong>闭环优化系统已连接</p>
            </div>
            <div class="links">
                <a href="/api/docs" target="_blank">📚 API文档</a>
                <a href="/api/health" target="_blank">🔍 健康检查</a>
                <a href="/api/status" target="_blank">📊 系统状态</a>
            </div>
        </div>
    </body>
    </html>
    """

@app.get("/api/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查P9系统连接
        if api_adapter:
            system_health = await api_adapter._check_system_health()
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "p9_system": system_health,
                "websocket_connections": len(websocket_manager.connections) if websocket_manager else 0
            }
        else:
            return {
                "status": "initializing",
                "timestamp": datetime.now().isoformat(),
                "message": "系统正在初始化"
            }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
        )

@app.get("/api/status")
async def system_status():
    """系统状态端点"""
    try:
        if api_adapter:
            dashboard_data = await api_adapter.get_dashboard_data()
            return {
                "status": "success",
                "data": dashboard_data
            }
        else:
            return {
                "status": "error",
                "message": "P9系统适配器未初始化"
            }
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={
                "status": "error",
                "message": str(e)
            }
        )

# WebSocket端点
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    if websocket_manager:
        await websocket_manager.connect(websocket)
        try:
            while True:
                # 使用超时机制接收客户端消息，避免无限等待
                try:
                    data = await asyncio.wait_for(websocket.receive_text(), timeout=60.0)
                    # 处理客户端发送的消息
                    if data == "ping":
                        await websocket.send_text("pong")
                    elif data == "heartbeat":
                        await websocket.send_text("heartbeat_ack")
                    else:
                        # 处理其他消息类型
                        logger.info(f"收到WebSocket消息: {data}")
                except asyncio.TimeoutError:
                    # 发送心跳保持连接活跃
                    try:
                        await websocket.send_text(json.dumps({
                            "type": "heartbeat",
                            "timestamp": datetime.now().isoformat()
                        }))
                    except Exception as e:
                        logger.error(f"发送心跳失败: {e}")
                        break
                except Exception as e:
                    logger.error(f"WebSocket消息处理错误: {e}")
                    break
        except WebSocketDisconnect:
            websocket_manager.disconnect(websocket)
        except Exception as e:
            logger.error(f"WebSocket连接错误: {e}")
            websocket_manager.disconnect(websocket)
    else:
        await websocket.close(code=1000, reason="WebSocket管理器未初始化")

# 健康检查端点
startup_time = time.time()

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        db_status = "healthy"
        if not os.path.exists("data/fucai3d.db"):
            db_status = "missing"

        # 检查缓存状态
        cache_stats = cache_manager.get_cache_stats()
        cache_status = "healthy" if cache_stats["size"] >= 0 else "error"

        # 检查WebSocket管理器
        ws_status = "healthy"  # 简化检查

        # 整体健康状态
        overall_status = "healthy"
        if db_status != "healthy" or cache_status != "healthy" or ws_status != "healthy":
            overall_status = "degraded"

        return {
            "status": overall_status,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "components": {
                "database": db_status,
                "cache": cache_status,
                "websocket": ws_status
            },
            "uptime": time.time() - startup_time,
            "cache_stats": cache_stats
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

@app.get("/ready")
async def readiness_check():
    """就绪检查端点"""
    try:
        # 检查关键组件是否就绪
        checks = {
            "api": True,
            "cache": cache_manager.get_cache_stats()["size"] >= 0,
            "websocket": True  # 简化检查
        }

        all_ready = all(checks.values())

        return {
            "ready": all_ready,
            "timestamp": datetime.now().isoformat(),
            "checks": checks
        }
    except Exception as e:
        return {
            "ready": False,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }

if __name__ == "__main__":
    # 开发模式启动
    uvicorn.run(
        "app:app",
        host="127.0.0.1",
        port=8000,
        reload=True,
        log_level="info"
    )
