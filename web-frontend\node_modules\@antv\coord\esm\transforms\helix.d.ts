import { CreateTransformer } from '../type';
/**
 * Maps normalized value to normalized helix coordinate at the center of the bounding box.
 * @param params [x0, x1, y0, y1]
 * @param x x of the the bounding box of coordinate
 * @param y y of the the bounding box of coordinate
 * @param width width of the the bounding box of coordinate
 * @param height height of the the bounding box of coordinate
 * @returns transformer
 */
export declare const helix: CreateTransformer;
