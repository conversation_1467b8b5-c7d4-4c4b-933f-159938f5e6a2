#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库最新期号验证脚本

验证数据库中的最新期号和数据真实性，为执行模式提供数据基础确认。

执行ID: execute_001
创建时间: 2025-08-12 03:30:00
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path

def verify_database_status():
    """验证数据库最新状态"""
    print("🔍 开始验证数据库状态...")
    
    results = {
        'lottery_db': None,
        'fucai3d_db': None,
        'data_consistency': False,
        'latest_issue': None,
        'verification_time': datetime.now().isoformat()
    }
    
    # 检查主数据库 (lottery.db)
    lottery_db_path = 'data/lottery.db'
    if os.path.exists(lottery_db_path):
        try:
            conn = sqlite3.connect(lottery_db_path)
            cursor = conn.cursor()
            
            # 获取最新3条记录
            cursor.execute("""
                SELECT issue, draw_date, hundreds, tens, units 
                FROM lottery_data 
                ORDER BY issue DESC 
                LIMIT 3
            """)
            lottery_records = cursor.fetchall()
            
            # 获取总记录数
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            total_count = cursor.fetchone()[0]
            
            conn.close()
            
            results['lottery_db'] = {
                'status': 'success',
                'latest_records': lottery_records,
                'total_count': total_count,
                'latest_issue': lottery_records[0][0] if lottery_records else None
            }
            
            print(f"📊 lottery.db 状态:")
            print(f"  总记录数: {total_count:,}")
            print(f"  最新3条记录:")
            for i, record in enumerate(lottery_records, 1):
                print(f"    {i}. 期号:{record[0]}, 日期:{record[1]}, 号码:{record[2]}{record[3]}{record[4]}")
                
        except Exception as e:
            results['lottery_db'] = {'status': 'error', 'message': str(e)}
            print(f"❌ lottery.db 检查失败: {e}")
    else:
        results['lottery_db'] = {'status': 'not_found'}
        print("❌ lottery.db 文件不存在")
    
    # 检查从数据库 (fucai3d.db)
    fucai3d_db_path = 'data/fucai3d.db'
    if os.path.exists(fucai3d_db_path):
        try:
            conn = sqlite3.connect(fucai3d_db_path)
            cursor = conn.cursor()
            
            # 检查lottery_data表
            cursor.execute("""
                SELECT issue, draw_date, hundreds, tens, units 
                FROM lottery_data 
                ORDER BY issue DESC 
                LIMIT 3
            """)
            fucai3d_lottery_records = cursor.fetchall()
            
            # 检查final_predictions表
            cursor.execute("""
                SELECT DISTINCT issue 
                FROM final_predictions 
                ORDER BY issue DESC 
                LIMIT 3
            """)
            prediction_issues = cursor.fetchall()
            
            conn.close()
            
            results['fucai3d_db'] = {
                'status': 'success',
                'lottery_records': fucai3d_lottery_records,
                'prediction_issues': [issue[0] for issue in prediction_issues],
                'latest_lottery_issue': fucai3d_lottery_records[0][0] if fucai3d_lottery_records else None
            }
            
            print(f"📊 fucai3d.db 状态:")
            print(f"  最新开奖记录:")
            for i, record in enumerate(fucai3d_lottery_records, 1):
                print(f"    {i}. 期号:{record[0]}, 日期:{record[1]}, 号码:{record[2]}{record[3]}{record[4]}")
            print(f"  预测期号: {prediction_issues}")
                
        except Exception as e:
            results['fucai3d_db'] = {'status': 'error', 'message': str(e)}
            print(f"❌ fucai3d.db 检查失败: {e}")
    else:
        results['fucai3d_db'] = {'status': 'not_found'}
        print("❌ fucai3d.db 文件不存在")
    
    # 数据一致性检查
    if (results['lottery_db'] and results['lottery_db']['status'] == 'success' and
        results['fucai3d_db'] and results['fucai3d_db']['status'] == 'success'):
        
        lottery_latest = results['lottery_db']['latest_issue']
        fucai3d_latest = results['fucai3d_db']['latest_lottery_issue']
        
        if lottery_latest == fucai3d_latest:
            results['data_consistency'] = True
            results['latest_issue'] = lottery_latest
            print(f"✅ 数据一致性检查通过，最新期号: {lottery_latest}")
        else:
            print(f"⚠️ 数据不一致: lottery.db={lottery_latest}, fucai3d.db={fucai3d_latest}")
    
    return results

def verify_data_authenticity(latest_issue):
    """验证数据真实性"""
    print(f"\n🔍 验证期号 {latest_issue} 的数据真实性...")
    
    try:
        conn = sqlite3.connect('data/lottery.db')
        cursor = conn.cursor()
        
        # 检查期号格式
        if len(latest_issue) == 7 and latest_issue.startswith('2025'):
            print("✅ 期号格式正确 (YYYYNNN)")
        else:
            print(f"⚠️ 期号格式异常: {latest_issue}")
        
        # 检查开奖号码合理性
        cursor.execute("""
            SELECT hundreds, tens, units, draw_date
            FROM lottery_data 
            WHERE issue = ?
        """, (latest_issue,))
        
        result = cursor.fetchone()
        if result:
            h, t, u, date = result
            
            # 验证号码范围
            if 0 <= h <= 9 and 0 <= t <= 9 and 0 <= u <= 9:
                print(f"✅ 开奖号码合理: {h}{t}{u}")
            else:
                print(f"❌ 开奖号码异常: {h}{t}{u}")
            
            # 验证日期格式
            try:
                datetime.strptime(date, '%Y-%m-%d')
                print(f"✅ 开奖日期格式正确: {date}")
            except:
                print(f"⚠️ 开奖日期格式异常: {date}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据真实性验证失败: {e}")
        return False

def check_prediction_readiness():
    """检查预测准备状态"""
    print(f"\n🔍 检查预测准备状态...")
    
    try:
        conn = sqlite3.connect('data/fucai3d.db')
        cursor = conn.cursor()
        
        # 获取最新开奖期号
        cursor.execute("SELECT MAX(issue) FROM lottery_data")
        latest_drawn = cursor.fetchone()[0]
        
        # 计算下一期期号
        next_issue = str(int(latest_drawn) + 1)
        
        # 检查是否已有预测数据
        cursor.execute("SELECT COUNT(*) FROM final_predictions WHERE issue = ?", (next_issue,))
        prediction_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 预测准备状态:")
        print(f"  最新开奖期号: {latest_drawn}")
        print(f"  下一期期号: {next_issue}")
        print(f"  预测数据条数: {prediction_count}")
        
        if prediction_count > 0:
            print(f"✅ 已有 {prediction_count} 条预测数据")
            return True, next_issue, prediction_count
        else:
            print(f"⚠️ 尚无预测数据，需要生成")
            return False, next_issue, 0
            
    except Exception as e:
        print(f"❌ 预测准备状态检查失败: {e}")
        return False, None, 0

def main():
    """主函数"""
    print("🔧 数据库最新期号验证工具")
    print("=" * 50)
    
    try:
        # 1. 验证数据库状态
        results = verify_database_status()
        
        # 2. 验证数据真实性
        if results['latest_issue']:
            verify_data_authenticity(results['latest_issue'])
        
        # 3. 检查预测准备状态
        has_predictions, next_issue, count = check_prediction_readiness()
        
        # 4. 生成验证报告
        print(f"\n📋 验证报告:")
        print(f"  验证时间: {results['verification_time']}")
        print(f"  最新期号: {results['latest_issue']}")
        print(f"  数据一致性: {'✅ 通过' if results['data_consistency'] else '❌ 失败'}")
        print(f"  预测准备: {'✅ 就绪' if has_predictions else '⚠️ 需要生成'}")
        
        if results['data_consistency'] and results['latest_issue'] >= '2025213':
            print(f"\n🎉 数据验证通过！数据库包含最新期号 {results['latest_issue']}")
            return True
        else:
            print(f"\n⚠️ 数据验证未完全通过，请检查数据状态")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
