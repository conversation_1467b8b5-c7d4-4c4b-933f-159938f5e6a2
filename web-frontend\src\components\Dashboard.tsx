import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Statistic, Table, Tag, Alert, Spin, Progress, Tooltip, Skeleton } from 'antd'
import { TrophyOutlined, RocketOutlined, CheckCircleOutlined, ClockCircleOutlined, ReloadOutlined, SyncOutlined } from '@ant-design/icons'
import { usePredictionData, useDashboardData } from '../hooks/usePredictionData'
import { useRealTimeData } from '../hooks/useRealTimeData'
import { useOptimizedPolling } from '../utils/pollingOptimizer'
import ProbabilityChart from './ProbabilityChart'
import RecommendationList from './RecommendationList'
import WebSocketStatus from './WebSocketStatus'
import axios from 'axios'

interface PredictionData {
  issue: string
  prediction_rank: number
  hundreds: number
  tens: number
  units: number
  sum_value: number
  span: number
  combined_probability: number
  confidence_level: string
  constraint_score: number
}

interface DashboardData {
  predictions: PredictionData[]
  system_status: any
  performance_metrics: any
  optimization_tasks: any[]
  update_time: string
}

const Dashboard: React.FC = () => {
  const [systemData, setSystemData] = useState<DashboardData | null>(null)
  const [systemLoading, setSystemLoading] = useState(true)
  const [systemError, setSystemError] = useState<string | null>(null)

  // 使用预测数据Hook
  const {
    predictions,
    statistics,
    loading: predictionLoading,
    error: predictionError,
    lastUpdate,
    refreshData,
    getConfidenceColor,
    getConfidenceText,
    formatProbability,
    formatPredictionNumber,
    calculateFeatures
  } = usePredictionData()

  // 使用实时数据Hook
  const {
    data: realTimeData,
    isRealTime,
    wsStatus,
    refreshData: refreshRealTimeData,
    reconnectWebSocket,
    dataFreshness
  } = useRealTimeData({
    enableWebSocket: true,
    onDataUpdate: (data) => {
      // 当实时数据更新时，同步更新本地状态
      if (data.systemStatus) {
        setSystemData(prev => prev ? { ...prev, system_status: data.systemStatus } : null)
      }
    }
  })

  // 禁用自动轮询，只在用户手动刷新时获取数据
  const refreshSystemData = async () => {
    try {
      setSystemLoading(true)
      const response = await axios.get('/api/status')
      if (response.data.status === 'success') {
        setSystemData(response.data.data)
        setSystemError(null)
      } else {
        setSystemError(response.data.message || '获取系统数据失败')
      }
    } catch (err) {
      setSystemError('连接服务器失败，请检查后端服务是否正常运行')
      console.error('获取系统数据失败:', err)
    } finally {
      setSystemLoading(false)
    }
  }

  useEffect(() => {
    // 只在初始加载时获取一次数据，不再自动轮询
    refreshSystemData()
  }, [])

  const handleRefresh = () => {
    refreshData()
    refreshRealTimeData()
    refreshSystemData()
  }



  const predictionColumns = [
    {
      title: '排名',
      dataIndex: 'prediction_rank',
      key: 'rank',
      width: 60,
      render: (rank: number) => (
        <Tag color={rank <= 3 ? 'gold' : rank <= 10 ? 'blue' : 'default'}>
          {rank}
        </Tag>
      ),
    },
    {
      title: '预测号码',
      key: 'number',
      render: (record: any) => (
        <span style={{
          fontFamily: 'monospace',
          fontSize: '16px',
          fontWeight: 'bold',
          color: record.prediction_rank <= 5 ? '#1890ff' : '#000'
        }}>
          {formatPredictionNumber(record)}
        </span>
      ),
    },
    {
      title: '和值',
      dataIndex: 'sum_value',
      key: 'sum',
      width: 60,
    },
    {
      title: '跨度',
      dataIndex: 'span',
      key: 'span',
      width: 60,
    },
    {
      title: '概率',
      dataIndex: 'combined_probability',
      key: 'probability',
      render: (prob: number) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <Progress
            percent={prob}
            size="small"
            style={{ width: 60 }}
            showInfo={false}
          />
          <span>{formatProbability(prob)}</span>
        </div>
      ),
      width: 120,
    },
    {
      title: '置信度',
      dataIndex: 'confidence_level',
      key: 'confidence',
      render: (level: string) => (
        <Tag color={getConfidenceColor(level)}>
          {getConfidenceText(level)}
        </Tag>
      ),
      width: 80,
    },
    {
      title: '约束分',
      dataIndex: 'constraint_score',
      key: 'constraint',
      render: (score: number) => (
        <Tooltip title="约束条件满足程度">
          {score ? score.toFixed(3) : '--'}
        </Tooltip>
      ),
      width: 80,
    },
  ]

  const loading = predictionLoading || systemLoading
  const error = predictionError || systemError

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载仪表板数据...</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="数据加载失败"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={handleRefresh} style={{ border: 'none', background: 'none', color: '#1890ff', cursor: 'pointer' }}>
            <ReloadOutlined /> 重试
          </button>
        }
      />
    )
  }

  const features = calculateFeatures(predictions)

  return (
    <div>
      {/* 仪表盘头部 */}
      <DashboardHeader />

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="预测数量"
              value={predictions?.length || 0}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={statistics?.basic_stats?.total_predictions ? `/ ${statistics.basic_stats.total_predictions}` : ''}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="平均概率"
              value={features?.avgProbability || '--'}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
              suffix="%"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={systemData?.system_status?.database_status === 'connected' ? '正常' : '异常'}
              prefix={<RocketOutlined />}
              valueStyle={{ color: systemData?.system_status?.database_status === 'connected' ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="最后更新"
              value={lastUpdate ? lastUpdate.toLocaleTimeString() : '--'}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <button
                onClick={handleRefresh}
                style={{
                  border: 'none',
                  background: 'none',
                  color: '#1890ff',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                <ReloadOutlined /> 刷新
              </button>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card
            title="🎯 最新预测结果"
            extra={
              features && (
                <div style={{ fontSize: '12px', color: '#666' }}>
                  平均和值: {features.avgSumValue} | 平均跨度: {features.avgSpan}
                </div>
              )
            }
          >
            <Table
              columns={predictionColumns}
              dataSource={predictions || []}
              rowKey="prediction_rank"
              pagination={{ pageSize: 10, showSizeChanger: false }}
              size="small"
              scroll={{ x: 600 }}
            />
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <RecommendationList predictions={predictions.slice(0, 5)} />
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} md={8}>
          <Card title="📊 系统状态" size="small">
            <div style={{ padding: '16px 0' }}>
              <p><strong>数据库状态:</strong>
                <Tag color={systemData?.system_status?.database_status === 'connected' ? 'green' : 'red'}>
                  {systemData?.system_status?.database_status || 'unknown'}
                </Tag>
              </p>
              <p><strong>优化运行:</strong>
                <Tag color={systemData?.system_status?.optimization_running ? 'green' : 'orange'}>
                  {systemData?.system_status?.optimization_running ? '运行中' : '停止'}
                </Tag>
              </p>
              <p><strong>系统健康:</strong>
                <Tag color={systemData?.system_status?.system_health === 'healthy' ? 'green' : 'orange'}>
                  {systemData?.system_status?.system_health || 'unknown'}
                </Tag>
              </p>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card title="🔧 活跃组件" size="small">
            <div style={{ padding: '16px 0' }}>
              {systemData?.system_status?.active_components?.length > 0 ? (
                systemData.system_status.active_components.map((component: string, index: number) => (
                  <Tag key={index} color="blue" style={{ marginBottom: 8 }}>
                    {component}
                  </Tag>
                ))
              ) : (
                <p style={{ color: '#999' }}>暂无活跃组件</p>
              )}
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <ProbabilityChart />
        </Col>
      </Row>
    </div>
  )
}

// 仪表盘头部组件
const DashboardHeader: React.FC = () => {
  const { dashboardData, loading, error, refreshDashboard } = useDashboardData()

  if (loading) return <Skeleton active paragraph={{ rows: 1 }} />
  if (error) return <Alert message={error} type="error" showIcon />
  if (!dashboardData) return null

  return (
    <div className="dashboard-header" style={{ marginBottom: 16 }}>
      <Card size="small" style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'white' }}>
          <div className="period-status" style={{ display: 'flex', gap: 12, alignItems: 'center' }}>
            <Tag
              color="success"
              icon={<CheckCircleOutlined />}
              style={{ margin: 0, padding: '4px 12px', fontSize: '14px' }}
            >
              {dashboardData.lastDrawn.issue}期: {dashboardData.lastDrawn.numbers} (已开奖)
            </Tag>
            <Tag
              color="processing"
              icon={<SyncOutlined spin={dashboardData.current.status === 'predicting'} />}
              style={{ margin: 0, padding: '4px 12px', fontSize: '14px' }}
            >
              {dashboardData.current.issue}期: {
                dashboardData.current.status === 'predicting' ? '预测中' : '准备中'
              }
            </Tag>
          </div>
          <div className="update-info" style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <span style={{ fontSize: '12px', opacity: 0.9 }}>
              更新时间: {dashboardData.updateTime}
            </span>
            <Tooltip title="刷新数据">
              <ReloadOutlined
                onClick={refreshDashboard}
                style={{ cursor: 'pointer', fontSize: '14px' }}
              />
            </Tooltip>
          </div>
        </div>

        {dashboardData.current.predictions && dashboardData.current.predictions.length > 0 && (
          <div style={{ marginTop: 12, paddingTop: 12, borderTop: '1px solid rgba(255,255,255,0.2)' }}>
            <div style={{ fontSize: '12px', marginBottom: 8, opacity: 0.9 }}>
              {dashboardData.current.issue}期预测推荐:
            </div>
            <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
              {dashboardData.current.predictions.slice(0, 3).map((pred, index) => (
                <Tag
                  key={index}
                  color="gold"
                  style={{ margin: 0, fontSize: '12px' }}
                >
                  {pred.numbers} ({pred.probability}%)
                </Tag>
              ))}
            </div>
          </div>
        )}
      </Card>
    </div>
  )
}

export default Dashboard
