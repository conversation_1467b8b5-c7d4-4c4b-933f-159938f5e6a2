"""
同步系统工具函数
"""

import os
import yaml
import logging
import sqlite3
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from functools import wraps
import time
import random

def load_config(config_path: str = "src/sync/config.yaml") -> Dict[str, Any]:
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        raise Exception(f"加载配置文件失败: {e}")

def setup_logging(config: Dict[str, Any]) -> logging.Logger:
    """设置日志系统"""
    log_config = config.get('logging', {})
    
    # 确保日志目录存在
    log_file = log_config.get('file', 'logs/sync_system.log')
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, log_config.get('level', 'INFO')),
        format=log_config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger('sync_system')

def retry_on_exception(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            retries = 0
            current_delay = delay
            
            while retries < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    retries += 1
                    if retries >= max_retries:
                        raise e
                    
                    # 添加随机抖动
                    jitter = random.uniform(0.1, 0.3) * current_delay
                    sleep_time = current_delay + jitter
                    
                    logging.warning(f"函数 {func.__name__} 执行失败 (第{retries}次重试): {e}")
                    logging.info(f"等待 {sleep_time:.2f} 秒后重试...")
                    time.sleep(sleep_time)
                    
                    current_delay *= backoff
            
            return None
        return wrapper
    return decorator

def validate_lottery_data(data: Dict[str, Any]) -> bool:
    """验证彩票数据格式"""
    required_fields = ['issue', 'draw_date', 'hundreds', 'tens', 'units']
    
    # 检查必需字段
    for field in required_fields:
        if field not in data:
            return False
    
    # 验证期号格式 (YYYYDDD)
    issue = data['issue']
    if not (isinstance(issue, str) and len(issue) == 7 and issue.isdigit()):
        return False
    
    # 验证日期格式
    try:
        datetime.strptime(data['draw_date'], '%Y-%m-%d')
    except ValueError:
        return False
    
    # 验证号码范围
    for pos in ['hundreds', 'tens', 'units']:
        value = data[pos]
        if not (isinstance(value, int) and 0 <= value <= 9):
            return False
    
    return True

def calculate_data_hash(data: List[Dict[str, Any]]) -> str:
    """计算数据哈希值用于比较"""
    data_str = str(sorted(data, key=lambda x: x.get('issue', '')))
    return hashlib.md5(data_str.encode()).hexdigest()

def get_database_info(db_path: str) -> Dict[str, Any]:
    """获取数据库信息"""
    if not os.path.exists(db_path):
        return {
            'exists': False,
            'size': 0,
            'latest_issue': None,
            'record_count': 0
        }
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取最新期号
        cursor.execute("SELECT MAX(issue) FROM lottery_data")
        latest_issue = cursor.fetchone()[0]
        
        # 获取记录数
        cursor.execute("SELECT COUNT(*) FROM lottery_data")
        record_count = cursor.fetchone()[0]
        
        # 获取文件大小
        file_size = os.path.getsize(db_path)
        
        conn.close()
        
        return {
            'exists': True,
            'size': file_size,
            'latest_issue': latest_issue,
            'record_count': record_count
        }
    except Exception as e:
        return {
            'exists': True,
            'size': os.path.getsize(db_path),
            'latest_issue': None,
            'record_count': 0,
            'error': str(e)
        }

def ensure_directory(path: str) -> None:
    """确保目录存在"""
    os.makedirs(path, exist_ok=True)

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"

def is_business_day() -> bool:
    """判断是否为工作日（福彩3D开奖日）"""
    today = datetime.now()
    # 福彩3D每天都开奖
    return True

def get_next_draw_time() -> datetime:
    """获取下一次开奖时间"""
    now = datetime.now()
    # 假设每天21:00开奖
    next_draw = now.replace(hour=21, minute=0, second=0, microsecond=0)
    
    if now >= next_draw:
        # 如果已过今天的开奖时间，则为明天
        next_draw += timedelta(days=1)
    
    return next_draw

def parse_issue_date(issue: str) -> Optional[datetime]:
    """从期号解析日期"""
    try:
        if len(issue) == 7 and issue.isdigit():
            year = int(issue[:4])
            day_of_year = int(issue[4:])
            return datetime(year, 1, 1) + timedelta(days=day_of_year - 1)
    except:
        pass
    return None
