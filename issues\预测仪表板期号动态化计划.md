# 预测仪表板期号动态化执行计划

**任务名称**: 预测仪表板期号动态化与真实数据预测  
**创建日期**: 2025-08-12  
**项目**: fucai3d (福彩3D智能预测系统)  
**协议**: RIPER-5 PLAN模式  
**预计工期**: 4-6小时  

## 🎯 核心目标

### 主要目标
1. **动态期号获取**: 消除API层硬编码，实现动态从数据库获取最新期号
2. **真实数据预测**: 确保所有预测都基于真实历史开奖数据，杜绝虚拟数据
3. **自动化流程**: 建立自动化预测生成机制，当有新开奖数据时自动预测下一期
4. **数据一致性**: 确保前端显示与数据库实际数据完全一致

### 用户需求分析
- **当前问题**: 数据库已更新到2025213期，但前端仍显示2025211/2025212期
- **根本原因**: API层硬编码期号，未动态获取最新数据
- **期望效果**: 前端自动显示最新已开奖期号和待预测期号，基于真实数据预测

## 🏗️ 技术方案设计

### 方案选择：动态期号获取 + 真实数据预测
```python
# API层动态期号获取
def get_latest_drawn_issue():
    """从数据库动态获取最新已开奖期号"""
    cursor.execute("SELECT issue, hundreds, tens, units, draw_date FROM lottery_data ORDER BY issue DESC LIMIT 1")
    return cursor.fetchone()

def calculate_next_issue(current_issue: str) -> str:
    """智能计算下一期期号"""
    return str(int(current_issue) + 1)

# 真实数据预测验证
def validate_prediction_data_source():
    """验证预测数据来源的真实性"""
    # 确保预测基于lottery_data表的真实历史数据
    # 禁止使用模拟或虚拟数据
```

### 数据流设计
```
真实开奖数据源 → lottery_data表 → 动态API → 前端显示
                     ↓
                历史数据分析 → 真实预测生成 → final_predictions表
```

## 📋 详细执行计划

### 阶段1: 数据基础验证 (30分钟)

#### 任务1.1: 验证数据库最新期号
**文件路径**: `data/lottery.db`, `data/fucai3d.db`
**验证内容**:
```sql
-- 检查主数据库最新期号
SELECT issue, draw_date, hundreds, tens, units 
FROM lottery_data 
ORDER BY issue DESC LIMIT 3;

-- 检查从数据库同步状态
SELECT issue, hundreds, tens, units 
FROM lottery_data 
ORDER BY issue DESC LIMIT 3;
```
**预期结果**: 确认2025213期真实开奖数据存在

#### 任务1.2: 数据真实性验证
**文件路径**: `src/web/utils/data_validator.py`
**验证方法**: 使用现有的 `validate_real_data()` 方法
**验证标准**:
- 数据来源于真实开奖网站
- 期号连续性检查
- 开奖号码合理性验证
- 时间戳真实性检查

### 阶段2: API层重构 (1.5小时)

#### 任务2.1: 重构dashboard API
**文件路径**: `src/web/routes/prediction.py`
**修改位置**: 第286-340行 `get_dashboard_data()` 函数
**核心修改**:
```python
@router.get("/dashboard", summary="获取仪表盘数据")
@cache_response(ttl=30)
async def get_dashboard_data():
    """获取仪表盘显示数据 - 动态获取最新期号"""
    try:
        if not os.path.exists(DB_PATH):
            raise HTTPException(status_code=500, detail="数据库文件不存在")

        conn = sqlite3.connect(DB_PATH)
        
        # 动态获取最新已开奖期号
        latest_drawn = get_latest_drawn_issue(conn)
        if not latest_drawn:
            raise HTTPException(status_code=404, detail="无开奖数据")
        
        # 计算下一期期号
        next_issue = calculate_next_issue(latest_drawn['issue'])
        
        # 获取下一期预测数据
        next_predictions = get_predictions_by_issue(conn, next_issue)
        
        # 构建响应数据
        return build_dashboard_response(latest_drawn, next_issue, next_predictions)
        
    except Exception as e:
        logger.error(f"获取仪表盘数据失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
```

#### 任务2.2: 新增辅助函数
**文件路径**: 同上
**新增函数**:
- `get_latest_drawn_issue(conn)` - 获取最新开奖期号
- `calculate_next_issue(issue)` - 计算下一期期号  
- `get_predictions_by_issue(conn, issue)` - 获取指定期号预测
- `build_dashboard_response()` - 构建响应数据

### 阶段3: 预测数据生成 (1.5小时)

#### 任务3.1: 触发真实数据预测生成
**文件路径**: `scripts/real_prediction_generator.py`
**执行命令**: 
```bash
cd /d/github/fucai3d
python scripts/real_prediction_generator.py --issue 2025214 --force
```
**验证要求**:
- 预测必须基于lottery_data表的真实历史数据
- 使用融合预测器的adaptive_fusion方法
- 生成TOP 20预测结果
- 保存到final_predictions表

#### 任务3.2: 预测数据质量验证
**验证内容**:
```sql
-- 检查预测数据是否生成
SELECT COUNT(*) FROM final_predictions WHERE issue = '2025214';

-- 验证预测数据合理性
SELECT issue, hundreds, tens, units, combined_probability, confidence_level
FROM final_predictions 
WHERE issue = '2025214' 
ORDER BY combined_probability DESC 
LIMIT 10;
```

### 阶段4: 数据验证机制强化 (1小时)

#### 任务4.1: 强化数据真实性检查
**文件路径**: `src/web/utils/data_validator.py`
**增强功能**:
```python
def validate_prediction_data_source(self, issue: str) -> Dict[str, Any]:
    """验证预测数据来源的真实性"""
    # 检查预测是否基于真实历史数据
    # 验证数据链路：lottery_data → 特征工程 → 预测生成
    # 确保无虚拟数据污染
```

#### 任务4.2: API响应数据验证
**修改位置**: `get_dashboard_data()` 函数
**添加验证**:
- 期号格式验证
- 开奖号码范围检查
- 预测数据完整性验证
- 时间戳合理性检查

### 阶段5: 前端显示优化 (1小时)

#### 任务5.1: 前端数据获取优化
**文件路径**: `web-frontend/src/hooks/usePredictionData.ts`
**修改内容**: `useDashboardData` Hook
**优化点**:
- 增加数据验证逻辑
- 优化错误处理
- 添加数据新鲜度检查

#### 任务5.2: 仪表板组件更新
**文件路径**: `web-frontend/src/components/Dashboard.tsx`
**修改位置**: `DashboardHeader` 组件
**更新内容**:
- 动态期号显示
- 数据来源标识
- 更新时间显示
- 真实数据标识

### 阶段6: 自动化流程建立 (30分钟)

#### 任务6.1: 集成到同步系统
**文件路径**: `src/sync/sync_manager.py`
**修改位置**: `_generate_predictions_for_new_data()` 方法
**集成内容**:
- 检测到新开奖数据时自动触发预测生成
- 使用real_prediction_generator生成真实预测
- 更新API缓存

## 🎯 预期成果

### 功能成果
1. ✅ API动态获取最新期号，消除硬编码
2. ✅ 前端显示与数据库完全一致
3. ✅ 所有预测基于真实历史数据
4. ✅ 自动化预测生成流程

### 技术成果
1. 🔧 重构的动态API接口
2. 🔧 强化的数据验证机制
3. 🔧 自动化的预测生成流程
4. 🔧 完整的真实数据链路

## 📝 风险评估

### 低风险项
- API接口重构（向后兼容）
- 前端组件优化（渐进式改造）

### 中风险项
- 预测数据生成（需要验证质量）
- 数据验证机制（可能影响性能）

### 风险缓解措施
1. API采用渐进式重构，保持向后兼容
2. 预测生成前进行数据质量检查
3. 数据验证采用缓存机制，减少性能影响
4. 完整的测试验证流程

## 📋 实施清单

### 准备工作
- [ ] 确认开发环境正常
- [ ] 备份当前数据库和配置
- [ ] 验证数据源可访问性

### 执行步骤
1. [ ] 验证数据库最新期号和数据真实性
2. [ ] 重构API层动态期号获取逻辑
3. [ ] 为最新期号生成基于真实数据的预测
4. [ ] 强化数据验证机制
5. [ ] 优化前端期号显示
6. [ ] 建立自动化预测流程
7. [ ] 全面功能测试验证
8. [ ] 数据一致性验证

### 验收标准
- [ ] 前端显示最新期号（2025213期已开奖，2025214期待预测）
- [ ] 所有预测数据基于真实历史开奖数据
- [ ] API响应动态获取，无硬编码
- [ ] 数据验证机制正常工作
- [ ] 自动化流程运行正常
- [ ] 无功能回归问题

## 🔧 详细实施步骤

### 步骤1: 数据库验证脚本
```python
# 创建验证脚本: scripts/verify_latest_data.py
import sqlite3
from datetime import datetime

def verify_database_status():
    """验证数据库最新状态"""
    # 检查主数据库
    conn = sqlite3.connect('data/lottery.db')
    cursor = conn.cursor()

    cursor.execute("SELECT issue, draw_date, hundreds, tens, units FROM lottery_data ORDER BY issue DESC LIMIT 1")
    latest = cursor.fetchone()

    print(f"最新期号: {latest[0]}")
    print(f"开奖日期: {latest[1]}")
    print(f"开奖号码: {latest[2]}{latest[3]}{latest[4]}")

    # 验证数据真实性
    if latest[0] >= '2025213':
        print("✅ 数据库包含最新期号")
        return True
    else:
        print("❌ 数据库期号过旧")
        return False
```

### 步骤2: API重构代码模板
```python
# 修改 src/web/routes/prediction.py
def get_latest_drawn_issue(conn):
    """动态获取最新已开奖期号"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT issue, hundreds, tens, units, draw_date
        FROM lottery_data
        ORDER BY issue DESC
        LIMIT 1
    """)
    result = cursor.fetchone()

    if result:
        return {
            'issue': result[0],
            'numbers': f"{result[1]}{result[2]}{result[3]}",
            'draw_date': result[4],
            'status': 'drawn'
        }
    return None

def calculate_next_issue(current_issue: str) -> str:
    """智能计算下一期期号"""
    try:
        # 解析期号格式 YYYYNNN
        year = int(current_issue[:4])
        number = int(current_issue[4:])

        # 简单递增
        next_number = number + 1

        # 处理年份切换
        if next_number > 365:
            year += 1
            next_number = 1

        return f"{year}{next_number:03d}"
    except:
        return str(int(current_issue) + 1)
```

### 步骤3: 预测生成验证
```bash
# 执行预测生成
cd /d/github/fucai3d
python scripts/real_prediction_generator.py

# 验证预测结果
python -c "
import sqlite3
conn = sqlite3.connect('data/fucai3d.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM final_predictions WHERE issue = (SELECT MAX(issue) FROM lottery_data) + 1')
count = cursor.fetchone()[0]
print(f'预测数据条数: {count}')
conn.close()
"
```

### 步骤4: 数据验证强化
```python
# 在 src/web/utils/data_validator.py 中添加
def validate_prediction_authenticity(self, issue: str) -> bool:
    """验证预测数据的真实性"""
    try:
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # 检查预测是否基于真实历史数据
        cursor.execute("""
            SELECT COUNT(*) FROM lottery_data
            WHERE issue < ?
        """, (issue,))

        historical_count = cursor.fetchone()[0]

        # 至少需要100期历史数据才能进行有效预测
        if historical_count < 100:
            return False

        # 检查预测数据的合理性
        cursor.execute("""
            SELECT hundreds, tens, units, combined_probability
            FROM final_predictions
            WHERE issue = ?
            ORDER BY combined_probability DESC
            LIMIT 10
        """, (issue,))

        predictions = cursor.fetchall()
        conn.close()

        # 验证预测数据格式和范围
        for pred in predictions:
            if not (0 <= pred[0] <= 9 and 0 <= pred[1] <= 9 and 0 <= pred[2] <= 9):
                return False
            if not (0 <= pred[3] <= 1):
                return False

        return True

    except Exception as e:
        print(f"验证失败: {e}")
        return False
```

## 🎯 关键成功指标

### 技术指标
- **API响应时间**: < 100ms
- **数据一致性**: 100%（前端与数据库完全一致）
- **预测数据质量**: 基于真实历史数据，无虚拟数据
- **自动化程度**: 新数据自动触发预测生成

### 业务指标
- **用户体验**: 期号显示实时准确
- **数据可信度**: 所有数据可追溯到真实来源
- **系统稳定性**: 无硬编码依赖，动态适应新期号

## 📊 测试验证方案

### 单元测试
```python
# 测试API动态期号获取
def test_dynamic_issue_retrieval():
    response = client.get("/api/prediction/dashboard")
    assert response.status_code == 200
    data = response.json()

    # 验证期号是动态获取的
    assert data['data']['lastDrawn']['issue'] >= '2025213'
    assert data['data']['current']['issue'] >= '2025214'

# 测试数据真实性
def test_data_authenticity():
    validator = DataValidator()
    result = validator.validate_prediction_authenticity('2025214')
    assert result == True
```

### 集成测试
```bash
# 完整流程测试
1. 验证数据库最新期号
2. 触发API调用
3. 检查前端显示
4. 验证预测数据生成
5. 确认数据一致性
```

---

**备注**: 本计划基于RIPER-5协议制定，确保技术方案的可行性和数据的真实性。所有修改都经过详细设计，确保系统稳定性和数据准确性。
