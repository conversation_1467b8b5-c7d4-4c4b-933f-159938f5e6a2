!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("@antv/g")):"function"==typeof define&&define.amd?define(["@antv/g"],e):"object"==typeof exports?exports.Component=e(require("@antv/g")):t.Component=e(t.G)}(self,t=>(()=>{var e={10:e=>{"use strict";e.exports=t},189:t=>{"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}},345:(t,e,n)=>{var i=n(670).parse;i.parseSVG=i,i.makeAbsolute=function(t){var e,n={x:0,y:0},i={x:"x0",y:"y0",x1:"x0",y1:"y0",x2:"x0",y2:"y0"};return t.forEach(function(t){for(var r in"moveto"===t.command&&(e=t),t.x0=n.x,t.y0=n.y,i)r in t&&(t[r]+=t.relative?t[i[r]]:0);"x"in t||(t.x=n.x),"y"in t||(t.y=n.y),t.relative=!1,t.code=t.code.toUpperCase(),"closepath"==t.command&&(t.x=e.x,t.y=e.y),n=t}),t},t.exports=i},365:(t,e,n)=>{"use strict";var i=n(856),r=Array.prototype.concat,s=Array.prototype.slice,a=t.exports=function(t){for(var e=[],n=0,a=t.length;n<a;n++){var o=t[n];i(o)?e=r.call(e,s.call(o)):e.push(o)}return e};a.wrap=function(t){return function(){return t(a(arguments))}}},450:(t,e,n)=>{var i=n(189),r=n(365),s=Object.hasOwnProperty,a=Object.create(null);for(var o in i)s.call(i,o)&&(a[i[o]]=o);var l=t.exports={to:{},get:{}};function h(t,e,n){return Math.min(Math.max(e,t),n)}function c(t){var e=Math.round(t).toString(16).toUpperCase();return e.length<2?"0"+e:e}l.get=function(t){var e,n;switch(t.substring(0,3).toLowerCase()){case"hsl":e=l.get.hsl(t),n="hsl";break;case"hwb":e=l.get.hwb(t),n="hwb";break;default:e=l.get.rgb(t),n="rgb"}return e?{model:n,value:e}:null},l.get.rgb=function(t){if(!t)return null;var e,n,r,a=[0,0,0,1];if(e=t.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(r=e[2],e=e[1],n=0;n<3;n++){var o=2*n;a[n]=parseInt(e.slice(o,o+2),16)}r&&(a[3]=parseInt(r,16)/255)}else if(e=t.match(/^#([a-f0-9]{3,4})$/i)){for(r=(e=e[1])[3],n=0;n<3;n++)a[n]=parseInt(e[n]+e[n],16);r&&(a[3]=parseInt(r+r,16)/255)}else if(e=t.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)){for(n=0;n<3;n++)a[n]=parseInt(e[n+1],0);e[4]&&(e[5]?a[3]=.01*parseFloat(e[4]):a[3]=parseFloat(e[4]))}else{if(!(e=t.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(e=t.match(/^(\w+)$/))?"transparent"===e[1]?[0,0,0,0]:s.call(i,e[1])?((a=i[e[1]])[3]=1,a):null:null;for(n=0;n<3;n++)a[n]=Math.round(2.55*parseFloat(e[n+1]));e[4]&&(e[5]?a[3]=.01*parseFloat(e[4]):a[3]=parseFloat(e[4]))}for(n=0;n<3;n++)a[n]=h(a[n],0,255);return a[3]=h(a[3],0,1),a},l.get.hsl=function(t){if(!t)return null;var e=t.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(e){var n=parseFloat(e[4]);return[(parseFloat(e[1])%360+360)%360,h(parseFloat(e[2]),0,100),h(parseFloat(e[3]),0,100),h(isNaN(n)?1:n,0,1)]}return null},l.get.hwb=function(t){if(!t)return null;var e=t.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/);if(e){var n=parseFloat(e[4]);return[(parseFloat(e[1])%360+360)%360,h(parseFloat(e[2]),0,100),h(parseFloat(e[3]),0,100),h(isNaN(n)?1:n,0,1)]}return null},l.to.hex=function(){var t=r(arguments);return"#"+c(t[0])+c(t[1])+c(t[2])+(t[3]<1?c(Math.round(255*t[3])):"")},l.to.rgb=function(){var t=r(arguments);return t.length<4||1===t[3]?"rgb("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+")":"rgba("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+", "+t[3]+")"},l.to.rgb.percent=function(){var t=r(arguments),e=Math.round(t[0]/255*100),n=Math.round(t[1]/255*100),i=Math.round(t[2]/255*100);return t.length<4||1===t[3]?"rgb("+e+"%, "+n+"%, "+i+"%)":"rgba("+e+"%, "+n+"%, "+i+"%, "+t[3]+")"},l.to.hsl=function(){var t=r(arguments);return t.length<4||1===t[3]?"hsl("+t[0]+", "+t[1]+"%, "+t[2]+"%)":"hsla("+t[0]+", "+t[1]+"%, "+t[2]+"%, "+t[3]+")"},l.to.hwb=function(){var t=r(arguments),e="";return t.length>=4&&1!==t[3]&&(e=", "+t[3]),"hwb("+t[0]+", "+t[1]+"%, "+t[2]+"%"+e+")"},l.to.keyword=function(t){return a[t.slice(0,3)]}},670:t=>{"use strict";function e(t,n,i,r){this.message=t,this.expected=n,this.found=i,this.location=r,this.name="SyntaxError","function"==typeof Error.captureStackTrace&&Error.captureStackTrace(this,e)}!function(t,e){function n(){this.constructor=t}n.prototype=e.prototype,t.prototype=new n}(e,Error),e.buildMessage=function(t,e){var n={literal:function(t){return'"'+r(t.text)+'"'},class:function(t){var e,n="";for(e=0;e<t.parts.length;e++)n+=t.parts[e]instanceof Array?s(t.parts[e][0])+"-"+s(t.parts[e][1]):s(t.parts[e]);return"["+(t.inverted?"^":"")+n+"]"},any:function(t){return"any character"},end:function(t){return"end of input"},other:function(t){return t.description}};function i(t){return t.charCodeAt(0).toString(16).toUpperCase()}function r(t){return t.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(t){return"\\x0"+i(t)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(t){return"\\x"+i(t)})}function s(t){return t.replace(/\\/g,"\\\\").replace(/\]/g,"\\]").replace(/\^/g,"\\^").replace(/-/g,"\\-").replace(/\0/g,"\\0").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/[\x00-\x0F]/g,function(t){return"\\x0"+i(t)}).replace(/[\x10-\x1F\x7F-\x9F]/g,function(t){return"\\x"+i(t)})}function a(t){return n[t.type](t)}return"Expected "+function(t){var e,n,i=new Array(t.length);for(e=0;e<t.length;e++)i[e]=a(t[e]);if(i.sort(),i.length>0){for(e=1,n=1;e<i.length;e++)i[e-1]!==i[e]&&(i[n]=i[e],n++);i.length=n}switch(i.length){case 1:return i[0];case 2:return i[0]+" or "+i[1];default:return i.slice(0,-1).join(", ")+", or "+i[i.length-1]}}(t)+" but "+function(t){return t?'"'+r(t)+'"':"end of input"}(e)+" found."},t.exports={SyntaxError:e,parse:function(t,n){n=void 0!==n?n:{};var i,r={},s={svg_path:ft},a=ft,o=function(t){if(!t)return[];for(var e=[],n=0;n<t.length;n++)e=e.concat.apply(e,t[n]);var i=e[0];return i&&"m"==i.code&&(delete i.relative,i.code="M"),e},l=function(t,e){return function(t,e){if(!e)return[t];for(var n=[t],i=0,r=e.length;i<r;i++)n[i+1]=e[i][1];return n}(t,e)},h=/^[Mm]/,c=ut(["M","m"],!1,!1),u=function(t,e,n){var i=Ht(t,[e]);return n&&(i=i.concat(Ht("M"==t?"L":"l",n[1]))),i},d=/^[Zz]/,p=ut(["Z","z"],!1,!1),g=function(){return Ht("Z")},f=/^[Ll]/,m=ut(["L","l"],!1,!1),y=function(t,e){return Ht(t,e)},b=/^[Hh]/,x=ut(["H","h"],!1,!1),v=function(t,e){return Ht(t,e.map(function(t){return{x:t}}))},w=/^[Vv]/,k=ut(["V","v"],!1,!1),S=function(t,e){return Ht(t,e.map(function(t){return{y:t}}))},A=/^[Cc]/,L=ut(["C","c"],!1,!1),B=function(t,e,n){return{x1:t.x,y1:t.y,x2:e.x,y2:e.y,x:n.x,y:n.y}},C=/^[Ss]/,M=ut(["S","s"],!1,!1),E=function(t,e){return{x2:t.x,y2:t.y,x:e.x,y:e.y}},P=/^[Qq]/,$=ut(["Q","q"],!1,!1),T=function(t,e){return{x1:t.x,y1:t.y,x:e.x,y:e.y}},O=/^[Tt]/,N=ut(["T","t"],!1,!1),z=/^[Aa]/,_=ut(["A","a"],!1,!1),I=function(t,e,n,i,r,s){return{rx:t,ry:e,xAxisRotation:n,largeArc:i,sweep:r,x:s.x,y:s.y}},F=function(t,e){return{x:t,y:e}},G=function(t){return 1*t},H=function(t){return 1*t.join("")},V=/^[01]/,W=ut(["0","1"],!1,!1),D=function(t){return"1"==t},j=function(){return""},R=",",Y=ct(",",!1),q=function(t){return t.join("")},K=".",Z=ct(".",!1),U=/^[eE]/,X=ut(["e","E"],!1,!1),J=/^[+\-]/,Q=ut(["+","-"],!1,!1),tt=/^[0-9]/,et=ut([["0","9"]],!1,!1),nt=function(t){return t.join("")},it=/^[ \t\n\r]/,rt=ut([" ","\t","\n","\r"],!1,!1),st=0,at=[{line:1,column:1}],ot=0,lt=[],ht=0;if("startRule"in n){if(!(n.startRule in s))throw new Error("Can't start parsing from rule \""+n.startRule+'".');a=s[n.startRule]}function ct(t,e){return{type:"literal",text:t,ignoreCase:e}}function ut(t,e,n){return{type:"class",parts:t,inverted:e,ignoreCase:n}}function dt(e){var n,i=at[e];if(i)return i;for(n=e-1;!at[n];)n--;for(i={line:(i=at[n]).line,column:i.column};n<e;)10===t.charCodeAt(n)?(i.line++,i.column=1):i.column++,n++;return at[e]=i,i}function pt(t,e){var n=dt(t),i=dt(e);return{start:{offset:t,line:n.line,column:n.column},end:{offset:e,line:i.line,column:i.column}}}function gt(t){st<ot||(st>ot&&(ot=st,lt=[]),lt.push(t))}function ft(){var t,e,n,i,s;for(t=st,e=[],n=Nt();n!==r;)e.push(n),n=Nt();if(e!==r)if(n=function(){var t,e,n,i,s,a;if(t=st,(e=mt())!==r){for(n=[],i=st,s=[],a=Nt();a!==r;)s.push(a),a=Nt();for(s!==r&&(a=mt())!==r?i=s=[s,a]:(st=i,i=r);i!==r;){for(n.push(i),i=st,s=[],a=Nt();a!==r;)s.push(a),a=Nt();s!==r&&(a=mt())!==r?i=s=[s,a]:(st=i,i=r)}n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),n===r&&(n=null),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?t=e=o(n):(st=t,t=r)}else st=t,t=r;else st=t,t=r;return t}function mt(){var e,n,i,s,a,o;if(e=st,n=function(){var e,n,i,s,a,o,l;if(e=st,h.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(c)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r&&(s=At())!==r?(a=st,(o=Mt())===r&&(o=null),o!==r&&(l=bt())!==r?a=o=[o,l]:(st=a,a=r),a===r&&(a=null),a!==r?e=n=u(n,s,a):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}(),n!==r){for(i=[],s=st,a=[],o=Nt();o!==r;)a.push(o),o=Nt();for(a!==r&&(o=yt())!==r?s=a=[a,o]:(st=s,s=r);s!==r;){for(i.push(s),s=st,a=[],o=Nt();o!==r;)a.push(o),o=Nt();a!==r&&(o=yt())!==r?s=a=[a,o]:(st=s,s=r)}i!==r?e=n=l(n,i):(st=e,e=r)}else st=e,e=r;return e}function yt(){var e,n;return d.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(p)),n!==r&&(n=g()),(e=n)===r&&(e=function(){var e,n,i,s;if(e=st,f.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(m)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r&&(s=bt())!==r?e=n=y(n,s):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,b.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(x)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r&&(s=xt())!==r?e=n=v(n,s):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,w.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(k)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r&&(s=xt())!==r?e=n=S(n,s):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,A.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(L)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?(s=function(){var t,e,n,i,s,a;if(t=st,(e=vt())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=vt())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=vt())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),s!==r?e=n=y(n,s):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,C.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(M)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?(s=function(){var t,e,n,i,s,a;if(t=st,(e=wt())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=wt())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=wt())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),s!==r?e=n=y(n,s):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,P.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt($)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?(s=function(){var t,e,n,i,s,a;if(t=st,(e=kt())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=kt())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=kt())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),s!==r?e=n=y(n,s):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,O.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(N)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?(s=function(){var t,e,n,i,s,a;if(t=st,(e=At())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=At())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=At())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),s!==r?e=n=y(n,s):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}())===r&&(e=function(){var e,n,i,s;if(e=st,z.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(_)),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?(s=function(){var t,e,n,i,s,a;if(t=st,(e=St())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=St())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=St())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}(),s!==r?e=n=y(n,s):(st=e,e=r)):(st=e,e=r)}else st=e,e=r;return e}()),e}function bt(){var t,e,n,i,s,a;if(t=st,(e=At())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=At())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=At())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}function xt(){var t,e,n,i,s,a;if(t=st,(e=Bt())!==r){for(n=[],i=st,(s=Mt())===r&&(s=null),s!==r&&(a=Bt())!==r?i=s=[s,a]:(st=i,i=r);i!==r;)n.push(i),i=st,(s=Mt())===r&&(s=null),s!==r&&(a=Bt())!==r?i=s=[s,a]:(st=i,i=r);n!==r?t=e=l(e,n):(st=t,t=r)}else st=t,t=r;return t}function vt(){var t,e,n,i,s,a;return t=st,(e=At())!==r?((n=Mt())===r&&(n=null),n!==r&&(i=At())!==r?((s=Mt())===r&&(s=null),s!==r&&(a=At())!==r?t=e=B(e,i,a):(st=t,t=r)):(st=t,t=r)):(st=t,t=r),t}function wt(){var t,e,n,i;return t=st,(e=At())!==r?((n=Mt())===r&&(n=null),n!==r&&(i=At())!==r?t=e=E(e,i):(st=t,t=r)):(st=t,t=r),t}function kt(){var t,e,n,i;return t=st,(e=At())!==r?((n=Mt())===r&&(n=null),n!==r&&(i=At())!==r?t=e=T(e,i):(st=t,t=r)):(st=t,t=r),t}function St(){var t,e,n,i,s,a,o,l,h,c,u;return t=st,(e=Lt())!==r?((n=Mt())===r&&(n=null),n!==r&&(i=Lt())!==r?((s=Mt())===r&&(s=null),s!==r&&(a=Bt())!==r&&Mt()!==r&&(o=Ct())!==r?((l=Mt())===r&&(l=null),l!==r&&(h=Ct())!==r?((c=Mt())===r&&(c=null),c!==r&&(u=At())!==r?t=e=I(e,i,a,o,h,u):(st=t,t=r)):(st=t,t=r)):(st=t,t=r)):(st=t,t=r)):(st=t,t=r),t}function At(){var t,e,n,i;return t=st,(e=Bt())!==r?((n=Mt())===r&&(n=null),n!==r&&(i=Bt())!==r?t=e=F(e,i):(st=t,t=r)):(st=t,t=r),t}function Lt(){var t;return(t=Pt())===r&&(t=Ot()),t!==r&&(t=G(t)),t}function Bt(){var t,e,n;return t=st,(e=Tt())===r&&(e=null),e!==r&&(n=Pt())!==r?t=e=[e,n]:(st=t,t=r),t===r&&(t=st,(e=Tt())===r&&(e=null),e!==r&&(n=Ot())!==r?t=e=[e,n]:(st=t,t=r)),t!==r&&(t=H(t)),t}function Ct(){var e;return V.test(t.charAt(st))?(e=t.charAt(st),st++):(e=r,0===ht&&gt(W)),e!==r&&(e=D(e)),e}function Mt(){var t,e,n,i,s;if(t=st,e=[],(n=Nt())!==r)for(;n!==r;)e.push(n),n=Nt();else e=r;if(e!==r)if((n=Et())===r&&(n=null),n!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?t=e=[e,n,i]:(st=t,t=r)}else st=t,t=r;else st=t,t=r;if(t===r){if(t=st,e=st,(n=Et())!==r){for(i=[],s=Nt();s!==r;)i.push(s),s=Nt();i!==r?e=n=[n,i]:(st=e,e=r)}else st=e,e=r;e!==r&&(e=j()),t=e}return t}function Et(){var e;return 44===t.charCodeAt(st)?(e=R,st++):(e=r,0===ht&&gt(Y)),e}function Pt(){var e,n,i;return e=st,n=function(){var e,n,i,s;return e=st,(n=Ot())===r&&(n=null),n!==r?(46===t.charCodeAt(st)?(i=K,st++):(i=r,0===ht&&gt(Z)),i!==r&&(s=Ot())!==r?e=n=[n,i,s]:(st=e,e=r)):(st=e,e=r),e===r&&(e=st,(n=Ot())!==r?(46===t.charCodeAt(st)?(i=K,st++):(i=r,0===ht&&gt(Z)),i!==r?e=n=[n,i]:(st=e,e=r)):(st=e,e=r)),e!==r&&(e=q(e)),e}(),n!==r?((i=$t())===r&&(i=null),i!==r?e=n=[n,i]:(st=e,e=r)):(st=e,e=r),e===r&&(e=st,(n=Ot())!==r&&(i=$t())!==r?e=n=[n,i]:(st=e,e=r)),e!==r&&(e=q(e)),e}function $t(){var e,n,i,s;return e=st,U.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(X)),n!==r?((i=Tt())===r&&(i=null),i!==r&&(s=Ot())!==r?e=n=[n,i,s]:(st=e,e=r)):(st=e,e=r),e!==r&&(e=q(e)),e}function Tt(){var e;return J.test(t.charAt(st))?(e=t.charAt(st),st++):(e=r,0===ht&&gt(Q)),e}function Ot(){var e,n;if(e=[],tt.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(et)),n!==r)for(;n!==r;)e.push(n),tt.test(t.charAt(st))?(n=t.charAt(st),st++):(n=r,0===ht&&gt(et));else e=r;return e!==r&&(e=nt(e)),e}function Nt(){var e;return it.test(t.charAt(st))?(e=t.charAt(st),st++):(e=r,0===ht&&gt(rt)),e!==r&&(e=j()),e}var zt,_t,It,Ft={m:"moveto",l:"lineto",h:"horizontal lineto",v:"vertical lineto",c:"curveto",s:"smooth curveto",q:"quadratic curveto",t:"smooth quadratic curveto",a:"elliptical arc",z:"closepath"};for(var Gt in Ft)Ft[Gt.toUpperCase()]=Ft[Gt];function Ht(t,e){e||(e=[{}]);for(var n=e.length;n--;){var i={code:t,command:Ft[t]};for(var r in t==t.toLowerCase()&&(i.relative=!0),e[n])i[r]=e[n][r];e[n]=i}return e}if((i=a())!==r&&st===t.length)return i;throw i!==r&&st<t.length&&gt({type:"end"}),zt=lt,_t=ot<t.length?t.charAt(ot):null,It=ot<t.length?pt(ot,ot+1):pt(ot,ot),new e(e.buildMessage(zt,_t),zt,_t,It)}}},856:t=>{t.exports=function(t){return!(!t||"string"==typeof t)&&(t instanceof Array||Array.isArray(t)||t.length>=0&&(t.splice instanceof Function||Object.getOwnPropertyDescriptor(t,t.length-1)&&"String"!==t.constructor.name))}}},n={};function i(t){var r=n[t];if(void 0!==r)return r.exports;var s=n[t]={exports:{}};return e[t](s,s.exports,i),s.exports}i.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return i.d(e,{a:e}),e},i.d=(t,e)=>{for(var n in e)i.o(e,n)&&!i.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},i.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),i.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var r={};return(()=>{"use strict";function t(t){return null==t}i.r(r),i.d(r,{Axis:()=>bi,BBox:()=>ci,Breadcrumb:()=>nt,Button:()=>J,Category:()=>ir,Checkbox:()=>Er,CircleCrosshair:()=>xr,Continuous:()=>Fi,Layout:()=>Nr,LineCrosshair:()=>br,Marker:()=>D,Navigator:()=>Di,PRIMILTIVE_ATTRIBUTES:()=>Ve,PolygonCrosshair:()=>Ar,Poptip:()=>Ui,Scrollbar:()=>Oe,Select:()=>_r,Selection:()=>$,Slider:()=>Te,Sparkline:()=>he,Switch:()=>dr,Tag:()=>j,Timebar:()=>cs,Timeline:()=>xi,Tooltip:()=>hr,add:()=>Rt,addPrefix:()=>k,applyStyleSheet:()=>A,applyToText:()=>$n,arrayInterpolate:()=>Xe,calcLayout:()=>Or,catmullRom2Bezier:()=>Qt,classNames:()=>Ae,copyAttributes:()=>Un,createOffscreenGroup:()=>h,createTempText:()=>qn,deepAssign:()=>m,defined:()=>jn,degToRad:()=>Ie,distance:()=>Zt,ellipsisIt:()=>Tn,flex:()=>$r,formatTime:()=>Vr,getCallbackValue:()=>Ge,getEventPos:()=>Se,getFont:()=>En,getLocalBBox:()=>Yn,getMask:()=>Hr,getPrimitiveAttributes:()=>De,getRenderBBox:()=>ui,getShapeSpace:()=>Rn,getTimeDiff:()=>Wr,getTimeScale:()=>Dr,getTimeStart:()=>jr,getTranslate:()=>Nn,grid:()=>Tr,groupBy:()=>Hi,hide:()=>a,ifNegative:()=>xe,ifPositive:()=>ve,ifShow:()=>z,inRange:()=>vn,interpolate:()=>Qe,intersection:()=>Sr,isHorizontal:()=>Kn,isInOffscreenGroup:()=>c,isPrimitiveAttribute:()=>We,isVertical:()=>Zn,keyframeInterpolate:()=>tn,lineLen:()=>vr,max:()=>Kt,maybeAppend:()=>O,measureTextWidth:()=>Ln,mid:()=>ke,min:()=>qt,multi:()=>we,normalize:()=>Ut,numberInterpolate:()=>Ue,objectInterpolate:()=>Je,omit:()=>rn,parseDate:()=>Gr,parseHTML:()=>ds,parseSeriesAttr:()=>P,path2marker:()=>fs,percentTransform:()=>zn,radToDeg:()=>Fe,removePrefix:()=>S,renderExtDo:()=>en,replaceChildren:()=>sr,rotate:()=>Xt,sampling:()=>Ne,scale:()=>jt,scaleToPixel:()=>nn,select:()=>T,setMockMeasureTextWidth:()=>An,show:()=>s,splitStyle:()=>M,stringToHTML:()=>us,sub:()=>Yt,subStyleProps:()=>B,superStyleProps:()=>C,svg2marker:()=>ms,textOf:()=>Pn,throttle:()=>ys,timer:()=>bs,toKNotation:()=>be,toLowercaseFirstLetter:()=>w,toPrecision:()=>fe,toScientificNotation:()=>ye,toThousands:()=>me,toUppercaseFirstLetter:()=>v,transpose:()=>Vi,traverse:()=>n,vertical:()=>Jt,visibility:()=>o,wrapIt:()=>On});var e=i(10);function n(t,e){e(t),t.children&&t.children.forEach(t=>{t&&n(t,e)})}function s(t){o(t,!0)}function a(t){o(t,!1)}function o(t,e){const i=e?"visible":"hidden";n(t,t=>{t.attr("visibility",i)})}class l extends e.Group{constructor(){super(...arguments),this.isMutationObserved=!0,this.addEventListener(e.ElementEvent.INSERTED,()=>{a(this)})}}function h(t){const e=t.appendChild(new l({class:"offscreen"}));return a(e),e}function c(t){let e=t;for(;e;){if("offscreen"===e.className)return!0;e=e.parent}return!1}const u=function(t){return"object"==typeof t&&null!==t};var d={}.toString;const p=function(t){if(!u(t)||!function(t,e){return d.call(t)==="[object "+e+"]"}(t,"Object"))return!1;if(null===Object.getPrototypeOf(t))return!0;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e};function g(t){return Array.isArray(t)}const f=function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:5;Object.entries(e).forEach(r=>{let[s,a]=r;const o=t;Object.prototype.hasOwnProperty.call(e,s)&&(a?p(a)?(p(t[s])||(o[s]={}),n<i?f(t[s],a,n+1,i):o[s]=e[s]):g(a)?(o[s]=[],o[s]=o[s].concat(a)):o[s]=a:o[s]=a)})},m=function(t){for(let e=0;e<(arguments.length<=1?0:arguments.length-1);e+=1)f(t,e+1<1||arguments.length<=e+1?void 0:arguments[e+1]);return t};function y(){o(this,"hidden"!==this.attributes.visibility)}class b extends e.CustomElement{_defaultOptions;_offscreen;get offscreenGroup(){return this._offscreen||(this._offscreen=h(this)),this._offscreen}initialized=!1;get defaultOptions(){return this._defaultOptions}constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(m({},{style:e},t)),this._defaultOptions=e}connectedCallback(){this.render(this.attributes,this),this.bindEvents(this.attributes,this),this.initialized=!0}disconnectedCallback(){this._offscreen?.destroy()}attributeChangedCallback(t){"visibility"===t&&y.call(this)}update(t,e){return this.attr(m({},this.attributes,t||{})),this.render?.(this.attributes,this,e)}clear(){this.removeChildren()}bindEvents(t,e){}getSubShapeStyle(t){const{x:e,y:n,transform:i,transformOrigin:r,class:s,className:a,zIndex:o,...l}=t;return l}}class x extends e.Text{_offscreen;get offscreenGroup(){return this._offscreen||(this._offscreen=h(this)),this._offscreen}disconnectedCallback(){this._offscreen?.destroy()}constructor(){let{style:t,...e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super({style:{text:"",fill:"black",fontFamily:"sans-serif",fontSize:16,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",lineWidth:1,textAlign:"start",textBaseline:"middle",...t},...e})}}function v(t){return t.toString().charAt(0).toUpperCase()+t.toString().slice(1)}function w(t){return t.toString().charAt(0).toLowerCase()+t.toString().slice(1)}function k(t,e){return`${e}${v(t)}`}function S(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const i=e||t.match(/^([a-z][a-z0-9]+)/)?.[0]||"",r=t.replace(new RegExp(`^(${i})`),"");return n?w(r):r}function A(t,e){Object.entries(e).forEach(e=>{let[n,i]=e;[t,...t.querySelectorAll(n)].filter(t=>t.matches(n)).forEach(t=>{t&&(t.style.cssText+=Object.entries(i).reduce((t,e)=>`${t}${e.join(":")};`,""))})})}const L=(t,e)=>{if(!t?.startsWith(e))return!1;const n=t[e.length];return n>="A"&&n<="Z"};function B(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const i={};return Object.entries(t).forEach(t=>{let[r,s]=t;if("className"===r||"class"===r);else if(L(r,"show")&&L(S(r,"show"),e)!==n)r===k(e,"show")?i[r]=s:i[r.replace(new RegExp(v(e)),"")]=s;else if(!L(r,"show")&&L(r,e)!==n){const t=S(r,e);"filter"===t&&"function"==typeof s||(i[t]=s)}}),i}function C(t,e){return Object.entries(t).reduce((t,n)=>{let[i,r]=n;return i.startsWith("show")?t[`show${e}${i.slice(4)}`]=r:t[`${e}${v(i)}`]=r,t},{})}function M(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["x","y","class","className"];const n=["transform","transformOrigin","anchor","visibility","pointerEvents","zIndex","cursor","clipPath","clipPathTargets","offsetPath","offsetPathTargets","offsetDistance","draggable","droppable"],i={},r={};return Object.entries(t).forEach(t=>{let[s,a]=t;e.includes(s)||(-1!==n.indexOf(s)?r[s]=a:i[s]=a)}),[i,r]}function E(t){return"number"==typeof t}function P(t){if(E(t))return[t,t,t,t];if(g(t)){const e=t.length;if(1===e)return[t[0],t[0],t[0],t[0]];if(2===e)return[t[0],t[1],t[0],t[1]];if(3===e)return[t[0],t[1],t[2],t[1]];if(4===e)return t}return[0,0,0,0]}class ${static registry=(()=>({g:e.Group,rect:e.Rect,circle:e.Circle,path:e.Path,text:x,ellipse:e.Ellipse,image:e.Image,line:e.Line,polygon:e.Polygon,polyline:e.Polyline,html:e.HTML}))();_elements;_parent;_data;_enter;_exit;_update;_merge;_split;_document;_transitions;_facetElements;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[null,null,null,null,null],s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:[];this._elements=Array.from(t),this._data=e,this._parent=n,this._document=i,this._enter=r[0],this._update=r[1],this._exit=r[2],this._merge=r[3],this._split=r[4],this._transitions=s,this._facetElements=a}selectAll(t){const e="string"==typeof t?this._parent.querySelectorAll(t):t;return new $(e,null,this._elements[0],this._document)}selectFacetAll(t){const e="string"==typeof t?this._parent.querySelectorAll(t):t;return new $(this._elements,null,this._parent,this._document,void 0,void 0,e)}select(t){const e="string"==typeof t?this._parent.querySelectorAll(t)[0]||null:t;return new $([e],null,e,this._document)}append(t){const e="function"==typeof t?t:()=>this.createElement(t),n=[];if(null!==this._data){for(let t=0;t<this._data.length;t++){const i=this._data[t],[r,s]=Array.isArray(i)?i:[i,null],a=e(r,t);a.__data__=r,null!==s&&(a.__fromElements__=s),this._parent.appendChild(a),n.push(a)}return new $(n,null,this._parent,this._document)}for(let t=0;t<this._elements.length;t++){const i=this._elements[t],r=e(i.__data__,t);i.appendChild(r),n.push(r)}return new $(n,null,n[0],this._document)}#t(t,e){const n=this._elements[0],i=n.querySelector(t);if(i)return new $([i],null,this._parent,this._document);const r="string"==typeof e?this.createElement(e):e();return n.appendChild(r),new $([r],null,this._parent,this._document)}maybeAppend(t,e){const n=this.#t("#"===t[0]?t:`#${t}`,e);return n.attr("id",t),n}maybeAppendByClassName(t,e){const n=t.toString(),i=this.#t("."===n[0]?n:`.${n}`,e);return i.attr("className",n),i}maybeAppendByName(t,e){const n=this.#t(`[name="${t}"]`,e);return n.attr("name",t),n}data(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t=>t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>null;const i=[],r=[],s=new Set(this._elements),a=[],o=new Set,l=new Map(this._elements.map((t,n)=>[e(t.__data__,n),t])),h=new Map(this._facetElements.map((t,n)=>[e(t.__data__,n),t])),c=function(t){const e=new Map;return t.forEach(t=>{const i=n(t.__data__);e.has(i)||e.set(i,[]),e.get(i).push(t)}),e}(this._elements);for(let u=0;u<t.length;u++){const d=t[u],p=e(d,u),g=n(d,u);if(l.has(p)){const t=l.get(p);t.__data__=d,t.__facet__=!1,r.push(t),s.delete(t),l.delete(p)}else if(h.has(p)){const t=h.get(p);t.__data__=d,t.__facet__=!0,r.push(t),h.delete(p)}else if(c.has(p)){const t=c.get(p);a.push([d,t]);for(const e of t)s.delete(e);c.delete(p)}else if(l.has(g)){const t=l.get(g);t.__toData__?t.__toData__.push(d):t.__toData__=[d],o.add(t),s.delete(t)}else i.push(d)}const u=[new $([],i,this._parent,this._document),new $(r,null,this._parent,this._document),new $(s,null,this._parent,this._document),new $([],a,this._parent,this._document),new $(o,null,this._parent,this._document)];return new $(this._elements,null,this._parent,this._document,u)}merge(t){const e=[...this._elements,...t._elements],n=[...this._transitions,...t._transitions];return new $(e,null,this._parent,this._document,void 0,n)}createElement(t){if(this._document)return this._document.createElement(t,{});const e=$.registry[t];return e?new e:function(t){throw new Error(t)}(`Unknown node type: ${t}`)}join(){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t=>t,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t=>t.remove(),n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t=>t,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:t=>t.remove();const r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:t=>t)(this._enter),s=t(this._update),a=e(this._exit),o=n(this._merge),l=i(this._split);return s.merge(r).merge(a).merge(o).merge(l)}remove(){for(let t=0;t<this._elements.length;t++){const e=this._elements[t],n=this._transitions[t];n?n.then(()=>e.remove()):e.remove()}return new $([],null,this._parent,this._document,void 0,this._transitions)}each(t){for(let e=0;e<this._elements.length;e++){const n=this._elements[e],i=n.__data__;t.call(n,i,e)}return this}attr(t,e){const n="function"!=typeof e?()=>e:e;return this.each(function(i,r){void 0!==e&&(this[t]=n.call(this,i,r))})}style(t,e){const n="function"!=typeof e||arguments.length>2&&void 0!==arguments[2]&&!arguments[2]?()=>e:e;return this.each(function(i,r){void 0!==e&&(this.style[t]=n.call(this,i,r))})}styles(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.each(function(n,i){Object.entries(t).forEach(t=>{let[r,s]=t;const a="function"==typeof s&&e?s:()=>s;void 0!==s&&this.attr(r,a.call(this,n,i))})})}update(t){const e="function"!=typeof t||arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?()=>t:t;return this.each(function(n,i){t&&this.update&&this.update(e.call(this,n,i))})}maybeUpdate(t){const e="function"!=typeof t||arguments.length>1&&void 0!==arguments[1]&&!arguments[1]?()=>t:t;return this.each(function(n,i){t&&this.update&&this.update(e.call(this,n,i))})}transition(t){const{_transitions:e}=this,n=new Array(this._elements.length);return this.each(function(e,i){n[i]=t.call(this,e,i)}),this._transitions=function(t){if(!g(t))return[];for(var e=[],n=0;n<t.length;n++)e=e.concat(t[n]);return e}(n),this}on(t,e){return this.each(function(){this.addEventListener(t,e)}),this}call(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return t.call(this._parent,this,...n),this}node(){return this._elements[0]}nodes(){return this._elements}transitions(){return this._transitions.filter(t=>!!t)}parent(){return this._parent}}function T(t){return new $([t],null,t,t.ownerDocument)}function O(t,e,n){return t.querySelector(e)?T(t).select(e):T(t).append(n)}function N(t){return"function"==typeof t}function z(t,e,n){let i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:t=>{t.node().removeChildren()};return t?n(e):(i&&r(e),null)}const _=(t,e,n)=>[["M",t-n,e],["A",n,n,0,1,0,t+n,e],["A",n,n,0,1,0,t-n,e],["Z"]],I=_,F=(t,e,n)=>[["M",t,e+n],["L",t,e-n]],G=(t,e,n)=>[["M",t-n,e],["L",t+n,e]],H=G,V=function(t){var e=typeof t;return null!==t&&"object"===e||"function"===e};function W(t){return"string"==typeof t}class D extends b{render(t,e){const{x:n=0,y:i=0}=t,{symbol:r,size:s=16,...a}=this.getSubShapeStyle(t),o=function(t){const e=function(t){let e="default";if(V(t)&&t instanceof Image)e="image";else if(N(t))e="symbol";else if(W(t)){const n=new RegExp("data:(image|text)");e=t.match(n)?"base64":/^(https?:\/\/(([a-zA-Z0-9]+-?)+[a-zA-Z0-9]+\.)+[a-zA-Z]+)(:\d+)?(\/.*)?(\?.*)?(#.*)?$/.test(t)?"url":"symbol"}return e}(t);return["base64","url","image"].includes(e)?"image":t&&"symbol"===e?"path":null}(r);z(!!o,T(e),t=>{t.maybeAppendByClassName("marker",o).attr("className",`marker ${o}-marker`).call(t=>{if("image"===o){const e=2*s;t.styles({img:r,width:e,height:e,x:n-s,y:i-s})}else{const e=s/2,o=N(r)?r:D.getSymbol(r);t.styles({d:o?.(n,i,e),...a})}})})}static MARKER_SYMBOL_MAP=(()=>new Map)();static registerSymbol=(t,e)=>{D.MARKER_SYMBOL_MAP.set(t,e)};static getSymbol=t=>D.MARKER_SYMBOL_MAP.get(t);static getSymbols=()=>Array.from(D.MARKER_SYMBOL_MAP.keys())}D.registerSymbol("cross",(t,e,n)=>[["M",t-n,e-n],["L",t+n,e+n],["M",t+n,e-n],["L",t-n,e+n]]),D.registerSymbol("hyphen",(t,e,n)=>[["M",t-n,e],["L",t+n,e]]),D.registerSymbol("line",F),D.registerSymbol("plus",(t,e,n)=>[["M",t-n,e],["L",t+n,e],["M",t,e-n],["L",t,e+n]]),D.registerSymbol("tick",(t,e,n)=>[["M",t-n/2,e-n],["L",t+n/2,e-n],["M",t,e-n],["L",t,e+n],["M",t-n/2,e+n],["L",t+n/2,e+n]]),D.registerSymbol("circle",_),D.registerSymbol("point",I),D.registerSymbol("bowtie",(t,e,n)=>{const i=n-1.5;return[["M",t-n,e-i],["L",t+n,e+i],["L",t+n,e-i],["L",t-n,e+i],["Z"]]}),D.registerSymbol("hexagon",(t,e,n)=>{const i=n/2*Math.sqrt(3);return[["M",t,e-n],["L",t+i,e-n/2],["L",t+i,e+n/2],["L",t,e+n],["L",t-i,e+n/2],["L",t-i,e-n/2],["Z"]]}),D.registerSymbol("square",(t,e,n)=>[["M",t-n,e-n],["L",t+n,e-n],["L",t+n,e+n],["L",t-n,e+n],["Z"]]),D.registerSymbol("diamond",(t,e,n)=>[["M",t-n,e],["L",t,e-n],["L",t+n,e],["L",t,e+n],["Z"]]),D.registerSymbol("triangle",(t,e,n)=>{const i=n*Math.sin(1/3*Math.PI);return[["M",t-n,e+i],["L",t,e-i],["L",t+n,e+i],["Z"]]}),D.registerSymbol("triangle-down",(t,e,n)=>{const i=n*Math.sin(1/3*Math.PI);return[["M",t-n,e-i],["L",t+n,e-i],["L",t,e+i],["Z"]]}),D.registerSymbol("line",F),D.registerSymbol("dot",G),D.registerSymbol("dash",H),D.registerSymbol("smooth",(t,e,n)=>[["M",t-n,e],["A",n/2,n/2,0,1,1,t,e],["A",n/2,n/2,0,1,0,t+n,e]]),D.registerSymbol("hv",(t,e,n)=>[["M",t-n-1,e-2.5],["L",t,e-2.5],["L",t,e+2.5],["L",t+n+1,e+2.5]]),D.registerSymbol("vh",(t,e,n)=>[["M",t-n-1,e+2.5],["L",t,e+2.5],["L",t,e-2.5],["L",t+n+1,e-2.5]]),D.registerSymbol("hvh",(t,e,n)=>[["M",t-(n+1),e+2.5],["L",t-n/2,e+2.5],["L",t-n/2,e-2.5],["L",t+n/2,e-2.5],["L",t+n/2,e+2.5],["L",t+n+1,e+2.5]]),D.registerSymbol("vhv",function(t,e){return[["M",t-5,e+2.5],["L",t-5,e],["L",t,e],["L",t,e-3],["L",t,e+3],["L",t+6.5,e+3]]});class j extends b{static tag="tag";constructor(t){super(t,{padding:4,spacing:4})}render(e,n){const{padding:i=0,marker:r,text:s,radius:a,spacing:o,align:l,verticalAlign:h}=e,c=B(e,"label"),u=B(e,"background"),[d,p,g,f]=P(i),m=O(n,".tag-content","g").attr("className","tag-content").style("zIndex",0).node(),y=r||{symbol:"triangle",size:0},b=O(m,".tag-marker",()=>new D({style:y})).attr("className","tag-marker").update(y).node(),{x:v,y:w}=function(t,e){const n=t.getLocalBounds();return{x:n.halfExtents[0]?n.max[0]+(e||0):t.style.x,y:n.halfExtents[1]?(n.min[1]+n.max[1])/2:t.style.y}}(b,o);T(m).maybeAppendByClassName("tag-text",()=>new x).styles({fontSize:12,text:t(s)?"":`${s}`,x:v,y:w,...c,textBaseline:"middle"}).call(t=>{s||t.remove()}),function(t,e,n,i,r){const s=t.getLocalBounds();let a=0,o=0;"start"===i&&(a=e),"center"===i&&(a=-s.halfExtents[0]),"end"===i&&(a=-e-2*s.halfExtents[0]),"top"===r&&(o=n+s.halfExtents[1]),"middle"===r&&(o=0),"bottom"===r&&(o=n-2*s.halfExtents[1]),t.setLocalPosition([a,o])}(m,f,d,l||"start",h||"top");const k=m.getLocalBounds();T(n).maybeAppendByClassName("tag-background","rect").styles({zIndex:-1,y:k.min[1]-d,x:k.min[0]-f,width:null!==u?f+p+2*k.halfExtents[0]:0,height:null!==u?d+g+2*k.halfExtents[1]:0,radius:a??2,fill:"#fafafa",stroke:"#d9d9d9",lineWidth:1,...u})}}const R=function(t){return void 0===t};function Y(t,e){if(Object.hasOwn)return Object.hasOwn(t,e);if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(t),e)}function q(t,e,n,i){for(var r in n=n||0,i=i||5,e)if(Y(e,r)){var s=e[r];null!==s&&p(s)?(p(t[r])||(t[r]={}),n<i?q(t[r],s,n+1,i):t[r]=e[r]):g(s)?(t[r]=[],t[r]=t[r].concat(s)):void 0!==s&&(t[r]=s)}}const K=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];for(var i=0;i<e.length;i+=1)q(t,e[i]);return t},Z={small:{textFontSize:10,buttonWidth:40,buttonHeight:20,markerSize:8},middle:{textFontSize:12,buttonWidth:60,buttonHeight:30,markerSize:12},large:{textFontSize:16,buttonWidth:80,buttonHeight:40,markerSize:16}},U={primary:{default:{textFill:"#fff",buttonFill:"#1890ff",buttonLineWidth:0,markerFill:"#1890ff"},active:{buttonFill:"#40a9ff",markerFill:"#40a9ff"},disabled:{}},dashed:{default:{buttonFill:"transparent",buttonStroke:"#bbb",buttonLineDash:[5,5]},active:{},disabled:{}},link:{default:{textFill:"#1890ff",buttonFill:"transparent",buttonLineWidth:0,markerFill:"#1890ff"},active:{},disabled:{}},text:{default:{textFill:"#000",buttonFill:"transparent",buttonLineWidth:0,markerFill:"#000"},active:{},disabled:{}},default:{default:{textFill:"#000",buttonFill:"transparent",buttonStroke:"#bbb",markerFill:"#bbb"},active:{textFill:"#1890ff",buttonStroke:"#1890ff",markerStroke:"#1890ff"},disabled:{}}},X={strict:{textFill:"#b8b8b8"},buttonStroke:"#d9d9d9",buttonFill:"#f5f5f5",markerStroke:"#d9d9d9"};class J extends b{static tag="button";textShape;state="default";get markerSize(){const{markerSymbol:t}=this.attributes,e=this.getStyle("marker");return t?e?.size||2:0}get textAvailableWidth(){const{markerSymbol:t,padding:e,ellipsis:n,width:i,markerSpacing:r}=this.attributes;if(!n)return 1/0;const s=R(i)?this.getStyle("button").width:i;return t?s-2*e-r-this.markerSize:s-2*e}get buttonHeight(){const{height:t}=this.attributes;return t?+t:+this.getStyle("button").height}constructor(t){super(t,{cursor:"pointer",padding:10,size:"middle",type:"default",text:"",state:"default",markerAlign:"left",markerSpacing:5,default:{buttonLineWidth:1,buttonRadius:5},active:{}})}getStyle(t){const{size:e,type:n}=this.attributes,{state:i}=this,r=K({},Z[e],U[n][i],this.attributes.default,this.attributes[i]);return"disabled"===i&&(Object.keys(r).forEach(t=>{t in X&&(r[t]=X[t])}),Object.keys(X.strict).forEach(t=>{r[t]=X.strict[t]}),K(r,this.attributes.disabled||{})),B(r,t)}render(t,e){const{text:n="",padding:i=0,markerSymbol:r,markerSpacing:s=0,x:a=0,y:o=0}=t;e.attr({cursor:"disabled"===this.state?"not-allowed":"pointer"});const[l,h,c,u]=P(i),d=this.buttonHeight,p=this.getStyle("marker"),{markerSize:g}=this,f={...p,symbol:r,x:a+u+g/2,y:o+d/2,size:g},m=O(e,".marker",()=>new D({className:"marker",style:f})).update({style:f}).node().getLocalBounds(),y=this.getStyle("text");this.textShape=O(e,".text","text").attr("className","text").styles({x:a+(g?m.max[0]+s:u),y:o+d/2,...y,text:n,textAlign:"left",textBaseline:"middle",wordWrap:!0,wordWrapWidth:this.textAvailableWidth,maxLines:1,textOverflow:"..."}).node();const b=this.textShape.getLocalBounds(),x=this.getStyle("button");T(e).maybeAppendByClassName(".background","rect").styles({zIndex:-1,...x,x:a,y:o,height:d,width:u+(g?g+s:0)+2*b.halfExtents[0]+h})}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.attr(K({},this.attributes,t));const{state:e}=this.attributes;this.state=e,this.render(this.attributes,this)}setState(t){this.update({state:t})}hide(){this.style.visibility="hidden"}show(){this.style.visibility="visible"}clickEvents=()=>{const{onClick:t,state:e}=this.attributes;"disabled"!==e&&t?.call(this,this)};mouseenterEvent=()=>{const{state:t}=this.attributes;"disabled"!==t&&(this.state="active",this.render(this.attributes,this))};mouseleaveEvent=()=>{const{state:t}=this.attributes;this.state=t,this.render(this.attributes,this)};bindEvents(){this.addEventListener("click",this.clickEvents),this.addEventListener("mouseenter",this.mouseenterEvent),this.addEventListener("mouseleave",this.mouseleaveEvent)}}const Q=function(t,e){if(t)if(g(t))for(var n=0,i=t.length;n<i&&!1!==e(t[n],n);n++);else if(V(t))for(var r in t)if(t.hasOwnProperty(r)&&!1===e(t[r],r))break};var tt=Object.prototype.hasOwnProperty;const et=function(t,e){if(null===t||!p(t))return{};var n={};return Q(e,function(e){tt.call(t,e)&&(n[e]=t[e])}),n};class nt extends b{static tag="breadcrumb";static defaultOptions={style:{separator:{text:"/",style:{fontSize:14,fill:"rgba(0, 0, 0, 0.45)"},spacing:8},textStyle:{default:{fontSize:14,fill:"rgba(0, 0, 0, 0.45)"},active:{fill:"#5468ff",cursor:"pointer"}},padding:[8,8,8,8]}};constructor(t){super(K({},nt.defaultOptions,t))}render(e,n){const{x:i,y:r,items:s,textStyle:a,padding:o=0,width:l,separator:h}=e,[c,u,d]=P(o),p=B(e,"tag"),g=O(n,".container","g").styles({className:"container",x:i+d,y:r+c});let f=0,m=0;g.node().removeChildren();for(let e=0;e<s.length;e++){const n=s[e],i=new j({className:"breadcrumb-item",style:{transform:`translate(${f}, ${m})`,...p,text:t(n.text)?n.id:n.text,...et(n,["marker"]),padding:0}});g.append(()=>i);const r=i.getLocalBounds(),a=2*r.halfExtents[0],o=2*r.halfExtents[1];f+=a,t(l)||f>l-u&&(i.attr({transform:`translateY(${m+o})`}),f=a,m+=o),this.bindInnerEvents(i,n);const{spacing:c=0,text:d="/",style:y}=h||{};if(e!==s.length-1){const t=new x({className:`${nt.tag}-separator`,id:`${nt.tag}-separator-${e}`,style:{x:f+c,y:m+o/2,...y,text:d,textAlign:"end",textBaseline:"middle"}});g.append(()=>t),f+=2*t.getLocalBounds().halfExtents[0]+c}}}update(t){this.attr(K({},this.attributes,t)),this.render(this.attributes,this)}bindInnerEvents(t,e){const{items:n,onClick:i}=this.attributes;i&&t.addEventListener("click",()=>{i.call(t,e.id,e,n)})}}const it=function(t){return t};class rt{constructor(t){this.options=K({},this.getDefaultOptions()),this.update(t)}getOptions(){return this.options}update(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=K({},this.options,t),this.rescale(t)}rescale(t){}}function st(t,e){return e-t?n=>(n-t)/(e-t):t=>.5}function at(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),i=1;i<e;i++)n[i-1]=arguments[i];return n.reduce((t,e)=>n=>t(e(n)),t)}var ot=i(450),lt=i.n(ot);function ht(t,e,n){let i=n;return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ct(t){const e=lt().get(t);if(!e)return null;const{model:n,value:i}=e;return"rgb"===n?i:"hsl"===n?function(t){const e=t[0]/360,n=t[1]/100,i=t[2]/100,r=t[3];if(0===n)return[255*i,255*i,255*i,r];const s=i<.5?i*(1+n):i+n-i*n,a=2*i-s;return[255*ht(a,s,e+1/3),255*ht(a,s,e),255*ht(a,s,e-1/3),r]}(i):null}const ut=(t,e)=>n=>t*(1-n)+e*n,dt=(t,e)=>"number"==typeof t&&"number"==typeof e?ut(t,e):"string"==typeof t&&"string"==typeof e?((t,e)=>{const n=ct(t),i=ct(e);return null===n||null===i?n?()=>t:()=>e:t=>{const e=new Array(4);for(let r=0;r<4;r+=1){const s=n[r],a=i[r];e[r]=s*(1-t)+a*t}const[r,s,a,o]=e;return`rgba(${Math.round(r)}, ${Math.round(s)}, ${Math.round(a)}, ${o})`}})(t,e):()=>t,pt=(t,e)=>{const n=ut(t,e);return t=>Math.round(n(t))};function gt(t){return!(R(t)||(e=t,null===e)||Number.isNaN(t));var e}const ft=Math.sqrt(50),mt=Math.sqrt(10),yt=Math.sqrt(2);function bt(t,e,n){const i=(e-t)/Math.max(0,n),r=Math.floor(Math.log(i)/Math.LN10),s=i/10**r;return r>=0?(s>=ft?10:s>=mt?5:s>=yt?2:1)*10**r:-(10**-r)/(s>=ft?10:s>=mt?5:s>=yt?2:1)}const xt=function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5;const i=[t,e];let r,s=0,a=i.length-1,o=i[s],l=i[a];return l<o&&([o,l]=[l,o],[s,a]=[a,s]),r=bt(o,l,n),r>0?(o=Math.floor(o/r)*r,l=Math.ceil(l/r)*r,r=bt(o,l,n)):r<0&&(o=Math.ceil(o*r)/r,l=Math.floor(l*r)/r,r=bt(o,l,n)),r>0?(i[s]=Math.floor(o/r)*r,i[a]=Math.ceil(l/r)*r):r<0&&(i[s]=Math.ceil(o*r)/r,i[a]=Math.floor(l*r)/r),i},vt=(t,e,n)=>{const[i,r]=t,[s,a]=e;let o,l;return i<r?(o=st(i,r),l=n(s,a)):(o=st(r,i),l=n(a,s)),at(l,o)},wt=(t,e,n)=>{const i=Math.min(t.length,e.length)-1,r=new Array(i),s=new Array(i),a=t[0]>t[i],o=a?[...t].reverse():t,l=a?[...e].reverse():e;for(let t=0;t<i;t+=1)r[t]=st(o[t],o[t+1]),s[t]=n(l[t],l[t+1]);return e=>{const n=function(t,e,n,i){let r=1,s=i||t.length;const a=t=>t;for(;r<s;){const n=Math.floor((r+s)/2);a(t[n])>e?s=n:r=n+1}return r}(t,e,0,i)-1,a=r[n];return at(s[n],a)(e)}},kt=(t,e,n,i)=>(Math.min(t.length,e.length)>2?wt:vt)(t,e,i?pt:n);class St extends rt{getDefaultOptions(){return{domain:[0,1],range:[0,1],nice:!1,clamp:!1,round:!1,interpolate:ut,tickCount:5}}map(t){return gt(t)?this.output(t):this.options.unknown}invert(t){return gt(t)?this.input(t):this.options.unknown}nice(){if(!this.options.nice)return;const[t,e,n,...i]=this.getTickMethodOptions();this.options.domain=this.chooseNice()(t,e,n,...i)}getTicks(){const{tickMethod:t}=this.options,[e,n,i,...r]=this.getTickMethodOptions();return t(e,n,i,...r)}getTickMethodOptions(){const{domain:t,tickCount:e}=this.options;return[t[0],t[t.length-1],e]}chooseNice(){return xt}rescale(){this.nice();const[t,e]=this.chooseTransforms();this.composeOutput(t,this.chooseClamp(t)),this.composeInput(t,e,this.chooseClamp(e))}chooseClamp(t){const{clamp:e,range:n}=this.options,i=this.options.domain.map(t),r=Math.min(i.length,n.length);return e?function(t,e){const n=e<t?e:t,i=t>e?t:e;return t=>Math.min(Math.max(n,t),i)}(i[0],i[r-1]):it}composeOutput(t,e){const{domain:n,range:i,round:r,interpolate:s}=this.options,a=kt(n.map(t),i,s,r);this.output=at(a,e,t)}composeInput(t,e,n){const{domain:i,range:r}=this.options,s=kt(r,i.map(t),ut);this.input=at(e,n,s)}}const At=(t,e,n)=>{let i,r,s=t,a=e;if(s===a&&n>0)return[s];let o=bt(s,a,n);if(0===o||!Number.isFinite(o))return[];if(o>0){s=Math.ceil(s/o),a=Math.floor(a/o),r=new Array(i=Math.ceil(a-s+1));for(let t=0;t<i;t+=1)r[t]=(s+t)*o}else{o=-o,s=Math.ceil(s*o),a=Math.floor(a*o),r=new Array(i=Math.ceil(a-s+1));for(let t=0;t<i;t+=1)r[t]=(s+t)/o}return r};class Lt extends St{getDefaultOptions(){return{domain:[0,1],range:[0,1],unknown:void 0,nice:!1,clamp:!1,round:!1,interpolate:dt,tickMethod:At,tickCount:5}}chooseTransforms(){return[it,it]}clone(){return new Lt(this.options)}}function Bt(t,e){let{map:n,initKey:i}=t;const r=i(e);return n.has(r)?n.get(r):e}function Ct(t){return"object"==typeof t?t.valueOf():t}class Mt extends Map{constructor(t){if(super(),this.map=new Map,this.initKey=Ct,null!==t)for(const[e,n]of t)this.set(e,n)}get(t){return super.get(Bt({map:this.map,initKey:this.initKey},t))}has(t){return super.has(Bt({map:this.map,initKey:this.initKey},t))}set(t,e){return super.set(function(t,e){let{map:n,initKey:i}=t;const r=i(e);return n.has(r)?n.get(r):(n.set(r,e),e)}({map:this.map,initKey:this.initKey},t),e)}delete(t){return super.delete(function(t,e){let{map:n,initKey:i}=t;const r=i(e);return n.has(r)&&(e=n.get(r),n.delete(r)),e}({map:this.map,initKey:this.initKey},t))}}const Et=Symbol("defaultUnknown");function Pt(t,e,n){for(let i=0;i<e.length;i+=1)t.has(e[i])||t.set(n(e[i]),i)}function $t(t){const{value:e,from:n,to:i,mapper:r,notFoundReturn:s}=t;let a=r.get(e);if(void 0===a){if(s!==Et)return s;a=n.push(e)-1,r.set(e,a)}return i[a%i.length]}function Tt(t){return t instanceof Date?t=>`${t}`:"object"==typeof t?t=>JSON.stringify(t):t=>t}class Ot extends rt{getDefaultOptions(){return{domain:[],range:[],unknown:Et}}constructor(t){super(t)}map(t){return 0===this.domainIndexMap.size&&Pt(this.domainIndexMap,this.getDomain(),this.domainKey),$t({value:this.domainKey(t),mapper:this.domainIndexMap,from:this.getDomain(),to:this.getRange(),notFoundReturn:this.options.unknown})}invert(t){return 0===this.rangeIndexMap.size&&Pt(this.rangeIndexMap,this.getRange(),this.rangeKey),$t({value:this.rangeKey(t),mapper:this.rangeIndexMap,from:this.getRange(),to:this.getDomain(),notFoundReturn:this.options.unknown})}rescale(t){const[e]=this.options.domain,[n]=this.options.range;if(this.domainKey=Tt(e),this.rangeKey=Tt(n),!this.rangeIndexMap)return this.rangeIndexMap=new Map,void(this.domainIndexMap=new Map);t&&!t.range||this.rangeIndexMap.clear(),(!t||t.domain||t.compare)&&(this.domainIndexMap.clear(),this.sortedDomain=void 0)}clone(){return new Ot(this.options)}getRange(){return this.options.range}getDomain(){if(this.sortedDomain)return this.sortedDomain;const{domain:t,compare:e}=this.options;return this.sortedDomain=e?[...t].sort(e):t,this.sortedDomain}}function Nt(t){return Math.round(1e12*t)/1e12}class zt extends Ot{getDefaultOptions(){return{domain:[],range:[0,1],align:.5,round:!1,paddingInner:0,paddingOuter:0,padding:0,unknown:Et,flex:[]}}constructor(t){super(t)}clone(){return new zt(this.options)}getStep(t){return void 0===this.valueStep?1:"number"==typeof this.valueStep?this.valueStep:void 0===t?Array.from(this.valueStep.values())[0]:this.valueStep.get(t)}getBandWidth(t){return void 0===this.valueBandWidth?1:"number"==typeof this.valueBandWidth?this.valueBandWidth:void 0===t?Array.from(this.valueBandWidth.values())[0]:this.valueBandWidth.get(t)}getRange(){return this.adjustedRange}getPaddingInner(){const{padding:t,paddingInner:e}=this.options;return t>0?t:e}getPaddingOuter(){const{padding:t,paddingOuter:e}=this.options;return t>0?t:e}rescale(){super.rescale();const{align:t,domain:e,range:n,round:i,flex:r}=this.options,{adjustedRange:s,valueBandWidth:a,valueStep:o}=function(t){var e;const{domain:n}=t,i=n.length;if(0===i)return{valueBandWidth:void 0,valueStep:void 0,adjustedRange:[]};if(null===(e=t.flex)||void 0===e?void 0:e.length)return function(t){const{domain:e,range:n,paddingOuter:i,paddingInner:r,flex:s,round:a,align:o}=t,l=e.length,h=function(t,e){const n=e-t.length;return n>0?[...t,...new Array(n).fill(1)]:n<0?t.slice(0,e):t}(s,l),[c,u]=n,d=u-c,p=d/(2/l*i+1-1/l*r),g=p*r/l,f=p-l*g,m=function(t){const e=Math.min(...t);return t.map(t=>t/e)}(h),y=f/m.reduce((t,e)=>t+e),b=new Mt(e.map((t,e)=>{const n=m[e]*y;return[t,a?Math.floor(n):n]})),x=new Mt(e.map((t,e)=>{const n=m[e]*y+g;return[t,a?Math.floor(n):n]})),v=Array.from(x.values()).reduce((t,e)=>t+e),w=c+(d-(v-v/l*r))*o;let k=a?Math.round(w):w;const S=new Array(l);for(let t=0;t<l;t+=1){S[t]=Nt(k);const n=e[t];k+=x.get(n)}return{valueBandWidth:b,valueStep:x,adjustedRange:S}}(t);const{range:r,paddingOuter:s,paddingInner:a,round:o,align:l}=t;let h,c,u=r[0];const d=r[1]-u,p=2*s,g=i-a;h=d/Math.max(1,p+g),o&&(h=Math.floor(h)),u+=(d-h*(i-a))*l,c=h*(1-a),o&&(u=Math.round(u),c=Math.round(c));const f=new Array(i).fill(0).map((t,e)=>u+e*h);return{valueStep:h,valueBandWidth:c,adjustedRange:f}}({align:t,range:n,round:i,flex:r,paddingInner:this.getPaddingInner(),paddingOuter:this.getPaddingOuter(),domain:e});this.valueStep=o,this.valueBandWidth=a,this.adjustedRange=s}}var _t=function(t){if("object"!=typeof t||null===t)return t;var e;if(g(t)){e=[];for(var n=0,i=t.length;n<i;n++)"object"==typeof t[n]&&null!=t[n]?e[n]=_t(t[n]):e[n]=t[n]}else for(var r in e={},t)"object"==typeof t[r]&&null!=t[r]?e[r]=_t(t[r]):e[r]=t[r];return e};const It=_t;class Ft extends e.DisplayObject{columnsGroup;constructor(t){let{style:n,...i}=t;super(K({},{type:"column"},{style:n,...i})),this.columnsGroup=new e.Group({name:"columns"}),this.appendChild(this.columnsGroup),this.render()}render(){const{columns:t,x:e,y:n}=this.attributes;this.columnsGroup.style.transform=`translate(${e}, ${n})`,T(this.columnsGroup).selectAll(".column").data(t.flat()).join(t=>t.append("rect").attr("className","column").each(function(t){this.attr(t)}),t=>t.each(function(t){this.attr(t)}),t=>t.remove())}update(t){this.attr(m({},this.attributes,t)),this.render()}clear(){this.removeChildren()}}class Gt extends e.DisplayObject{linesGroup=(()=>this.appendChild(new e.Group))();areasGroup=(()=>this.appendChild(new e.Group))();constructor(t){let{style:e,...n}=t;super(K({},{type:"lines"},{style:e,...n})),this.render()}render(){const{lines:t,areas:e,x:n,y:i}=this.attributes;this.style.transform=`translate(${n}, ${i})`,t&&this.renderLines(t),e&&this.renderAreas(e)}clear(){this.linesGroup.removeChildren(),this.areasGroup.removeChildren()}update(t){this.attr(m({},this.attributes,t)),this.render()}renderLines(t){T(this.linesGroup).selectAll(".line").data(t).join(t=>t.append("path").attr("className","line").each(function(t){this.attr(t)}),t=>t.each(function(t){this.attr(t)}),t=>t.remove())}renderAreas(t){T(this.linesGroup).selectAll(".area").data(t).join(t=>t.append("path").attr("className","area").each(function(t){this.attr(t)}),t=>t.each(function(t){this.style(t)}),t=>t.remove())}}const Ht=function(t,e,n){return t<e?e:t>n?n:t},Vt=function(t){return null!==t&&"function"!=typeof t&&isFinite(t.length)};var Wt=function(t,e){if(t===e)return!0;if(!t||!e)return!1;if(W(t)||W(e))return!1;if(Vt(t)||Vt(e)){if(t.length!==e.length)return!1;for(var n=!0,i=0;i<t.length&&(n=Wt(t[i],e[i]));i++);return n}if(u(t)||u(e)){var r=Object.keys(t),s=Object.keys(e);if(r.length!==s.length)return!1;for(n=!0,i=0;i<r.length&&(n=Wt(t[r[i]],e[r[i]]));i++);return n}return!1};const Dt=Wt;function jt(t,e){return[t[0]*e,t[1]*e]}function Rt(t,e){return[t[0]+e[0],t[1]+e[1]]}function Yt(t,e){return[t[0]-e[0],t[1]-e[1]]}function qt(t,e){return[Math.min(t[0],e[0]),Math.min(t[1],e[1])]}function Kt(t,e){return[Math.max(t[0],e[0]),Math.max(t[1],e[1])]}function Zt(t,e){return Math.sqrt((t[0]-e[0])**2+(t[1]-e[1])**2)}function Ut(t){if(0===t[0]&&0===t[1])return[0,0];const e=Math.sqrt(t[0]**2+t[1]**2);return[t[0]/e,t[1]/e]}function Xt(t,e,n){const[i,r]=t,[s,a]=e,o=i-s,l=r-a,h=Math.sin(n),c=Math.cos(n);return[o*c-l*h+s,o*h+l*c+a]}function Jt(t,e){return e?[t[1],-t[0]]:[-t[1],t[0]]}function Qt(t){let e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[[0,0],[1,1]];const n=!!(arguments.length>1&&void 0!==arguments[1]&&arguments[1]),i=[];for(let e=0,n=t.length;e<n;e+=2)i.push([t[e],t[e+1]]);const r=function(t,e,n,i){const r=[],s=!!i;let a,o,l,h,c,u=[1/0,1/0],d=[-1/0,-1/0];if(s){[u,d]=i;for(let e=0,n=t.length;e<n;e+=1){const n=t[e];u=qt(u,n),d=Kt(d,n)}}for(let e=0,i=t.length;e<i;e+=1){const p=t[e];if(0!==e||n)if(e!==i-1||n){a=t[[e?e-1:i-1,e-1][n?0:1]],o=t[n?(e+1)%i:e+1];let g=[0,0];g=Yt(o,a),g=jt(g,.4);let f=Zt(p,a),m=Zt(p,o);const y=f+m;0!==y&&(f/=y,m/=y);let b=jt(g,-f),x=jt(g,m);h=Rt(p,b),l=Rt(p,x),l=qt(l,Kt(o,p)),l=Kt(l,qt(o,p)),b=Yt(l,p),b=jt(b,-f/m),h=Rt(p,b),h=qt(h,Kt(a,p)),h=Kt(h,qt(a,p)),x=Yt(p,h),x=jt(x,m/f),l=Rt(p,x),s&&(h=Kt(h,u),h=qt(h,d),l=Kt(l,u),l=qt(l,d)),r.push(c),r.push(h),c=l}else h=p,r.push(c),r.push(h);else c=p}return n&&r.push(r.shift()),r}(i,0,n,e),s=i.length,a=[];let o,l,h;for(let t=0;t<s-1;t+=1)o=r[2*t],l=r[2*t+1],h=i[t+1],a.push(["C",o[0],o[1],l[0],l[1],h[0],h[1]]);return n&&(o=r[s],l=r[s+1],[h]=i,a.push(["C",o[0],o[1],l[0],l[1],h[0],h[1]])),a}function te(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=e?t.length-1:0,i=t.map((t,e)=>[e===n?"M":"L",...t]);return e?i.reverse():i}function ee(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t.length<=2)return te(t);const n=[],i=t.length;for(let r=0;r<i;r+=1){const s=e?t[i-r-1]:t[r];Dt(s,n.slice(-2))||n.push(...s)}const r=Qt(n,!1);return e?r.unshift(["M",...t[i-1]]):r.unshift(["M",...t[0]]),r}function ne(t,e,n){const i=It(t);return i.push(["L",e,n],["L",0,n],["Z"]),i}const ie=function(t){if(g(t))return t.reduce(function(t,e){return Math.min(t,e)},t[0])},re=function(t,e){if(g(t)){for(var n,i=1/0,r=0;r<t.length;r++){var s=t[r],a=N(e)?e(s):s[e];a<i&&(n=s,i=a)}return n}};function se(t){if(!Array.isArray(t))return-1/0;var e=t.length;if(!e)return-1/0;for(var n=t[0],i=1;i<e;i++)n=Math.max(n,t[i]);return n}const ae=function(t,e){if(g(t)){for(var n,i=-1/0,r=0;r<t.length;r++){var s=t[r],a=N(e)?e(s):s[e];a>i&&(n=s,i=a)}return n}};function oe(t){return 0===t.length?[0,0]:[ie(re(t,t=>ie(t)||0)),se(ae(t,t=>se(t)||0))]}function le(t){const e=It(t),n=e[0].length,[i,r]=[Array(n).fill(0),Array(n).fill(0)];for(let t=0;t<e.length;t+=1){const s=e[t];for(let t=0;t<n;t+=1)s[t]>=0?(s[t]+=i[t],i[t]=s[t]):(s[t]+=r[t],r[t]=s[t])}return e}class he extends b{static tag="sparkline";get rawData(){const{data:t}=this.attributes;if(!t||0===t?.length)return[[]];const e=It(t);return E(e[0])?[e]:e}get data(){return this.attributes.isStack?le(this.rawData):this.rawData}get scales(){return this.createScales(this.data)}get baseline(){const{y:t}=this.scales,[e,n]=t.getOptions().domain||[0,0];return n<0?t.map(n):t.map(e<0?0:e)}get containerShape(){const{width:t,height:e}=this.attributes;return{width:t,height:e}}get linesStyle(){const{type:t,isStack:e,smooth:n}=this.attributes;if("line"!==t)throw new Error("linesStyle can only be used in line type");const i=B(this.attributes,"area"),r=B(this.attributes,"line"),{width:s}=this.containerShape,{data:a}=this;if(0===a[0].length)return{lines:[],areas:[]};const{x:o,y:l}=this.scales,h=function(t,e){const{x:n,y:i}=e;let[r,s]=i.getOptions().range||[0,0];return s>r&&([s,r]=[r,s]),t.map(t=>t.map((t,e)=>[n.map(e),Ht(i.map(t),s,r)]))}(a,{type:"line",x:o,y:l});let c=[];if(i){const{baseline:t}=this;c=e?n?function(t,e,n){const i=[];for(let r=t.length-1;r>=0;r-=1){const s=t[r],a=ee(s);let o;if(0===r)o=ne(a,e,n);else{const e=ee(t[r-1],!0),n=s[0];e[0][0]="L",o=[...a,...e,["M",...n],["Z"]]}i.push(o)}return i}(h,s,t):function(t,e,n){const i=[];for(let r=t.length-1;r>=0;r-=1){const s=te(t[r]);let a;if(0===r)a=ne(s,e,n);else{const e=te(t[r-1],!0);e[0][0]="L",a=[...s,...e,["Z"]]}i.push(a)}return i}(h,s,t):function(t,e,n,i){return t.map(t=>ne(e?ee(t):te(t),n,i))}(h,n,s,t)}return{lines:h.map((t,e)=>({stroke:this.getColor(e),d:n?ee(t):te(t),...r})),areas:c.map((t,e)=>({d:t,fill:this.getColor(e),...i}))}}get columnsStyle(){const t=B(this.attributes,"column"),{isStack:e,type:n,scale:i}=this.attributes;if("column"!==n)throw new Error("columnsStyle can only be used in column type");const{height:r}=this.containerShape;let{rawData:s}=this;if(!s)return{columns:[]};e&&(s=le(s));const{x:a,y:o}=this.createScales(s),[l,h]=oe(s),c=new Lt({domain:[0,h-(l>0?0:l)],range:[0,r*i]}),u=a.getBandWidth(),{rawData:d}=this;return{columns:s.map((n,i)=>n.map((n,r)=>{const l=u/s.length;return{fill:this.getColor(i),...t,...e?{x:a.map(r),y:o.map(n),width:u,height:c.map(d[i][r])}:{x:a.map(r)+l*i,y:n>=0?o.map(n):o.map(0),width:l,height:c.map(Math.abs(n))}}}))}}constructor(t){super(t,{type:"line",x:0,y:0,width:200,height:20,isStack:!1,color:["#83daad","#edbf45","#d2cef9","#e290b3","#6f63f4"],smooth:!0,lineLineWidth:1,areaOpacity:0,isGroup:!1,columnLineWidth:1,columnStroke:"#fff",scale:1,spacing:0})}render(t,e){O(e,".container","rect").attr("className","container").node();const{type:n,x:i,y:r}=t,s=`spark${n}`,a={x:i,y:r,..."line"===n?this.linesStyle:this.columnsStyle};T(e).selectAll(".spark").data([n]).join(t=>t.append(t=>"line"===t?new Gt({className:s,style:a}):new Ft({className:s,style:a})).attr("className",`spark ${s}`),t=>t.update(a),t=>t.remove())}getColor(t){const{color:e}=this.attributes;return g(e)?e[t%e.length]:N(e)?e.call(null,t):e}createScales(t){const{type:e,scale:n,range:i=[],spacing:r}=this.attributes,{width:s,height:a}=this.containerShape,[o,l]=oe(t),h=new Lt({domain:[i[0]??o,i[1]??l],range:[a,a*(1-n)]});return"line"===e?{type:e,x:new Lt({domain:[0,t[0].length-1],range:[0,s]}),y:h}:{type:e,x:new zt({domain:t[0].map((t,e)=>e),range:[0,s],paddingInner:r,paddingOuter:r/2,align:.5}),y:h}}}function ce(t){if(!t)return{enter:!1,update:!1,exit:!1};const e=["enter","update","exit"],n=Object.fromEntries(Object.entries(t).filter(t=>{let[n]=t;return!e.includes(n)}));return Object.fromEntries(e.map(e=>function(t){return"boolean"!=typeof t&&"enter"in t&&"update"in t&&"exit"in t}(t)?!1===t[e]?[e,!1]:[e,{...t[e],...n}]:[e,n]))}function ue(t,e){t?t.finished.then(e):e()}function de(t,e){"update"in t?t.update(e):t.attr(e)}function pe(t,e,n){return 0===e.length?null:n?t.animate(e,n):(de(t,{style:e.slice(-1)[0]}),null)}function ge(e,n,i){const r={},s={};return Object.entries(n).forEach(n=>{let[i,a]=n;if(!t(a)){const t=e.style[i]||e.parsedStyle[i]||0;t!==a&&(r[i]=t,s[i]=a)}}),i?pe(e,[r,s],{fill:"both",...i}):(de(e,s),null)}function fe(t,e){return+t.toPrecision(e)}function me(t){return t.toLocaleString()}function ye(t){return t.toExponential()}function be(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return Math.abs(t)<1e3?String(t):`${fe(t/1e3,e).toLocaleString()}K`}const xe=(t,e,n)=>t<0&&Number.isFinite(t)?e:n,ve=(t,e,n)=>t>0&&Number.isFinite(t)?e:n,we=(t,e)=>t*e,ke=(t,e)=>t/2+(e||0)/2;function Se(t){const{canvas:e,touches:n,offsetX:i,offsetY:r}=t;if(e){const{x:t,y:n}=e;return[t,n]}if(n){const{clientX:t,clientY:e}=n[0];return[t,e]}return i&&r?[i,r]:[0,0]}const Ae=(t,e)=>{const n=t=>`${e}-${t}`,i=Object.fromEntries(Object.entries(t).map(t=>{let[e,i]=t;const r=n(i);return[e,{name:r,class:`.${r}`,id:`#${r}`,toString:()=>r}]}));return Object.assign(i,{prefix:n}),i},Le={fill:"#fff",lineWidth:1,radius:2,size:10,stroke:"#bfbfbf",strokeOpacity:1,zIndex:0},Be={fill:"#000",fillOpacity:.45,fontSize:12,textAlign:"center",textBaseline:"middle",zIndex:1},Ce={x:0,y:0,orientation:"horizontal",showLabel:!0,type:"start"},Me=Ae({foreground:"foreground",handle:"handle",selection:"selection",sparkline:"sparkline",sparklineGroup:"sparkline-group",track:"track",brushArea:"brush-area"},"slider"),Ee=Ae({labelGroup:"label-group",label:"label",iconGroup:"icon-group",icon:"icon",iconRect:"icon-rect",iconLine:"icon-line"},"handle");class Pe extends b{render(t,e){const{x:n,y:i,size:r=10,radius:s=r/4,orientation:a,...o}=t,l=r,h=2.4*l,c=T(e).maybeAppendByClassName(Ee.iconRect,"rect").styles({...o,width:l,height:h,radius:s,x:n-l/2,y:i-h/2,transformOrigin:"center"}),u=n+1/3*l-l/2,d=n+2/3*l-l/2,p=i+1/4*h-h/2,g=i+3/4*h-h/2;c.maybeAppendByClassName(`${Ee.iconLine}-1`,"line").styles({x1:u,x2:u,y1:p,y2:g,...o}),c.maybeAppendByClassName(`${Ee.iconLine}-2`,"line").styles({x1:d,x2:d,y1:p,y2:g,...o}),"vertical"===a&&(c.node().style.transform="rotate(90)")}}class $e extends b{label;icon;constructor(t){super(t,Ce)}renderLabel(t){const{x:e,y:n,showLabel:i}=this.attributes,{x:r=0,y:s=0,transform:a,transformOrigin:o,...l}=B(this.attributes,"label"),[h,c]=M(l,[]),u=T(t).maybeAppendByClassName(Ee.labelGroup,"g").styles(c),{text:d,...p}={...Be,...h};z(!!i,u,t=>{this.label=t.maybeAppendByClassName(Ee.label,"text").styles({...p,x:e+r,y:n+s,transform:a,transformOrigin:o,text:`${d}`}),this.label.on("mousedown",t=>{t.stopPropagation()}),this.label.on("touchstart",t=>{t.stopPropagation()})})}renderIcon(t){const{x:e,y:n,orientation:i,type:r}=this.attributes,s={x:e,y:n,orientation:i,...Le,...B(this.attributes,"icon")},{iconShape:a=()=>new Pe({style:s})}=this.attributes;T(t).maybeAppendByClassName(Ee.iconGroup,"g").selectAll(Ee.icon.class).data([a]).join(t=>t.append("string"==typeof a?a:()=>a(r)).attr("className",Ee.icon.name),t=>t.update(s),t=>t.remove())}render(t,e){this.renderIcon(e),this.renderLabel(e)}}class Te extends b{static tag="slider";range=[0,1];get values(){return this.attributes.values}set values(t){this.attributes.values=this.clampValues(t)}trackShape;brushArea;foregroundGroup;selectionShape;startHandle;endHandle;selectionStartPos;selectionWidth;prevPos;target;get sparklineStyle(){const{orientation:t}=this.attributes;if("horizontal"!==t)return null;const e=B(this.attributes,"sparkline");return{zIndex:0,...this.availableSpace,...e}}get shape(){const{trackLength:t,trackSize:e}=this.attributes,[n,i]=this.getOrientVal([[t,e],[e,t]]);return{width:n,height:i}}get availableSpace(){const{x:t,y:e,padding:n}=this.attributes,[i,r,s,a]=P(n),{width:o,height:l}=this.shape;return{x:a,y:i,width:o-(a+r),height:l-(i+s)}}constructor(t){super(t,{x:0,y:0,animate:{duration:100,fill:"both"},brushable:!0,formatter:t=>t.toString(),handleSpacing:2,orientation:"horizontal",padding:0,autoFitLabel:!0,scrollable:!0,selectionFill:"#5B8FF9",selectionFillOpacity:.45,selectionZIndex:2,showHandle:!0,showLabel:!0,slidable:!0,trackFill:"#416180",trackLength:200,trackOpacity:.05,trackSize:20,trackZIndex:-1,values:[0,1],type:"range",selectionType:"select",handleIconOffset:0,...C(Ce,"handle"),...C(Le,"handleIcon"),...C(Be,"handleLabel")}),this.selectionStartPos=0,this.selectionWidth=0,this.prevPos=0,this.target=""}getValues(){return this.values}setValues(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[0,0],e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.attributes.values=t;const n=!1!==e&&this.attributes.animate;this.updateSelectionArea(n),this.updateHandlesPosition(n)}updateSelectionArea(t){const e=this.calcSelectionArea();this.foregroundGroup.selectAll(Me.selection.class).each(function(n,i){ge(this,e[i],t)})}updateHandlesPosition(t){this.attributes.showHandle&&(this.startHandle&&ge(this.startHandle,this.getHandleStyle("start"),t),this.endHandle&&ge(this.endHandle,this.getHandleStyle("end"),t))}innerSetValues(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[0,0],e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.values,i=this.clampValues(t);this.attributes.values=i,this.setValues(i),e&&this.onValueChange(n)}renderTrack(t){const{x:e,y:n}=this.attributes,i=B(this.attributes,"track");this.trackShape=T(t).maybeAppendByClassName(Me.track,"rect").styles({x:e,y:n,...this.shape,...i})}renderBrushArea(t){const{x:e,y:n,brushable:i}=this.attributes;this.brushArea=T(t).maybeAppendByClassName(Me.brushArea,"rect").styles({x:e,y:n,fill:"transparent",cursor:i?"crosshair":"default",...this.shape})}renderSparkline(t){const{x:e,y:n,orientation:i}=this.attributes;z("horizontal"===i,T(t).maybeAppendByClassName(Me.sparklineGroup,"g"),t=>{const i={...this.sparklineStyle,x:e,y:n};t.maybeAppendByClassName(Me.sparkline,()=>new he({style:i})).update(i)})}renderHandles(){const{showHandle:t,type:e}=this.attributes,n=t?"range"===e?["start","end"]:["end"]:[],i=this;this.foregroundGroup?.selectAll(Me.handle.class).data(n.map(t=>({type:t})),t=>t.type).join(t=>t.append(t=>{let{type:e}=t;return new $e({style:this.getHandleStyle(e)})}).each(function(t){let{type:e}=t;this.attr("class",`${Me.handle.name} ${e}-handle`),i[`${e}Handle`]=this,this.addEventListener("pointerdown",i.onDragStart(e))}),t=>t.each(function(t){let{type:e}=t;this.update(i.getHandleStyle(e))}),t=>t.each(t=>{let{type:e}=t;i[`${e}Handle`]=void 0}).remove())}renderSelection(t){const{x:e,y:n,type:i,selectionType:r}=this.attributes;this.foregroundGroup=T(t).maybeAppendByClassName(Me.foreground,"g");const s=B(this.attributes,"selection"),a=t=>t.style("visibility",t=>t.show?"visible":"hidden").style("cursor",t=>"select"===r?"grab":"invert"===r?"crosshair":"default").styles({...s,transform:`translate(${e}, ${n})`}),o=this;this.foregroundGroup.selectAll(Me.selection.class).data("value"===i?[]:this.calcSelectionArea().map((t,e)=>({style:{...t},index:e,show:"select"===r?1===e:1!==e})),t=>t.index).join(t=>t.append("rect").attr("className",Me.selection.name).call(a).each(function(t,e){1===e?(o.selectionShape=T(this),this.on("pointerdown",t=>{this.attr("cursor","grabbing"),o.onDragStart("selection")(t)}),o.dispatchCustomEvent(this,"pointerenter","selectionMouseenter"),o.dispatchCustomEvent(this,"pointerleave","selectionMouseleave"),o.dispatchCustomEvent(this,"click","selectionClick"),this.addEventListener("pointerdown",()=>{this.attr("cursor","grabbing")}),this.addEventListener("pointerup",()=>{this.attr("cursor","pointer")}),this.addEventListener("pointerover",()=>{this.attr("cursor","pointer")})):this.on("pointerdown",o.onDragStart("track"))}),t=>t.call(a),t=>t.remove()),this.updateSelectionArea(!1),this.renderHandles()}render(t,e){this.renderTrack(e),this.renderSparkline(e),this.renderBrushArea(e),this.renderSelection(e)}clampValues(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;const[n,i]=this.range,[r,s]=this.getValues().map(t=>fe(t,e)),a=Array.isArray(t)?t:[r,t??s];let[o,l]=(a||[r,s]).map(t=>fe(t,e));if("value"===this.attributes.type)return[0,Ht(l,n,i)];o>l&&([o,l]=[l,o]);const h=l-o;return h>i-n?[n,i]:o<n?r===n&&s===l?[n,l]:[n,h+n]:l>i?s===i&&r===o?[o,i]:[i-h,i]:[o,l]}calcSelectionArea(t){const[e,n]=this.clampValues(t),{x:i,y:r,width:s,height:a}=this.availableSpace;return this.getOrientVal([[{y:r,height:a,x:i,width:e*s},{y:r,height:a,x:e*s+i,width:(n-e)*s},{y:r,height:a,x:n*s,width:(1-n)*s}],[{x:i,width:s,y:r,height:e*a},{x:i,width:s,y:e*a+r,height:(n-e)*a},{x:i,width:s,y:n*a,height:(1-n)*a}]])}calcHandlePosition(t){const{handleIconOffset:e}=this.attributes,{x:n,y:i,width:r,height:s}=this.availableSpace,[a,o]=this.clampValues(),l="start"===t?-e:e,h=("start"===t?a:o)*this.getOrientVal([r,s])+l;return{x:n+this.getOrientVal([h,r/2]),y:i+this.getOrientVal([s/2,h])}}inferTextStyle(t){const{orientation:e}=this.attributes;return"horizontal"===e?{}:"start"===t?{transformOrigin:"left center",transform:"rotate(90)",textAlign:"start"}:"end"===t?{transformOrigin:"right center",transform:"rotate(90)",textAlign:"end"}:{}}calcHandleText(t){const{type:e,orientation:n,formatter:i,autoFitLabel:r}=this.attributes,s=B(this.attributes,"handle"),a=B(s,"label"),{spacing:o}=s,l=this.getHandleSize(),h=this.clampValues(),c=i("start"===t?h[0]:h[1]),u=new x({style:{...a,...this.inferTextStyle(t),text:c}}),{width:d,height:p}=u.getBBox();if(u.destroy(),!r){if("value"===e)return{text:c,x:0,y:-p-o};const i=o+l+("horizontal"===n?d/2:0);return{text:c,["horizontal"===n?"x":"y"]:"start"===t?-i:i}}let g=0,f=0;const{width:m,height:y}=this.availableSpace,{x:b,y:v,width:w,height:k}=this.calcSelectionArea()[1],S=o+l;if("horizontal"===n){const e=S+d/2;g="start"===t?b-S-d>0?-e:e:m-b-w-S>d?e:-e}else{const e=p+S;f="start"===t?v-l>p?-e:S:y-(v+k)-l>p?e:-S}return{x:g,y:f,text:c}}getHandleLabelStyle(t){return{...B(this.attributes,"handleLabel"),...this.calcHandleText(t),...this.inferTextStyle(t)}}getHandleIconStyle(){const{handleIconShape:t}=this.attributes,e=B(this.attributes,"handleIcon");return{cursor:this.getOrientVal(["ew-resize","ns-resize"]),shape:t,size:this.getHandleSize(),...e}}getHandleStyle(t){const{x:e,y:n,showLabel:i,showLabelOnInteraction:r,orientation:s}=this.attributes,{x:a,y:o}=this.calcHandlePosition(t),l=this.calcHandleText(t);let h=i;return!i&&r&&(h=!!this.target),{...C(this.getHandleIconStyle(),"icon"),...C({...this.getHandleLabelStyle(t),...l},"label"),transform:`translate(${a+e}, ${o+n})`,orientation:s,showLabel:h,type:t,zIndex:3}}getHandleSize(){const{handleIconSize:t,width:e,height:n}=this.attributes;return t||Math.floor((this.getOrientVal([+n,+e])+4)/2.4)}getOrientVal(t){let[e,n]=t;const{orientation:i}=this.attributes;return"horizontal"===i?e:n}setValuesOffset(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{type:i}=this.attributes,[r,s]=this.getValues(),a=[r+("range"===i?t:0),s+e].sort();n?this.setValues(a):this.innerSetValues(a,!0)}getRatio(t){const{width:e,height:n}=this.availableSpace;return t/this.getOrientVal([e,n])}dispatchCustomEvent(t,n,i){t.on(n,t=>{t.stopPropagation(),this.dispatchEvent(new e.CustomEvent(i,{detail:t}))})}bindEvents(){this.addEventListener("wheel",this.onScroll);const t=this.brushArea;this.dispatchCustomEvent(t,"click","trackClick"),this.dispatchCustomEvent(t,"pointerenter","trackMouseenter"),this.dispatchCustomEvent(t,"pointerleave","trackMouseleave"),t.on("pointerdown",this.onDragStart("track"))}onScroll(t){const{scrollable:e}=this.attributes;if(e){const{deltaX:e,deltaY:n}=t,i=n||e,r=this.getRatio(i);this.setValuesOffset(r,r,!0)}}onDragStart=t=>e=>{e.stopPropagation(),this.target=t,this.prevPos=this.getOrientVal(Se(e));const{x:n,y:i}=this.availableSpace,{x:r,y:s}=this.getBBox();this.selectionStartPos=this.getRatio(this.prevPos-this.getOrientVal([n,i])-this.getOrientVal([+r,+s])),this.selectionWidth=0,document.addEventListener("pointermove",this.onDragging),document.addEventListener("pointerup",this.onDragEnd)};onDragging=t=>{const{slidable:e,brushable:n,type:i}=this.attributes;t.stopPropagation();const r=this.getOrientVal(Se(t)),s=r-this.prevPos;if(!s)return;const a=this.getRatio(s);switch(this.target){case"start":e&&this.setValuesOffset(a);break;case"end":e&&this.setValuesOffset(0,a);break;case"selection":e&&this.setValuesOffset(a,a);break;case"track":if(!n)return;this.selectionWidth+=a,"range"===i?this.innerSetValues([this.selectionStartPos,this.selectionStartPos+this.selectionWidth].sort(),!0):this.innerSetValues([0,this.selectionStartPos+this.selectionWidth],!0)}this.prevPos=r};onDragEnd=()=>{document.removeEventListener("pointermove",this.onDragging),document.removeEventListener("pointermove",this.onDragging),document.removeEventListener("pointerup",this.onDragEnd),this.target="",this.updateHandlesPosition(!1)};onValueChange=t=>{const{onChange:n,type:i}=this.attributes,r="range"===i?t:t[1],s="range"===i?this.getValues():this.getValues()[1],a=new e.CustomEvent("valuechange",{detail:{oldValue:r,value:s}});this.dispatchEvent(a),n?.(s)}}class Oe extends b{static tag="scrollbar";slider;range=[0,1];get padding(){const{padding:t}=this.attributes;return P(t)}constructor(t){super(t,{x:0,y:0,isRound:!0,orientation:"vertical",padding:[2,2,2,2],scrollable:!0,slidable:!0,thumbCursor:"default",trackSize:10,value:0})}get value(){const{value:t}=this.attributes,[e,n]=this.range;return Ht(t,e,n)}get trackLength(){const{viewportLength:t,trackLength:e=t}=this.attributes;return e}get availableSpace(){const{trackSize:t}=this.attributes,e=this.trackLength,[n,i,r,s]=this.padding,[a,o]=this.getOrientVal([[e,t],[t,e]]);return{x:s,y:n,width:+a-(s+i),height:+o-(n+r)}}get trackRadius(){const{isRound:t,trackSize:e}=this.attributes;return t?e/2:0}get thumbRadius(){const{isRound:t,thumbRadius:e}=this.attributes;if(!t)return 0;const{width:n,height:i}=this.availableSpace;return e||this.getOrientVal([i,n])/2}getValues(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.value;const{viewportLength:e,contentLength:n}=this.attributes,i=e/n,[r,s]=this.range,a=t*(s-r-i);return[a,a+i]}getValue(){return this.value}renderSlider(t){const{x:e,y:n,orientation:i,trackSize:r,padding:s,slidable:a}=this.attributes,o=B(this.attributes,"track"),l=B(this.attributes,"thumb"),h={x:e,y:n,brushable:!1,orientation:i,padding:s,selectionRadius:this.thumbRadius,showHandle:!1,slidable:a,trackLength:this.trackLength,trackRadius:this.trackRadius,trackSize:r,values:this.getValues(),...C(o,"track"),...C(l,"selection")};this.slider=T(t).maybeAppendByClassName("scrollbar",()=>new Te({style:h})).update(h).node()}render(t,e){this.renderSlider(e)}setValue(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{value:n}=this.attributes,[i,r]=this.range;this.slider.setValues(this.getValues(Ht(t,i,r)),e),this.onValueChange(n)}onValueChange=t=>{const{value:n}=this.attributes;if(t===n)return;const i={detail:{oldValue:t,value:n}};this.dispatchEvent(new e.CustomEvent("scroll",i)),this.dispatchEvent(new e.CustomEvent("valuechange",i))};bindEvents(){this.slider.addEventListener("trackClick",t=>{t.stopPropagation(),this.onTrackClick(t.detail)}),this.onHover()}getOrientVal(t){const{orientation:e}=this.attributes;return"horizontal"===e?t[0]:t[1]}onTrackClick=t=>{const{slidable:e}=this.attributes;if(!e)return;const[n,i]=this.getLocalPosition(),[r,,,s]=this.padding,a=this.getOrientVal([n+s,i+r]),o=(this.getOrientVal(Se(t))-a)/this.trackLength;this.setValue(o,!0)};onHover(){this.slider.addEventListener("selectionMouseenter",this.onThumbMouseenter),this.slider.addEventListener("trackMouseenter",this.onTrackMouseenter),this.slider.addEventListener("selectionMouseleave",this.onThumbMouseleave),this.slider.addEventListener("trackMouseleave",this.onTrackMouseleave)}onThumbMouseenter=t=>{this.dispatchEvent(new e.CustomEvent("thumbMouseenter",{detail:t.detail}))};onTrackMouseenter=t=>{this.dispatchEvent(new e.CustomEvent("trackMouseenter",{detail:t.detail}))};onThumbMouseleave=t=>{this.dispatchEvent(new e.CustomEvent("thumbMouseleave",{detail:t.detail}))};onTrackMouseleave=t=>{this.dispatchEvent(new e.CustomEvent("trackMouseleave",{detail:t.detail}))}}function Ne(t,e){if(t.length<=e)return t;const n=Math.floor(t.length/e),i=[];for(let e=0;e<t.length;e+=n)i.push(t[e]);return i}const ze={data:[],animate:{enter:!1,update:{duration:100,easing:"ease-in-out-sine",fill:"both"},exit:{duration:100,fill:"both"}},showArrow:!0,showGrid:!0,showLabel:!0,showLine:!0,showTick:!0,showTitle:!0,showTrunc:!1,dataThreshold:100,lineLineWidth:1,lineStroke:"black",crossPadding:10,titleFill:"black",titleFontSize:12,titlePosition:"lb",titleSpacing:0,titleTextAlign:"center",titleTextBaseline:"middle",lineArrow:()=>new e.Path({style:{d:[["M",10,10],["L",-10,0],["L",10,-10],["L",0,0],["L",10,10],["Z"]],fill:"black",transformOrigin:"center"}}),labelAlign:"parallel",labelDirection:"positive",labelFontSize:12,labelSpacing:0,gridConnect:"line",gridControlAngles:[],gridDirection:"positive",gridLength:0,gridType:"segment",lineArrowOffset:15,lineArrowSize:10,tickDirection:"positive",tickLength:5,tickLineWidth:1,tickStroke:"black",labelOverlap:[]},_e=(K({},ze,{style:{type:"arc"}}),K({},ze,{style:{}}),Ae({mainGroup:"main-group",gridGroup:"grid-group",grid:"grid",lineGroup:"line-group",line:"line",tickGroup:"tick-group",tick:"tick",tickItem:"tick-item",labelGroup:"label-group",label:"label",labelItem:"label-item",titleGroup:"title-group",title:"title",lineFirst:"line-first",lineSecond:"line-second"},"axis"));function Ie(t){return t*Math.PI/180}function Fe(t){return Number((180*t/Math.PI).toPrecision(5))}function Ge(t,e){return N(t)?t(...e):t}function He(t,e){return t.style.opacity||(t.style.opacity=1),ge(t,{opacity:0},e)}const Ve=["$el","cx","cy","d","dx","dy","fill","fillOpacity","filter","fontFamily","fontSize","fontStyle","fontVariant","fontWeight","height","img","increasedLineWidthForHitTesting","innerHTML","isBillboard","billboardRotation","isSizeAttenuation","isClosed","isOverflowing","leading","letterSpacing","lineDash","lineHeight","lineWidth","markerEnd","markerEndOffset","markerMid","markerStart","markerStartOffset","maxLines","metrics","miterLimit","offsetX","offsetY","opacity","path","points","r","radius","rx","ry","shadowColor","src","stroke","strokeOpacity","text","textAlign","textBaseline","textDecorationColor","textDecorationLine","textDecorationStyle","textOverflow","textPath","textPathSide","textPathStartOffset","transform","transformOrigin","visibility","width","wordWrap","wordWrapWidth","x","x1","x2","y","y1","y2","z1","z2","zIndex"];function We(t){return Ve.includes(t)}function De(t){const e={};for(const n in t)We(n)&&(e[n]=t[n]);return e}const je=Ae({lineGroup:"line-group",line:"line",regionGroup:"region-group",region:"region"},"grid");function Re(t){return t.reduce((t,e,n)=>(t.push([0===n?"M":"L",...e]),t),[])}function Ye(t,e,n){return"surround"===e.type?function(t,e,n){const{connect:i="line",center:r}=e;if("line"===i)return Re(t);if(!r)return[];const s=Zt(t[0],r),a=n?0:1;return t.reduce((t,e,n)=>(0===n?t.push(["M",...e]):t.push(["A",s,s,0,0,a,...e]),t),[])}(t,e,n):Re(t)}function qe(t,e,n){const{type:i,connect:r,center:s,closed:a}=n,o=a?[["Z"]]:[],[l,h]=[Ye(t,n),Ye(e.slice().reverse(),n,!0)],[c,u]=[t[0],e.slice(-1)[0]],d=(t,e)=>[l,t,h,e,o].flat();if("line"===r||"surround"===i)return d([["L",...u]],[["L",...c]]);if(!s)throw new Error("Arc grid need to specified center");const[p,g]=[Zt(u,s),Zt(c,s)];return d([["A",p,p,0,0,1,...u],["L",...u]],[["A",g,g,0,0,0,...c],["L",...c]])}class Ke extends b{render(t,e){const{type:n,center:i,areaFill:r,closed:s,...a}=t,o=function(t){const{data:e=[],closed:n}=t;return n?e.map(t=>{const{points:e}=t,[n]=e;return{...t,points:[...e,n]}}):e}(t),l=T(e).maybeAppendByClassName(je.lineGroup,"g"),h=T(e).maybeAppendByClassName(je.regionGroup,"g"),c=function(t,e,n,i){const{animate:r,isBillboard:s}=n,a=e.map((t,e)=>({id:t.id||`grid-line-${e}`,d:Ye(t.points,n)}));return t.selectAll(je.line.class).data(a,t=>t.id).join(t=>t.append("path").each(function(t,e){const n=Ge(De({d:t.d,...i}),[t,e,a]);this.attr({class:je.line.name,stroke:"#D9D9D9",lineWidth:1,lineDash:[4,4],isBillboard:s,...n})}),t=>t.transition(function(t,e){return ge(this,Ge(De({d:t.d,...i}),[t,e,a]),r.update)}),t=>t.transition(function(){const t=He(this,r.exit);return ue(t,()=>this.remove()),t})).transitions()}(l,o,t,a),u=function(t,e,n){const{animate:i,connect:r,areaFill:s}=n;if(e.length<2||!s||!r)return[];const a=Array.isArray(s)?s:[s,"transparent"],o=t=>a[t%a.length],l=[];for(let t=0;t<e.length-1;t++){const[i,r]=[e[t].points,e[t+1].points],s=qe(i,r,n);l.push({d:s,fill:o(t)})}return t.selectAll(je.region.class).data(l,(t,e)=>e).join(t=>t.append("path").each(function(t,e){const n=Ge(t,[t,e,l]);this.attr(n)}).attr("className",je.region.name),t=>t.transition(function(t,e){return ge(this,Ge(t,[t,e,l]),i.update)}),t=>t.transition(function(){const t=He(this,i.exit);return ue(t,()=>this.remove()),t})).transitions()}(h,o,t);return[...c,...u]}}const Ze=function(t,e,n){for(var i=0,r=W(e)?e.split("."):e;t&&i<r.length;)t=t[r[i++]];return void 0===t||i<r.length?n:t},Ue=function(t,e){return function(n){return t*(1-n)+e*n}};function Xe(t,e){const n=e?e.length:0,i=t?Math.min(n,t.length):0;return function(r){const s=new Array(i),a=new Array(n);let o=0;for(o=0;o<i;++o)s[o]=Qe(t[o],e[o]);for(;o<n;++o)a[o]=e[o];for(o=0;o<i;++o)a[o]=s[o](r);return a}}function Je(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n={},i={};return Object.entries(e).forEach(e=>{let[r,s]=e;r in t?n[r]=Qe(t[r],s):i[r]=s}),function(t){return Object.entries(n).forEach(e=>{let[n,r]=e;return i[n]=r(t)}),i}}function Qe(t,e){return"number"==typeof t&&"number"==typeof e?Ue(t,e):Array.isArray(t)&&Array.isArray(e)?Xe(t,e):"object"==typeof t&&"object"==typeof e?Je(t,e):e=>t}function tn(t,e,n,i){if(!i)return t.attr("__keyframe_data__",n),null;const{duration:r=0}=i,s=Qe(e,n),a=Math.ceil(+r/16),o=new Array(a).fill(0).map((t,e,n)=>({__keyframe_data__:s(e/(n.length-1))}));return t.animate(o,{fill:"both",...i})}function en(t){return"function"==typeof t?t():W(t)||E(t)?new x({style:{text:String(t)}}):t}function nn(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const{width:i,height:r}=t.getBBox(),s=e/Math.max(i,r);return n&&(t.style.transform=`scale(${s})`),s}function rn(t,e){const n={},i=Array.isArray(e)?e:[e];for(const e in t)i.includes(e)||(n[e]=t[e]);return n}function sn(t,e){return Object.fromEntries(Object.entries(t).map(t=>{let[n,i]=t;return[n,Ge(i,e)]}))}function an(t,e){return e&&N(e)?t.filter(e):t}function on(t,e){const{startAngle:n,endAngle:i}=e;return(i-n)*t+n}function ln(t,e){if("linear"===e.type){const{startPos:[t,n],endPos:[i,r]}=e,[s,a]=[i-t,r-n];return Ut([s,a])}const n=Ie(on(t,e));return[-Math.sin(n),Math.cos(n)]}function hn(t,e,n){return Jt(ln(t,n),"positive"!==e)}function cn(t,e){return hn(t,e.labelDirection,e)}function un(t,e){return"linear"===e.type?function(t,e){const{startPos:[n,i],endPos:[r,s]}=e,[a,o]=[r-n,s-i];return[n+a*t,i+o*t]}(t,e):function(t,e){const{radius:n,center:[i,r]}=e,s=Ie(on(t,e));return[i+n*Math.cos(s),r+n*Math.sin(s)]}(t,e)}function dn(t){return 0===ln(0,t)[1]}function pn(t){return 0===ln(0,t)[0]}function gn(t,e){return e-t===360}function fn(t,e,n,i,r){const s=e-t,[a,o]=[r,r],[l,h]=[Ie(t),Ie(e)],c=t=>[n+r*Math.cos(t),i+r*Math.sin(t)],[u,d]=c(l),[p,g]=c(h);if(gn(t,e)){const t=(h+l)/2,[e,n]=c(t);return[["M",u,d],["A",a,o,0,1,0,e,n],["A",a,o,0,1,0,p,g]]}return`M${u},${d},A${a},${o},0,${s>180?1:0},${t>e?0:1},${p},${g}`}function mn(t){const[[e,n],[i,r]]=t;return{x1:e,y1:n,x2:i,y2:r}}function yn(t,e,n){const{type:i}=e;let r;const s=B(e,"line");return r="linear"===i?function(t,e,n,i){const{showTrunc:r,startPos:s,endPos:a,truncRange:o,lineExtension:l}=e,[[h,c],[u,d]]=[s,a],[p,g,f,m]=l?function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[0,0];const[[i,r],[s,a],[o,l]]=[t,e,n],[h,c]=[s-i,a-r],u=Math.sqrt(h**2+c**2),[d,p]=[-o/u,l/u];return[d*h,d*c,p*h,p*c]}(s,a,l):new Array(4).fill(0),y=e=>t.selectAll(_e.line.class).data(e,(t,e)=>e).join(t=>t.append("line").attr("className",t=>`${_e.line.name} ${t.className}`).styles(n).transition(function(t){return ge(this,mn(t.line),!1)}),t=>t.styles(n).transition(function(t){let{line:e}=t;return ge(this,mn(e),i.update)}),t=>t.remove()).transitions();if(!r||!o)return y([{line:[[h+p,c+g],[u+f,d+m]],className:_e.line.name}]);const[b,x]=o,v=u-h,w=d-c,[k,S]=[h+v*b,c+w*b],[A,L]=[h+v*x,c+w*x],B=y([{line:[[h+p,c+g],[k,S]],className:_e.lineFirst.name},{line:[[A,L],[u+f,d+m]],className:_e.lineSecond.name}]);return function(t,e){let{truncRange:n,truncShape:i,lineExtension:r}=e}(0,e),B}(t,e,rn(s,"arrow"),n):function(t,e,n,i){const{startAngle:r,endAngle:s,center:a,radius:o}=e;return t.selectAll(_e.line.class).data([{d:fn(r,s,...a,o)}],(t,e)=>e).join(t=>t.append("path").attr("className",_e.line.name).styles(e).styles({d:t=>t.d}),t=>t.transition(function(){const t=tn(this,function(t){const{startAngle:e,endAngle:n,center:i,radius:r}=t.attributes;return[e,n,...i,r]}(this),[r,s,...a,o],i.update);if(t){const e=()=>{const t=Ze(this.attributes,"__keyframe_data__");this.style.d=fn(...t)};t.onframe=e,t.onfinish=e}return t}).styles(e),t=>t.remove()).styles(n).transitions()}(t,e,rn(s,"arrow"),n),function(t,e,n,i){const{showArrow:r,showTrunc:s,lineArrow:a,lineArrowOffset:o,lineArrowSize:l}=n;let h;if(h="arc"===e?t.select(_e.line.class):s?t.select(_e.lineSecond.class):t.select(_e.line.class),!r||!a||"arc"===n.type&&gn(n.startAngle,n.endAngle)){const t=h.node();return void(t&&(t.style.markerEnd=void 0))}const c=en(a);c.attr(i),nn(c,l,!0),h.style("markerEnd",c).style("markerEndOffset",-o)}(t,i,e,s),r}function bn(t){const{type:e,gridCenter:n}=t;return"linear"===e?n:n||t.center}function xn(t,e,n,i){const r=B(n,"grid"),{type:s,areaFill:a}=r,o=bn(n),l=an(e,n.gridFilter),h="segment"===s?function(t,e){const{gridLength:n}=e;return t.map((t,i)=>{let{value:r}=t;const[s,a]=un(r,e),[o,l]=jt(function(t,e){return hn(t,e.gridDirection,e)}(r,e),n);return{id:i,points:[[s,a],[s+o,a+l]]}})}(l,n):function(t,e){const n=e.gridControlAngles,i=bn(e);if(!i)throw new Error("grid center is not provide");if(t.length<2)throw new Error("Invalid grid data");if(!n||0===n.length)throw new Error("Invalid gridControlAngles");const[r,s]=i;return t.map((t,i)=>{let{value:a}=t;const[o,l]=un(a,e),[h,c]=[o-r,l-s],u=[];return n.forEach(t=>{const e=Ie(t),[n,i]=[Math.cos(e),Math.sin(e)],a=h*n-c*i+r,o=h*i+c*n+s;u.push([a,o])}),{points:u,id:i}})}(l,n),c={...r,center:o,areaFill:N(a)?l.map((t,e)=>Ge(a,[t,e,l])):a,animate:i,data:h};return t.selectAll(_e.grid.class).data([1]).join(t=>t.append(()=>new Ke({style:c})).attr("className",_e.grid.name),t=>t.transition(function(){return this.update(c)}),t=>t.remove()).transitions()}function vn(t,e,n){return!((arguments.length>3&&void 0!==arguments[3]&&!arguments[3]||t!==e)&&!(arguments.length>4&&void 0!==arguments[4]&&arguments[4]&&t===n))||t>e&&t<n}var wn=new Map;let kn,Sn;function An(t){Sn=t}const Ln=(Bn=(t,n)=>{const{fontSize:i,fontFamily:r,fontWeight:s,fontStyle:a,fontVariant:o}=n;return Sn?Sn(t,i):(kn||(kn=e.runtime.offscreenCanvasCreator.getOrCreateContext(void 0)),kn.font=[a,o,s,`${i}px`,r].join(" "),kn.measureText(t).width)},Cn=(t,e)=>[t,Object.values(e||En(t)).join()].join(""),void 0===(Mn=4096)&&(Mn=128),function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var n=Cn?Cn.apply(this,t):t[0];wn.has(Bn)||wn.set(Bn,function(t){var e,n,i,r=t||1;function s(t,s){++e>r&&(i=n,a(1),++e),n[t]=s}function a(t){e=0,n=Object.create(null),t||(i=Object.create(null))}return a(),{clear:a,has:function(t){return void 0!==n[t]||void 0!==i[t]},get:function(t){var e=n[t];return void 0!==e?e:void 0!==(e=i[t])?(s(t,e),e):void 0},set:function(t,e){void 0!==n[t]?n[t]=e:s(t,e)}}}(Mn));var i=wn.get(Bn);if(i.has(n))return i.get(n);var r=Bn.apply(this,t);return i.set(n,r),r});var Bn,Cn,Mn;const En=t=>{const e=t.style.fontFamily||"sans-serif",n=t.style.fontWeight||"normal",i=t.style.fontStyle||"normal",r=t.style.fontVariant;let s=t.style.fontSize;return s="object"==typeof s?s.value:s,{fontSize:s,fontFamily:e,fontWeight:n,fontStyle:i,fontVariant:r}};function Pn(t){return"text"===t.nodeName?t:"g"===t.nodeName&&1===t.children.length&&"text"===t.children[0].nodeName?t.children[0]:null}function $n(t,e){const n=Pn(t);n&&n.attr(e)}function Tn(t,e){$n(t,{wordWrap:!0,wordWrapWidth:e,maxLines:1,textOverflow:arguments.length>2&&void 0!==arguments[2]?arguments[2]:"..."})}function On(t,e){$n(t,{wordWrap:!0,wordWrapWidth:e,maxLines:arguments.length>2&&void 0!==arguments[2]?arguments[2]:2,textBaseline:arguments.length>3&&void 0!==arguments[3]?arguments[3]:"top"})}function Nn(t,e,n){const{width:i,height:r}=t.getBBox(),[s,a]=[e,n].map((t,e)=>t.includes("%")?parseFloat(t.match(/[+-]?([0-9]*[.])?[0-9]+/)?.[0]||"0")/100*(0===e?i:r):t);return[s,a]}function zn(t,e){if(e)try{const n=/translate\(([+-]*[\d]+[%]*),[ ]*([+-]*[\d]+[%]*)\)/g,i=e.replace(n,(e,n,i)=>`translate(${Nn(t,n,i)})`);t.attr("transform",i)}catch(t){}}class _n{x1;y1;x2;y2;constructor(t,e,n,i){this.set(t,e,n,i)}get left(){return this.x1}get top(){return this.y1}get right(){return this.x2}get bottom(){return this.y2}get width(){return this.defined("x2")&&this.defined("x1")?this.x2-this.x1:void 0}get height(){return this.defined("y2")&&this.defined("y1")?this.y2-this.y1:void 0}rotatedPoints(t,e,n){const{x1:i,y1:r,x2:s,y2:a}=this,o=Math.cos(t),l=Math.sin(t),h=e-e*o+n*l,c=n-e*l-n*o;return[[o*i-l*a+h,l*i+o*a+c],[o*s-l*a+h,l*s+o*a+c],[o*i-l*r+h,l*i+o*r+c],[o*s-l*r+h,l*s+o*r+c]]}set(t,e,n,i){return n<t?(this.x2=t,this.x1=n):(this.x1=t,this.x2=n),i<e?(this.y2=e,this.y1=i):(this.y1=e,this.y2=i),this}defined(t){return this[t]!==Number.MAX_VALUE&&this[t]!==-Number.MAX_VALUE}}function In(t,e){const n=t.getEulerAngles()||0;t.setEulerAngles(0);const{min:[i,r],max:[s,a]}=t.getBounds(),{width:o,height:l}=t.getBBox();let h=l,c=0,u=0,d=i,p=r;const g=Pn(t);if(g){h-=1.5;const t=g.style.textAlign,e=g.style.textBaseline;"center"===t?d=(i+s)/2:"right"!==t&&"end"!==t||(d=s),"middle"===e?p=(r+a)/2:"bottom"===e&&(p=a)}const[f=0,m=0,y=f,b=m]=P(e),x=new _n((c+=i)-b,(u+=r)-f,c+o+m,u+h+y);return t.setEulerAngles(n),x.rotatedPoints(Ie(n),d,p)}function Fn(t,e){return e[0]<=Math.max(t[0][0],t[1][0])&&e[0]<=Math.min(t[0][0],t[1][0])&&e[1]<=Math.max(t[0][1],t[1][1])&&e[1]<=Math.min(t[0][1],t[1][1])}function Gn(t,e,n){const i=(e[1]-t[1])*(n[0]-e[0])-(e[0]-t[0])*(n[1]-e[1]);return 0===i?0:i<0?2:1}function Hn(t,e){const n=Gn(t[0],t[1],e[0]),i=Gn(t[0],t[1],e[1]),r=Gn(e[0],e[1],t[0]),s=Gn(e[0],e[1],t[1]);return n!==i&&r!==s||!(0!==n||!Fn(t,e[0]))||!(0!==i||!Fn(t,e[1]))||!(0!==r||!Fn(e,t[0]))||!(0!==s||!Fn(e,t[1]))}function Vn(t,e){return[[t[0],t[1],t[2],t[3]],[t[2],t[3],t[4],t[5]],[t[4],t[5],t[6],t[7]],[t[6],t[7],t[0],t[1]]].some(t=>function(t,e){const[n,i,r,s]=t,[a,o,l,h]=e,c=r-n,u=s-i,d=l-a,p=h-o,g=c*p-d*u;if(0===g)return!1;const f=g>0,m=n-a,y=i-o,b=c*y-u*m;if(b<0===f)return!1;const x=d*y-p*m;return x<0!==f&&b>g!==f&&x>g!==f}(e,t))}function Wn(t,e,n){const{crossPadding:i}=e,r=new Set;let s=null;const a=function(t,e){const{type:n,labelDirection:i,crossSize:r}=t;if(!r)return!1;if("arc"===n){const{center:n,radius:s}=t,[a,o]=n,l="negative"===i?0:r,h=-s-l,c=s+l,[u,d,p,g]=P(e);return new _n(a+h-g,o+h-u,a+c+d,o+c+p)}const{startPos:[s,a],endPos:[o,l]}=t,[h,c,u,d]=pn(t)?[-e,0,e,0]:[0,e,0,-e],p=jt(cn(0,t),r),g=new _n(s,a,o,l);return g.x1+=d,g.y1+=h,g.x2+=c+p[0],g.y2+=u+p[1],g}(e,i),o=t=>!a||function(t,e){const{x1:n,x2:i,y1:r,y2:s}=t;return a=[[n,r],[i,r],[i,s],[n,s]],In(e,void 0).every(t=>function(t,e){const n=t.length;if(n<3)return!1;const i=[e,[9999,e[1]]];let r=0,s=0;do{const a=[t[s],t[(s+1)%n]];if(Hn(a,i)){if(0===Gn(a[0],e,a[1]))return Fn(a,e);r++}s=(s+1)%n}while(0!==s);return!!(1&r)}(a,t));var a}(a,t),l=(t,e)=>!t||!t.firstChild||!function(t,e,n){const i=In(t,n).flat(1),r=In(e,n).flat(1),s=[[i[0],i[1],i[2],i[3]],[i[0],i[1],i[4],i[5]],[i[4],i[5],i[6],i[7]],[i[2],i[3],i[6],i[7]]];for(const t of s)if(Vn(r,t))return!0;return!1}(t.firstChild,e.firstChild,P(n));for(const e of t)o(e)?!s||l(s,e)?s=e:(r.add(s),r.add(e)):r.add(e);return Array.from(r)}function Dn(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t(e)?0:"number"==typeof e?e:Math.floor(Ln(e,n))}const jn=t=>void 0!==t&&null!=t&&!Number.isNaN(t);function Rn(t){const e=t&&t.getRenderBounds();if(!e)return{width:0,height:0};const n=e.getMax(),i=e.getMin();return{width:n[0]-i[0],height:n[1]-i[1]}}function Yn(t){const{min:e,max:n}=t.getLocalBounds(),[[i,r],[s,a]]=[e,n];return{x:i,y:r,width:s-i,height:a-r,left:i,bottom:a,top:r,right:s}}function qn(t,e){const n=T(t).append("text").node();return n.attr({...e,visibility:"hidden"}),n}function Kn(t,e){const[n,i]=t,[r,s]=e;return n!==r&&i===s}function Zn(t,e){const[n,i]=t,[r,s]=e;return n===r&&i!==s}function Un(t,e){const{attributes:n}=e;for(const[e,i]of Object.entries(n))"id"!==e&&"className"!==e&&t.attr(e,i)}const Xn=(t,e)=>{let{seq:n=2}=e;return t.filter((t,e)=>!(e%n&&(a(t),1)))},Jn=t=>t.filter(jn),Qn=new Map([["hide",function(t,e,n,i){const r=t.length,{keepHeader:s,keepTail:a}=e;if(r<=1||2===r&&s&&a)return;const o=Xn,l=t=>(t.forEach(i.show),t);let h=2;const c=t.slice();let u=t.slice();const d=Math.min(1,...t.map(t=>t.getBBox().width));if("linear"===n.type&&(dn(n)||pn(n))){const e=Yn(t[0]).left,n=Yn(t[r-1]).right,i=Math.abs(n-e)||1;h=Math.max(Math.floor(r*d/i),h)}let p,g;for(s&&(p=c.splice(0,1)[0]),a&&(g=c.splice(-1,1)[0],c.reverse()),l(c);h<t.length&&Wn(Jn(g?[g,...u,p]:[p,...u]),n,e?.margin).length;)(g&&!p&&h%2==0||g&&p)&&c.splice(0,1).forEach(i.hide),u=o(l(c),{seq:h}),h++}],["rotate",function(t,e,n,i){const{optionalAngles:r=[0,45,90],margin:s,recoverWhenFailed:a=!0}=e,o=t.map(t=>t.getLocalEulerAngles()),l=()=>Wn(t,n,s).length<1,h=e=>t.forEach((t,n)=>{const r=Array.isArray(e)?e[n]:e;i.rotate(t,+r)});for(const t of r)if(h(t),l())return;a&&h(o)}],["ellipsis",function(e,n,i,r){if(e.length<=0)return;const{suffix:s="...",minLength:a,maxLength:o=1/0,step:l=" ",margin:h=[0,0,0,0]}=n,c=En(r.getTextShape(e[0])),u=Dn(l,c),d=a?Dn(a,c):u;let p=Dn(o,c);(t(p)||p===1/0)&&(p=Math.max.apply(null,e.map(t=>t.getBBox().width)));let g=e.slice();const[f=0,m=0,y=f,b=m]=h;for(let t=p;t>d+u;t-=u)if(g.forEach(e=>{r.ellipsis(r.getTextShape(e),t,s)}),g=Wn(e,i,h),g.length<1)return}],["wrap",function(t,e,n,i,r){const{maxLines:s=3,recoverWhenFailed:a=!0,margin:o=[0,0,0,0]}=e,l=Ge(e.wordWrapWidth??50,[r]),h=t.map(t=>t.attr("maxLines")||1),c=Math.min(...h),u=()=>Wn(t,n,o).length<1,d=function(t){const{type:e,labelDirection:n}=t;return"linear"===e&&dn(t)?"negative"===n?"bottom":"top":"middle"}(n),p=e=>t.forEach((t,n)=>{const r=Array.isArray(e)?e[n]:e;i.wrap(t,l,r,d)});if(!(c>s)){if("linear"===n.type&&dn(n)){if(p(s),u())return}else for(let t=c;t<=s;t++)if(p(t),u())return;a&&p(h)}}]]);function ti(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return e.reduce((t,e)=>t*("positive"===e?-1:1),1)}function ei(t){let e=t;for(;e<0;)e+=360;return Math.round(e%360)}function ni(t,e){const[n,i]=t,[r,s]=e,[a,o]=[n*r+i*s,n*s-i*r];return Math.atan2(o,a)}function ii(t,e,n){const{type:i,labelAlign:r}=n,s=cn(t,n),a=ei(e),o=ei(Fe(ni([1,0],s)));let l="center",h="middle";return"linear"===i?[90,270].includes(o)&&0===a?(l="center",h=1===s[1]?"top":"bottom"):o%180||![90,270].includes(a)?0===o?(vn(a,0,90,!1,!0)||vn(a,0,90)||vn(a,270,360))&&(l="start"):90===o?vn(a,0,90,!1,!0)?l="start":(vn(a,90,180)||vn(a,270,360))&&(l="end"):270===o?vn(a,0,90,!1,!0)?l="end":(vn(a,90,180)||vn(a,270,360))&&(l="start"):180===o&&(90===a?l="start":(vn(a,0,90)||vn(a,270,360))&&(l="end")):l="center":"parallel"===r?h=vn(o,0,180,!0)?"top":"bottom":"horizontal"===r?vn(o,90,270,!1)?l="end":(vn(o,270,360,!1)||vn(o,0,90))&&(l="start"):"perpendicular"===r&&(l=vn(o,90,270)?"end":"start"),{textAlign:l,textBaseline:h}}function ri(t,e,n){const{showTick:i,tickLength:r,tickDirection:s,labelDirection:a,labelSpacing:o}=n,l=e.indexOf(t),h=Ge(o,[t,l,e]),[c,u]=[cn(t.value,n),ti(a,s)],d=1===u?Ge(i?r:0,[t,l,e]):0,[p,g]=Rt(jt(c,h+d),un(t.value,n));return{x:p,y:g}}function si(t,e){"text"===t.nodeName&&t.attr(e)}function ai(t,e){!function(t,e,n,i){const{labelOverlap:r=[]}=e;r.length&&r.forEach(r=>{const{type:s}=r,a=Qn.get(s);(function(t,e,n){return!(e.labelOverlap.length<1)&&("hide"===n?!c(t[0]):"rotate"===n?!t.some(t=>!!t.attr("transform")?.includes("rotate")):"ellipsis"!==n&&"wrap"!==n||t.filter(t=>t.querySelector("text")).length>=1)})(t,e,s)&&a?.(t,r,e,i,n)})}(this.node().childNodes,t,e,{hide:a,show:s,rotate:(e,n)=>{!function(t,e,n){e.setLocalEulerAngles(t);const{value:i}=e.__data__,r=ii(i,t,n),s=e.querySelector(_e.labelItem.class);s&&si(s,r)}(+n,e,t)},ellipsis:(t,e,n)=>{t&&Tn(t,e||1/0,n)},wrap:(t,e,n)=>{t&&On(t,e,n)},getTextShape:t=>t.querySelector("text")})}function oi(t,e,n,i,r){const s=n.indexOf(e),a=T(t).append(function(t,e,n,i){const{labelFormatter:r}=i;return N(r)?()=>en(Ge(r,[t,e,n,cn(t.value,i)])):()=>en(t.label||"")}(e,s,n,r)).attr("className",_e.labelItem.name).node(),[o,{transform:l,...h}]=M(sn(i,[e,s,n]));zn(a,l);const c=function(t,e,n){const{labelAlign:i}=n,r=e.style.transform?.includes("rotate");if(r)return e.getLocalEulerAngles();let s=0;const a=cn(t.value,n),o=ln(t.value,n);return"horizontal"===i?0:(s="perpendicular"===i?ni([1,0],a):ni([o[0]<0?-1:1,0],o),function(t){let e=(t+360)%180;return vn(e,-90,90)||(e+=180),e}(Fe(s)))}(e,a,r);return a.getLocalEulerAngles()||a.setLocalEulerAngles(c),si(a,{...ii(e.value,c,r),...o}),t.attr(h),a}function li(t,e){return hn(t,e.tickDirection,e)}function hi(t,e,n,i,r,s){const a=function(t,e,n,i,r){const{tickFormatter:s}=r,a=li(e.value,r);let o="line";return N(s)&&(o=()=>Ge(s,[e,n,i,a])),t.append(o).attr("className",_e.tickItem.name)}(T(this),t,e,n,i);!function(t,e,n,i,r,s,a){const o=li(t.value,s),{x1:l,x2:h,y1:c,y2:u}=function(t,e,n,i,r){const{tickLength:s}=r,[[a,o],[l,h]]=function(t,e){const[n,i]=t;return[[0,0],[n*e,i*e]]}(i,Ge(s,[t,e,n]));return{x1:a,x2:l,y1:o,y2:h}}(t,e,n,o,s),[d,p]=M(sn(a,[t,e,n,o]));"line"===i.node().nodeName&&i.styles({x1:l,x2:h,y1:c,y2:u,...d}),r.attr(p),i.styles(d)}(t,e,n,a,this,i,r);const[o,l]=un(t.value,i);return ge(this,{transform:`translate(${o}, ${l})`},s)}class ci{x=0;y=0;width=0;height=0;get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0;this.x=t,this.y=e,this.width=n,this.height=i}static fromRect(t){return new ci(t.x,t.y,t.width,t.height)}toJSON(){return{x:this.x,y:this.y,width:this.width,height:this.height,top:this.top,right:this.right,bottom:this.bottom,left:this.left}}isPointIn(t,e){return t>=this.left&&t<=this.right&&e>=this.top&&e<=this.bottom}}function ui(t){const{min:[e,n],max:[i,r]}=t.getRenderBounds();return new ci(e,n,i-e,r-n)}const di=Ae({text:"text"},"title");function pi(t){return/\S+-\S+/g.test(t)?t.split("-").map(t=>t[0]):t.length>2?[t[0]]:t.split("")}function gi(t,e){const n=Object.entries(e).reduce((e,n)=>{let[i,r]=n;return t.node().attr(i)||(e[i]=r),e},{});t.styles(n)}class fi extends b{title;constructor(t){super(t,{text:"",width:0,height:0,fill:"#4a505a",fontWeight:"bold",fontSize:12,fontFamily:"sans-serif",inset:0,spacing:0,position:"top-left"})}getAvailableSpace(){const{width:t,height:e,position:n,spacing:i,inset:r}=this.attributes,s=this.querySelector(di.text.class);if(!s)return new ci(0,0,+t,+e);const{width:a,height:o}=s.getBBox(),[l,h,c,u]=P(i);let[d,p,g,f]=[0,0,+t,+e];const m=pi(n);if(m.includes("i"))return new ci(d,p,g,f);m.forEach((n,i)=>{"t"===n&&([p,f]=0===i?[o+c,+e-o-c]:[0,+e]),"r"===n&&([g]=[+t-a-u]),"b"===n&&([f]=[+e-o-l]),"l"===n&&([d,g]=0===i?[a+h,+t-a-h]:[0,+t])});const[y,b,x,v]=P(r),[w,k]=[v+b,y+x];return new ci(d+v,p+y,g-w,f-k)}getBBox(){return this.title?this.title.getBBox():new ci(0,0,0,0)}render(t,e){const{width:n,height:i,position:r,spacing:s,...a}=t,[o]=M(a),{x:l,y:h,textAlign:c,textBaseline:u}=function(t){const{width:e,height:n,position:i}=t,[r,s]=[+e/2,+n/2];let[a,o,l,h]=[+r,+s,"center","middle"];const c=pi(i);return c.includes("l")&&([a,l]=[0,"start"]),c.includes("r")&&([a,l]=[+e,"end"]),c.includes("t")&&([o,h]=[0,"top"]),c.includes("b")&&([o,h]=[+n,"bottom"]),{x:a,y:o,textAlign:l,textBaseline:h}}(t);z(!!a.text,T(e),t=>{this.title=t.maybeAppendByClassName(di.text,"text").styles(o).call(gi,{x:l,y:h,textAlign:c,textBaseline:u}).node()})}}function mi(t,e,n,i,r){const s=B(i,"title"),[a,{transform:o,transformOrigin:l,...h}]=M(s);e.styles(h);const c=o||function(t,e,n){const{halfExtents:i}=t.getGeometryBounds(),r=2*i[1];if("vertical"===e){if("left"===n)return`rotate(-90) translate(0, ${r/2})`;if("right"===n)return`rotate(-90) translate(0, -${r/2})`}return""}(t.node(),a.direction,a.position);t.styles({...a,transformOrigin:l}),zn(t.node(),c);const{x:u,y:d}=function(t,e,n){const{titlePosition:i="lb",titleSpacing:r}=n,s=pi(i),{min:[a,o],halfExtents:[l,h]}=t.node().getLocalBounds(),{halfExtents:[c,u]}=e.node().getLocalBounds();let[d,p]=[a+l,o+h];const[g,f,m,y]=P(r);if(["start","end"].includes(i)&&"linear"===n.type){const{startPos:t,endPos:e}=n,[r,s]="start"===i?[t,e]:[e,t],a=Ut([-s[0]+r[0],-s[1]+r[1]]),[o,l]=jt(a,g);return{x:r[0]+o,y:r[1]+l}}return s.includes("t")&&(p-=h+u+g),s.includes("r")&&(d+=l+c+f),s.includes("l")&&(d-=l+c+y),s.includes("b")&&(p+=h+u+m),{x:d,y:p}}(T(n._offscreen||n.querySelector(_e.mainGroup.class)),e,i);return ge(e.node(),{transform:`translate(${u}, ${d})`},r)}function yi(t,e,n,i){const{showLine:r,showTick:o,showLabel:l}=t,h=z(r,e.maybeAppendByClassName(_e.lineGroup,"g"),e=>yn(e,t,i))||[],c=z(o,e.maybeAppendByClassName(_e.tickGroup,"g"),e=>function(t,e,n,i){const r=an(e,n.tickFilter),s=B(n,"tick");return t.selectAll(_e.tick.class).data(r,t=>t.id||t.label).join(t=>t.append("g").attr("className",_e.tick.name).transition(function(t,e){return hi.call(this,t,e,r,n,s,!1)}),t=>t.transition(function(t,e){return this.removeChildren(),hi.call(this,t,e,r,n,s,i.update)}),t=>t.transition(function(){const t=He(this.childNodes[0],i.exit);return ue(t,()=>this.remove()),t})).transitions()}(e,n,t,i))||[],u=z(l,e.maybeAppendByClassName(_e.labelGroup,"g"),r=>function(t,e,n,i,r){const o=an(e,n.labelFilter),l=B(n,"label");let h;const c=t.selectAll(_e.label.class).data(o,(t,e)=>e).join(t=>t.append("g").attr("className",_e.label.name).transition(function(t){oi(this,t,e,l,n);const{x:i,y:r}=ri(t,e,n);return this.style.transform=`translate(${i}, ${r})`,null}),t=>t.transition(function(t){const r=function(t,e,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"destroy";if(function(t,e){return"text"===t.nodeName&&"text"===e.nodeName&&t.attributes.text===e.attributes.text}(t,e))return t.remove(),[null];const r=()=>{"destroy"===i?t.destroy():"hide"===i&&a(t),e.isVisible()&&s(e)};if(!n)return r(),[null];const{duration:o=0,delay:l=0}=n,h=Math.ceil(+o/2),c=+o/4,{center:[u,d]}=t.getGeometryBounds(),{center:[p,g]}=e.getGeometryBounds(),[f,m]=[(u+p)/2-u,(d+g)/2-d],{opacity:y=1}=t.style,{opacity:b=1}=e.style,x=t.style.transform||"",v=e.style.transform||"",w=t.animate([{opacity:y,transform:`translate(0, 0) ${x}`},{opacity:0,transform:`translate(${f}, ${m}) ${x}`}],{fill:"both",...n,duration:l+h+c}),k=e.animate([{opacity:0,transform:`translate(${-f}, ${-m}) ${v}`,offset:.01},{opacity:b,transform:`translate(0, 0) ${v}`}],{fill:"both",...n,duration:h+c,delay:l+h-c});return ue(k,r),[w,k]}(this.querySelector(_e.labelItem.class),oi(this,t,e,l,n),i.update),{x:o,y:h}=ri(t,e,n);return[...r,ge(this,{transform:`translate(${o}, ${h})`},i.update)]}),t=>(h=t,t.transition(function(){const t=He(this.childNodes[0],i.exit);return ue(t,()=>T(this).remove()),t}),h)).transitions();var u,d;return d=()=>{ai.call(t,n,r)},0===(u=c).length?d():Promise.all(u.map(t=>t?.finished)).then(d),c}(r,n,t,i,e.node()))||[];return[...h,...c,...u].filter(t=>!!t)}class bi extends b{constructor(t){super(t,ze)}render(t,e,n){const{titleText:i,data:r,animate:s,showTitle:a,showGrid:o,dataThreshold:l,truncRange:h}=t,c=Ne(r,l).filter(t=>{let{value:e}=t;return!(h&&e>h[0]&&e<h[1])}),u=ce(void 0===n?s:n),d=z(o,T(e).maybeAppendByClassName(_e.gridGroup,"g"),e=>xn(e,c,t,u))||[],p=T(e).maybeAppendByClassName(_e.mainGroup,"g");i&&(!this.initialized&&u.enter||this.initialized&&u.update)&&yi(t,T(this.offscreenGroup),c,ce(!1));const g=yi(t,T(p.node()),c,u),f=z(a,T(e).maybeAppendByClassName(_e.titleGroup,"g"),e=>function(t,e,n,i){const{titleText:r}=n;return t.selectAll(_e.title.class).data([{title:r}].filter(t=>!!t.title),(t,e)=>t.title).join(s=>s.append(()=>en(r)).attr("className",_e.title.name).transition(function(){return mi(T(this),t,e,n,i.enter)}),r=>r.transition(function(){return mi(T(this),t,e,n,i.update)}),t=>t.remove()).transitions()}(e,this,t,u))||[];return[...d,...g,...f].flat().filter(t=>!!t)}}class xi{}const vi={backgroundFill:"#262626",backgroundLineCap:"round",backgroundLineWidth:1,backgroundStroke:"#333",backgroundZIndex:-1,formatter:t=>t.toString(),labelFill:"#fff",labelFontSize:12,labelTextBaseline:"middle",padding:[2,4],position:"right",radius:0,zIndex:999},wi=Ae({background:"background",labelGroup:"label-group",label:"label"},"indicator");class ki extends b{constructor(t){super(t,vi),this.group=this.appendChild(new e.Group({})),this.isMutationObserved=!0}group;background;label;point=[0,0];renderBackground(){if(!this.label)return;const{position:t,padding:e}=this.attributes,[n,i,r,s]=P(e),{min:a,max:o}=this.label.node().getLocalBounds(),l=new ci(a[0]-s,a[1]-n,o[0]+i-a[0]+s,o[1]+r-a[1]+n),h=this.getPath(t,l),c=B(this.attributes,"background");this.background=T(this.group).maybeAppendByClassName(wi.background,"path").styles({...c,d:h}),this.group.appendChild(this.label.node())}renderLabel(){const{formatter:t,labelText:e}=this.attributes,n=B(this.attributes,"label"),[{text:i,...r},s]=M(n);this.label=T(this.group).maybeAppendByClassName(wi.labelGroup,"g").styles(s),e&&this.label.maybeAppendByClassName(wi.label,()=>en(t(e))).style("text",t(e).toString()).selectAll("text").styles(r)}adjustLayout(){const[t,e]=this.point,{x:n,y:i}=this.attributes;this.group.attr("transform",`translate(${n-t}, ${i-e})`)}getPath(t,e){const{radius:n}=this.attributes,{x:i,y:r,width:s,height:a}=e,o=[["M",i+n,r],["L",i+s-n,r],["A",n,n,0,0,1,i+s,r+n],["L",i+s,r+a-n],["A",n,n,0,0,1,i+s-n,r+a],["L",i+n,r+a],["A",n,n,0,0,1,i,r+a-n],["L",i,r+n],["A",n,n,0,0,1,i+n,r],["Z"]],l={top:4,right:6,bottom:0,left:2}[t],h=this.createCorner([o[l].slice(-2),o[l+1].slice(-2)]);return o.splice(l+1,1,...h),o[0][0]="M",o}createCorner(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;const n=Kn(...t),[[i,r],[s,a]]=t,[o,[l,h]]=n?[s-i,[i,s]]:[a-r,[r,a]],c=o/2,u=e*(o/Math.abs(o)),d=u/2,p=u*Math.sqrt(3)/2*.8,[g,f,m,y,b]=[l,l+c-d,l+c,l+c+d,h];return n?(this.point=[m,r-p],[["L",g,r],["L",f,r],["L",m,r-p],["L",y,r],["L",b,r]]):(this.point=[i+p,m],[["L",i,g],["L",i,f],["L",i+p,m],["L",i,y],["L",i,b]])}applyVisibility(){const{visibility:t}=this.attributes;"hidden"===t?a(this):s(this)}bindEvents(){this.label.on(e.ElementEvent.BOUNDS_CHANGED,this.renderBackground)}render(){this.renderLabel(),this.renderBackground(),this.adjustLayout(),this.applyVisibility()}}function Si(){return"horizontal"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"horizontal")?arguments.length>1?arguments[1]:void 0:arguments.length>2?arguments[2]:void 0}D.registerSymbol("hiddenHandle",function(t,e,n){const i=1.4*n;return[["M",t-n,e-i],["L",t+n,e-i],["L",t+n,e+i],["L",t-n,e+i],["Z"]]}),D.registerSymbol("verticalHandle",function(t,e,n){const i=1.4*n,r=n/2,s=n/6,a=t+.4*i;return[["M",t,e],["L",a,e+r],["L",t+i,e+r],["L",t+i,e-r],["L",a,e-r],["Z"],["M",a,e+s],["L",t+i-2,e+s],["M",a,e-s],["L",t+i-2,e-s]]}),D.registerSymbol("horizontalHandle",function(t,e,n){const i=1.4*n,r=n/2,s=n/6,a=e+.4*i;return[["M",t,e],["L",t-r,a],["L",t-r,e+i],["L",t+r,e+i],["L",t+r,a],["Z"],["M",t-s,a],["L",t-s,e+i-2],["M",t+s,a],["L",t+s,e+i-2]]});const Ai=Ae({markerGroup:"marker-group",marker:"marker",labelGroup:"label-group",label:"label"},"handle"),Li={showLabel:!0,formatter:t=>t.toString(),markerSize:25,markerStroke:"#c5c5c5",markerFill:"#fff",markerLineWidth:1,labelFontSize:12,labelFill:"#c5c5c5",labelText:"",orientation:"vertical",spacing:0};class Bi extends b{constructor(t){super(t,Li)}marker;render(t,e){const n=T(e).maybeAppendByClassName(Ai.markerGroup,"g");this.renderMarker(n);const i=T(e).maybeAppendByClassName(Ai.labelGroup,"g");this.renderLabel(i)}renderMarker(t){const{orientation:e,markerSymbol:n=Si(e,"horizontalHandle","verticalHandle")}=this.attributes;z(!!n,t,t=>{const e=B(this.attributes,"marker"),i={symbol:n,...e};this.marker=t.maybeAppendByClassName(Ai.marker,()=>new D({style:i})).update(i)})}renderLabel(t){const{showLabel:e,orientation:n,spacing:i=0,formatter:r}=this.attributes;z(e,t,t=>{const{text:e,...s}=B(this.attributes,"label"),{width:a=0,height:o=0}=t.select(Ai.marker.class)?.node().getBBox()||{},[l,h,c,u]=Si(n,[0,o+i,"center","top"],[a+i,0,"start","middle"]);t.maybeAppendByClassName(Ai.label,"text").styles({...s,x:l,y:h,text:r(e).toString(),textAlign:c,textBaseline:u})})}}const Ci={showTitle:!0,padding:0,orientation:"horizontal",backgroundFill:"transparent",titleText:"",titleSpacing:4,titlePosition:"top-left",titleFill:"#2C3542",titleFontWeight:"bold",titleFontFamily:"sans-serif",titleFontSize:12},Mi=m({},Ci,{}),Ei=m({},Ci,C(Li,"handle"),{color:["#d0e3fa","#acc7f6","#8daaf2","#6d8eea","#4d73cd","#325bb1","#5a3e75","#8c3c79","#e23455","#e7655b"],indicatorBackgroundFill:"#262626",indicatorLabelFill:"white",indicatorLabelFontSize:12,indicatorVisibility:"hidden",labelAlign:"value",labelDirection:"positive",labelSpacing:5,showHandle:!0,showIndicator:!0,showLabel:!0,slidable:!0,titleText:"",type:"continuous"}),Pi=Ae({title:"title",titleGroup:"title-group",items:"items",itemsGroup:"items-group",contentGroup:"content-group",ribbonGroup:"ribbon-group",ribbon:"ribbon",handlesGroup:"handles-group",handle:"handle",startHandle:"start-handle",endHandle:"end-handle",labelGroup:"label-group",label:"label",indicator:"indicator"},"legend");function $i(t,e){const[n,i]=function(t,e){for(let n=1;n<t.length;n+=1){const i=t[n-1],r=t[n];if(e>=i&&e<=r)return[i,r]}return[e,e]}(t,e);return{tick:e>(n+i)/2?i:n,range:[n,i]}}const Ti=Ae({trackGroup:"background-group",track:"background",selectionGroup:"ribbon-group",selection:"ribbon",clipPath:"clip-path"},"ribbon");function Oi(t){const{orientation:e,size:n,length:i}=t;return Si(e,[i,n],[n,i])}function Ni(t){const{type:e}=t,[n,i]=Oi(t);return"size"===e?[["M",0,i],["L",0+n,0],["L",0+n,i],["Z"]]:[["M",0,i],["L",0,0],["L",0+n,0],["L",0+n,i],["Z"]]}function zi(t){return Ni(t)}function _i(t){const{orientation:e,range:n}=t;if(!n)return[];const[i,r]=Oi(t),[s,a]=n,o=Si(e,s*i,0),l=Si(e,0,s*r),h=Si(e,a*i,i),c=Si(e,r,a*r);return[["M",o,l],["L",o,c],["L",h,c],["L",h,l],["Z"]]}class Ii extends b{constructor(t){super(t,{type:"color",orientation:"horizontal",size:30,range:[0,1],length:200,block:!1,partition:[],color:["#fff","#000"],trackFill:"#e5e5e5"})}render(t,n){!function(t,e){const n=B(e,"track");t.maybeAppendByClassName(Ti.track,"path").styles({d:Ni(e),...n})}(T(n).maybeAppendByClassName(Ti.trackGroup,"g"),t),function(t,n){const i=B(n,"selection"),r=function(t){const{orientation:n,color:i,block:r,partition:s}=t;let a;a=N(i)?new Array(20).fill(0).map((t,e,n)=>i(e/(n.length-1))):i;const o=a.length,l=a.map(t=>(0,e.parseColor)(t).toString());return o?1===o?l[0]:r?function(t,e,n){const i=Array.from(e),r=t.length;return new Array(r).fill(0).reduce((e,n,s)=>{const a=i[s%i.length];return e+` ${t[s]}:${a}${s<r-1?` ${t[s+1]}:${a}`:""}`},`l(${"horizontal"===n?"0":"270"})`)}(s,l,n):l.reduce((t,e,n)=>t+` ${n/(o-1)}:${e}`,`l(${Si(n,"0","270")})`):""}(n),s=t.maybeAppendByClassName(Ti.selection,"path").styles({d:zi(n),fill:r,...i}),a=s.maybeAppendByClassName(Ti.clipPath,"path").styles({d:_i(n)}).node();s.style("clipPath",a)}(T(n).maybeAppendByClassName(Ti.selectionGroup,"g"),t)}}class Fi extends b{constructor(t){super(t,Ei)}eventToOffsetScale=(()=>new Lt({}))();innerRibbonScale=(()=>new Lt({}))();title;label;ribbon;indicator;get handleOffsetRatio(){return this.ifHorizontal(.5,.5)}handlesGroup;startHandle;endHandle;getBBox(){const{width:t,height:e}=this.attributes;return new ci(0,0,t,e)}render(t,e){const{showLabel:n}=t;this.renderTitle(T(e));const{x:i,y:r}=this.availableSpace,s=T(e).maybeAppendByClassName(Pi.contentGroup,"g").styles({transform:`translate(${i}, ${r})`});z(!!n,s.maybeAppendByClassName(Pi.labelGroup,"g").styles({zIndex:1}),t=>{this.renderLabel(t)});const a=s.maybeAppendByClassName(Pi.ribbonGroup,"g").styles({zIndex:0});this.handlesGroup=s.maybeAppendByClassName(Pi.handlesGroup,"g").styles({zIndex:2}),this.renderHandles(),this.renderRibbon(a),this.renderIndicator(s),this.adjustLabel(),this.adjustHandles()}get range(){const{data:t,domain:e}=this.attributes;return e?{min:e[0],max:e[1]}:function(t){return{min:Math.min(...t.map(t=>t.value)),max:Math.max(...t.map(t=>t.value))}}(t)}get ribbonScale(){const{min:t,max:e}=this.range;return this.innerRibbonScale.update({domain:[t,e],range:[0,1]}),this.innerRibbonScale}get ribbonRange(){const[t,e]=this.selection,n=this.ribbonScale;return[n.map(t),n.map(e)]}get selection(){const{min:t,max:e}=this.range,{defaultValue:[n,i]=[t,e]}=this.attributes;return[n,i]}ifHorizontal(t,e){return Si(this.attributes.orientation,"function"==typeof t?t():t,"function"==typeof e?e():e)}renderTitle(t){const{showTitle:e,titleText:n,width:i,height:r}=this.attributes,s={...B(this.attributes,"title"),width:i,height:r,text:n},a=this;t.selectAll(Pi.title.class).data(e?[n]:[]).join(t=>t.append(()=>new fi({style:s})).attr("className",Pi.title.name).each(function(){a.title=this}),t=>t.update(s),t=>t.each(()=>{a.title=void 0}).remove())}get availableSpace(){if(this.title)return this.title.getAvailableSpace();const{width:t,height:e}=this.attributes;return new ci(0,0,t,e)}get labelFixedSpacing(){const{showTick:t}=this.attributes;return t?5:0}get labelPosition(){const{orientation:t,labelDirection:e}=this.attributes;return{vertical:{positive:"right",negative:"left"},horizontal:{positive:"bottom",negative:"top"}}[t][e]}cacheLabelBBox=null;get labelBBox(){const{showLabel:t}=this.attributes;if(!t)return new ci(0,0,0,0);if(this.cacheLabelBBox)return this.cacheLabelBBox;const{width:e,height:n}=(this.label.querySelector(_e.labelGroup.class)?.children.slice(-1)[0]).getBBox();return this.cacheLabelBBox=new ci(0,0,e,n),this.cacheLabelBBox}get labelShape(){const{showLabel:t,labelSpacing:e=0}=this.attributes;if(!t)return{width:0,height:0,size:0,length:0};const{width:n,height:i}=this.labelBBox;return{width:n,height:i,size:this.ifHorizontal(i,n)+e+this.labelFixedSpacing,length:this.ifHorizontal(n,i)}}get ribbonBBox(){const{showHandle:t,ribbonSize:e}=this.attributes,{width:n,height:i}=this.availableSpace,{size:r,length:s}=this.labelShape,[a,o]=this.ifHorizontal([i,n],[n,i]),{size:l,length:h}=t?this.handleShape:{size:0,length:0},c=this.handleOffsetRatio;let u=0;const d=this.labelPosition;u=e||(["bottom","right"].includes(d)?Math.min(a-r,(a-l)/c):a*(1-c)>l?Math.max(a-r,0):Math.max((a-r-l)/c,0));const p=Math.max(h,s),g=o-p,[f,m]=this.ifHorizontal([g,u],[u,g]),y=["top","left"].includes(d)?r:0,[b,x]=this.ifHorizontal([p/2,y],[y,p/2]);return new ci(b,x,f,m)}get ribbonShape(){const{width:t,height:e}=this.ribbonBBox;return this.ifHorizontal({size:e,length:t},{size:t,length:e})}renderRibbon(t){const{data:e,type:n,orientation:i,color:r,block:s}=this.attributes,a=B(this.attributes,"ribbon"),{min:o,max:l}=this.range,{x:h,y:c}=this.ribbonBBox,{length:u,size:d}=this.ribbonShape,p=m({transform:`translate(${h}, ${c})`,length:u,size:d,type:n,orientation:i,color:r,block:s,partition:e.map(t=>(t.value-o)/(l-o)),range:this.ribbonRange},a);this.ribbon=t.maybeAppendByClassName(Pi.ribbon,()=>new Ii({style:p})).update(p)}getHandleClassName(t){return`${Pi.prefix(`${t}-handle`)}`}renderHandles(){const{showHandle:t,orientation:e}=this.attributes,n=B(this.attributes,"handle"),[i,r]=this.selection,s={...n,orientation:e},{shape:a="slider"}=n,o="basic"===a?Bi:$e,l=this;this.handlesGroup.selectAll(Pi.handle.class).data(t?[{value:i,type:"start"},{value:r,type:"end"}]:[],t=>t.type).join(t=>t.append(()=>new o({style:s})).attr("className",t=>{let{type:e}=t;return`${Pi.handle} ${l.getHandleClassName(e)}`}).each(function(t){let{type:e,value:n}=t;this.update({labelText:n}),l[`${e}Handle`]=this,this.addEventListener("pointerdown",l.onDragStart(e))}),t=>t.update(s).each(function(t){let{value:e}=t;this.update({labelText:e})}),t=>t.each(t=>{let{type:e}=t;l[`${e}Handle`]=void 0}).remove())}adjustHandles(){const[t,e]=this.selection;this.setHandlePosition("start",t),this.setHandlePosition("end",e)}cacheHandleBBox=null;get handleBBox(){if(this.cacheHandleBBox)return this.cacheHandleBBox;if(!this.attributes.showHandle)return new ci(0,0,0,0);const{width:t,height:e}=this.startHandle.getBBox(),{width:n,height:i}=this.endHandle.getBBox(),[r,s]=[Math.max(t,n),Math.max(e,i)];return this.cacheHandleBBox=new ci(0,0,r,s),this.cacheHandleBBox}get handleShape(){const{width:t,height:e}=this.handleBBox,[n,i]=this.ifHorizontal([e,t],[t,e]);return{width:t,height:e,size:n,length:i}}setHandlePosition(t,e){const{handleFormatter:n}=this.attributes,{x:i,y:r}=this.ribbonBBox,{size:s}=this.ribbonShape,a=this.getOffset(e),[o,l]=this.ifHorizontal([i+a,r+s*this.handleOffsetRatio],[i+s*this.handleOffsetRatio,r+a]),h=this.handlesGroup.select(`.${this.getHandleClassName(t)}`).node();h?.update({transform:`translate(${o}, ${l})`,formatter:n})}renderIndicator(t){const e=B(this.attributes,"indicator");this.indicator=t.maybeAppendByClassName(Pi.indicator,()=>new ki({})).update(e)}get labelData(){const{data:t}=this.attributes;return t.reduce((t,e,n,i)=>{const r=e?.id??n.toString();if(t.push({...e,id:r,index:n,type:"value",label:e?.label??e.value.toString(),value:this.ribbonScale.map(e.value)}),n<i.length-1){const s=i[n+1],[a,o]=[e.value,s.value],l=(a+o)/2;t.push({...e,id:r,index:n,type:"range",range:[a,o],label:[a,o].join("~"),value:this.ribbonScale.map(l)})}return t},[])}get labelStyle(){let[t,e]=["center","middle"];const n=this.labelPosition;return"top"===n?e="bottom":"bottom"===n?e="top":"left"===n?t="end":"right"===n&&(t="start"),{labelTextAlign:t,labelTextBaseline:e}}renderLabel(t){const{showTick:e=!1,labelFilter:n,labelFormatter:i}=this.attributes,r=B(this.attributes,"tick"),s=B(this.attributes,"label"),{align:a}=s,o={...m({showLine:!1,showGrid:!1,showTick:e,type:"linear",startPos:[0,0],endPos:[0,0],tickDirection:"negative",labelTransform:"rotate(0)",...this.labelStyle},C(r,"tick"),C(s,"label"),{data:this.labelData}),tickFilter:(t,e,i)=>"value"===t?.type&&(!n||n(t,t.index,i.filter(t=>"value"!==t.type))),labelFilter:(t,e,i)=>t?.type===a&&(!n||n(t,t.index,i.filter(t=>t.type===a))),labelFormatter:i,labelOverlap:[{type:"hide"}]};this.label=t.maybeAppendByClassName(Pi.label,()=>new bi({style:o})).node(),this.label.update(o,!1)}get labelAxisStyle(){const{showTick:t,labelDirection:e,labelSpacing:n,tickLength:i}=this.attributes,{size:r}=this.ribbonShape,s=this.labelPosition,a=this.labelFixedSpacing;let[o,l,h]=[0,0,0];const c=i??r;return t?(h=c,l=a,"positive"===e?"right"===s?(o=c,h=c):"bottom"===s&&(o=h):"negative"===e&&("top"===s||"left"===s)&&(o=r)):"positive"===e?"right"===s?l=c:"bottom"===s&&(o=r+a,l=n):"negative"===e&&("left"===s||"top"===s)&&(l=n),{offset:o,spacing:l,tickLength:h}}adjustLabel(){const{showLabel:t}=this.attributes;if(!t)return;const{x:e,y:n,width:i,height:r}=this.ribbonBBox,{offset:s,spacing:a,tickLength:o}=this.labelAxisStyle,[l,h]=this.ifHorizontal([[e,n+s],[e+i,n+s]],[[e+s,n+r],[e+s,n]]);this.label.update({startPos:l,endPos:h,tickLength:o,labelSpacing:a},!1)}target;prevValue;bindEvents(){this.style.cursor="pointer",this.ribbon.on("pointerdown",this.onDragStart("ribbon")),this.ribbon.on("pointermove",this.onHovering),this.addEventListener("pointerout",this.hideIndicator)}onHovering=t=>{const{data:e,block:n}=this.attributes;t.stopPropagation();const i=this.getValueByCanvasPoint(t);if(n){const{range:t}=$i(e.map(t=>{let{value:e}=t;return e}),i),n=this.getRealSelection(t);this.showIndicator((t[0]+t[1])/2,`${n[0]}-${n[1]}`),this.dispatchIndicated(i,t)}else{const t=this.getTickValue(i);this.showIndicator(t,`${this.getRealValue(t)}`),this.dispatchIndicated(t)}};showIndicator(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:`${t}`;const{showIndicator:n}=this.attributes;if(!n||"number"!=typeof t)return void this.hideIndicator();const{min:i,max:r}=this.range,{x:a,y:o}=this.ribbonBBox,l=Ht(t,i,r),h=this.getOffset(l),c=this.ifHorizontal([h+a,o],[a,h+o]);this.indicator.update({x:c[0],y:c[1],position:this.ifHorizontal("top","left"),labelText:e}),s(this.indicator.node())}hideIndicator(){a(this.indicator.node())}onDragStart=t=>e=>{e.stopPropagation(),this.attributes.slidable&&(this.target=t,this.prevValue=this.getTickValue(this.getValueByCanvasPoint(e)),document.addEventListener("mousemove",this.onDragging),document.addEventListener("touchmove",this.onDragging),document.addEventListener("mouseleave",this.onDragEnd),document.addEventListener("mouseup",this.onDragEnd),document.addEventListener("mouseup",this.onDragEnd),document.addEventListener("touchend",this.onDragEnd))};onDragging=t=>{const{target:e}=this;this.updateMouse();const[n,i]=this.selection,r=this.getTickValue(this.getValueByCanvasPoint(t)),s=r-this.prevValue;"start"===e?n!==r&&this.updateSelection(r,i):"end"===e?i!==r&&this.updateSelection(n,r):"ribbon"===e&&0!==s&&(this.prevValue=r,this.updateSelection(s,s,!0))};onDragEnd=()=>{this.style.cursor="pointer",document.removeEventListener("mousemove",this.onDragging),document.removeEventListener("touchmove",this.onDragging),document.removeEventListener("mouseup",this.onDragEnd),document.removeEventListener("touchend",this.onDragEnd)};updateMouse(){this.attributes.slidable&&(this.style.cursor="grabbing")}setSelection(t,e){this.updateSelection(t,e)}updateSelection(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const[i,r]=this.selection;let[s,a]=[t,e];n&&(s+=i,a+=r);const{min:o,max:l}=this.range;[s,a]=function(t,e,n){const[i,r]=t,[s,a]=e,[o,l]=n;let[h,c]=[s,a];const u=c-h;return h>c&&([h,c]=[c,h]),u>r-i?[i,r]:h<i?o===i&&l===c?[i,c]:[i,u+i]:c>r?l===r&&o===h?[h,r]:[r-u,r]:[h,c]}([o,l],[s,a],this.selection),this.update({defaultValue:[s,a]}),this.dispatchSelection()}get step(){const{step:t=1}=this.attributes,{min:e,max:n}=this.range;return R(t)?fe(.01*(n-e),0):t}getTickValue(t){const{data:e,block:n}=this.attributes,{min:i}=this.range;return n?$i(e.map(t=>{let{value:e}=t;return e}),t).tick:function(t,e,n){return n+Math.round((t-n)/e)*e}(t,this.step,i)}getValueByCanvasPoint(t){const{min:e,max:n}=this.range,[i,r]=this.ribbon.node().getPosition(),s=this.ifHorizontal(i,r),a=this.ifHorizontal(...Se(t))-s;return Ht(this.getOffset(a,!0),e,n)}getOffset(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const{min:n,max:i}=this.range,{length:r}=this.ribbonShape,s=this.eventToOffsetScale;return s.update({domain:[n,i],range:[0,r]}),e?s.invert(t):s.map(t)}getRealSelection(t){const{max:e}=this.range,[n,i]=t;return this.ifHorizontal([n,i],[e-i,e-n])}getRealValue(t){const{max:e}=this.range;return this.ifHorizontal(t,e-t)}dispatchSelection(){const t=this.getRealSelection(this.selection),n=new e.CustomEvent("valuechange",{detail:{value:t}});this.dispatchEvent(n)}dispatchIndicated(t,n){const{max:i}=this.range,r=this.ifHorizontal(()=>({value:t,range:n}),()=>({value:i-t,range:n?this.getRealSelection(n):void 0})),s=new e.CustomEvent("indicate",{detail:r});this.dispatchEvent(s)}}const Gi=function(){};function Hi(t,e){return t.reduce((t,n)=>((t[n[e]]=t[n[e]]||[]).push(n),t),{})}function Vi(t){return t[0]?.map((e,n)=>t.map(t=>t[n]))||[]}const Wi=Ae({prevBtnGroup:"prev-btn-group",prevBtn:"prev-btn",nextBtnGroup:"next-btn-group",nextBtn:"next-btn",pageInfoGroup:"page-info-group",pageInfo:"page-info",playWindow:"play-window",contentGroup:"content-group",controller:"controller",clipPath:"clip-path"},"navigator");class Di extends b{constructor(t){super(t,{x:0,y:0,animate:{easing:"linear",duration:200,fill:"both"},buttonCursor:"pointer",buttonFill:"black",buttonD:[["M",-6,-6],["L",6,0],["L",-6,6],["Z"]],buttonSize:12,controllerPadding:5,controllerSpacing:5,formatter:(t,e)=>`${t}/${e}`,defaultPage:0,loop:!1,orientation:"horizontal",pageNumFill:"black",pageNumFontSize:12,pageNumTextAlign:"start",pageNumTextBaseline:"middle"})}playState="idle";contentGroup=(()=>this.appendChild(new e.Group({class:Wi.contentGroup.name})))();playWindow=(()=>this.contentGroup.appendChild(new e.Group({class:Wi.playWindow.name})))();get defaultPage(){const{defaultPage:t}=this.attributes;return Ht(t,0,Math.max(this.pageViews.length-1,0))}innerCurrPage=this.defaultPage;clipPath;prevBtnGroup;nextBtnGroup;pageInfoGroup;get pageViews(){return this.playWindow.children}get controllerShape(){return this.totalPages>1?{width:55,height:0}:{width:0,height:0}}get pageShape(){const{pageViews:t}=this,[e,n]=Vi(t.map(t=>{const{width:e,height:n}=t.getBBox();return[e,n]})).map(t=>Math.max(...t)),{pageWidth:i=e,pageHeight:r=n}=this.attributes;return{pageWidth:i,pageHeight:r}}getContainer(){return this.playWindow}get totalPages(){return this.pageViews.length}get currPage(){return this.innerCurrPage}getBBox(){const{x:t,y:e}=super.getBBox(),n=this.controllerShape,{pageWidth:i,pageHeight:r}=this.pageShape;return new ci(t,e,i+n.width,r)}goTo(t){const{animate:e}=this.attributes,{currPage:n,playState:i,playWindow:r,pageViews:s}=this;if("idle"!==i||t<0||s.length<=0||t>=s.length)return null;s[n].setLocalPosition(0,0),this.prepareFollowingPage(t);const[a,o]=this.getFollowingPageDiff(t);this.playState="running";const l=pe(r,[{transform:"translate(0, 0)"},{transform:`translate(${-a}, ${-o})`}],e);return ue(l,()=>{this.innerCurrPage=t,this.playState="idle",this.setVisiblePages([t]),this.updatePageInfo()}),l}prev(){const{loop:t}=this.attributes,e=this.pageViews.length,n=this.currPage;if(!t&&n<=0)return null;const i=t?(n-1+e)%e:Ht(n-1,0,e);return this.goTo(i)}next(){const{loop:t}=this.attributes,e=this.pageViews.length,n=this.currPage;if(!t&&n>=e-1)return null;const i=t?(n+1)%e:Ht(n+1,0,e);return this.goTo(i)}renderClipPath(t){const{pageWidth:e,pageHeight:n}=this.pageShape;e&&n?(this.clipPath=t.maybeAppendByClassName(Wi.clipPath,"rect").styles({width:e,height:n}),this.contentGroup.attr("clipPath",this.clipPath.node())):this.contentGroup.style.clipPath=void 0}setVisiblePages(t){this.playWindow.children.forEach((e,n)=>{t.includes(n)?s(e):a(e)})}adjustControllerLayout(){const{prevBtnGroup:t,nextBtnGroup:e,pageInfoGroup:n}=this,{orientation:i,controllerPadding:r}=this.attributes,{width:s,height:a}=n.getBBox(),[o,l]="horizontal"===i?[-180,0]:[-90,90];t.setLocalEulerAngles(o),e.setLocalEulerAngles(l);const{width:h,height:c}=t.getBBox(),{width:u,height:d}=e.getBBox(),p=Math.max(h,s,u),{offset:[[g,f],[m,y],[b,x]],textAlign:v}="horizontal"===i?{offset:[[0,0],[h/2+r,0],[h+s+2*r,0]],textAlign:"start"}:{offset:[[p/2,-c-r],[p/2,0],[p/2,d+r]],textAlign:"center"},w=n.querySelector("text");w&&(w.style.textAlign=v),t.setLocalPosition(g,f),n.setLocalPosition(m,y),e.setLocalPosition(b,x)}updatePageInfo(){const{currPage:t,pageViews:e,attributes:{formatter:n}}=this;e.length<2||(this.pageInfoGroup.querySelector(Wi.pageInfo.class)?.attr("text",n(t+1,e.length)),this.adjustControllerLayout())}getFollowingPageDiff(t){const{currPage:e}=this;if(e===t)return[0,0];const{orientation:n}=this.attributes,{pageWidth:i,pageHeight:r}=this.pageShape,s=t<e?-1:1;return"horizontal"===n?[s*i,0]:[0,s*r]}prepareFollowingPage(t){const{currPage:e,pageViews:n}=this;if(this.setVisiblePages([t,e]),t!==e){const[e,i]=this.getFollowingPageDiff(t);n[t].setLocalPosition(e,i)}}renderController(t){const{controllerSpacing:e}=this.attributes,{pageWidth:n,pageHeight:i}=this.pageShape,r=this.pageViews.length>=2,s=t.maybeAppendByClassName(Wi.controller,"g");if(o(s.node(),r),!r)return;const a=B(this.attributes,"button"),l=B(this.attributes,"pageNum"),[{size:h,...c},u]=M(a),d=!s.select(Wi.prevBtnGroup.class).node(),p=s.maybeAppendByClassName(Wi.prevBtnGroup,"g").styles(u);this.prevBtnGroup=p.node();const g=p.maybeAppendByClassName(Wi.prevBtn,"path"),f=s.maybeAppendByClassName(Wi.nextBtnGroup,"g").styles(u);this.nextBtnGroup=f.node(),[g,f.maybeAppendByClassName(Wi.nextBtn,"path")].forEach(t=>{t.styles({...c,transformOrigin:"center"}),nn(t.node(),h,!0)});const m=s.maybeAppendByClassName(Wi.pageInfoGroup,"g");this.pageInfoGroup=m.node(),m.maybeAppendByClassName(Wi.pageInfo,"text").styles(l),this.updatePageInfo(),s.node().setLocalPosition(n+e,i/2),d&&(this.prevBtnGroup.addEventListener("click",()=>{this.prev()}),this.nextBtnGroup.addEventListener("click",()=>{this.next()}))}render(t,e){const{x:n=0,y:i=0}=t;this.attr("transform",`translate(${n}, ${i})`);const r=T(e);this.renderClipPath(r),this.renderController(r),this.setVisiblePages([this.defaultPage]),this.goTo(this.defaultPage)}bindEvents(){const t=(n=()=>this.render(this.attributes,this),i=50,function(){var t=this,e=arguments,a=r&&!s;clearTimeout(s),s=setTimeout(function(){s=null,r||n.apply(t,e)},i),a&&n.apply(t,e)});var n,i,r,s;this.playWindow.addEventListener(e.ElementEvent.INSERTED,t),this.playWindow.addEventListener(e.ElementEvent.REMOVED,t)}}function ji(t,e){for(var n in e)e.hasOwnProperty(n)&&"constructor"!==n&&void 0!==e[n]&&(t[n]=e[n])}const Ri="component-poptip",Yi="component-poptip-arrow",qi="component-poptip-text",Ki={[`.${Ri}`]:{visibility:"visible",position:"absolute","background-color":"rgba(0, 0, 0)","box-shadow":"0px 0px 10px #aeaeae","border-radius":"3px",color:"#fff",opacity:.8,"font-size":"12px",padding:"4px 6px",display:"flex","justify-content":"center","align-items":"center","z-index":8,transition:"visibility 50ms"},[`.${qi}`]:{"text-align":"center"},[`.${Ri}[data-position='top']`]:{transform:"translate(-50%, -100%)"},[`.${Ri}[data-position='left']`]:{transform:"translate(-100%, -50%)"},[`.${Ri}[data-position='right']`]:{transform:"translate(0, -50%)"},[`.${Ri}[data-position='bottom']`]:{transform:"translate(-50%, 0)"},[`.${Ri}[data-position='top-left']`]:{transform:"translate(0,-100%)"},[`.${Ri}[data-position='top-right']`]:{transform:"translate(-100%,-100%)"},[`.${Ri}[data-position='left-top']`]:{transform:"translate(-100%, 0)"},[`.${Ri}[data-position='left-bottom']`]:{transform:"translate(-100%, -100%)"},[`.${Ri}[data-position='right-top']`]:{transform:"translate(0, 0)"},[`.${Ri}[data-position='right-bottom']`]:{transform:"translate(0, -100%)"},[`.${Ri}[data-position='bottom-left']`]:{transform:"translate(0, 0)"},[`.${Ri}[data-position='bottom-right']`]:{transform:"translate(-100%, 0)"},[`.${Yi}`]:{width:"4px",height:"4px",transform:"rotate(45deg)","background-color":"rgba(0, 0, 0)",position:"absolute","z-index":-1},[`.${Ri}[data-position='top']`]:{transform:"translate(-50%, calc(-100% - 5px))"},[`[data-position='top'] .${Yi}`]:{bottom:"-2px"},[`.${Ri}[data-position='left']`]:{transform:"translate(calc(-100% - 5px), -50%)"},[`[data-position='left'] .${Yi}`]:{right:"-2px"},[`.${Ri}[data-position='right']`]:{transform:"translate(5px, -50%)"},[`[data-position='right'] .${Yi}`]:{left:"-2px"},[`.${Ri}[data-position='bottom']`]:{transform:"translate(-50%, 5px)"},[`[data-position='bottom'] .${Yi}`]:{top:"-2px"},[`.${Ri}[data-position='top-left']`]:{transform:"translate(0, calc(-100% - 5px))"},[`[data-position='top-left'] .${Yi}`]:{left:"10px",bottom:"-2px"},[`.${Ri}[data-position='top-right']`]:{transform:"translate(-100%, calc(-100% - 5px))"},[`[data-position='top-right'] .${Yi}`]:{right:"10px",bottom:"-2px"},[`.${Ri}[data-position='left-top']`]:{transform:"translate(calc(-100% - 5px), 0)"},[`[data-position='left-top'] .${Yi}`]:{right:"-2px",top:"8px"},[`.${Ri}[data-position='left-bottom']`]:{transform:"translate(calc(-100% - 5px), -100%)"},[`[data-position='left-bottom'] .${Yi}`]:{right:"-2px",bottom:"8px"},[`.${Ri}[data-position='right-top']`]:{transform:"translate(5px, 0)"},[`[data-position='right-top'] .${Yi}`]:{left:"-2px",top:"8px"},[`.${Ri}[data-position='right-bottom']`]:{transform:"translate(5px, -100%)"},[`[data-position='right-bottom'] .${Yi}`]:{left:"-2px",bottom:"8px"},[`.${Ri}[data-position='bottom-left']`]:{transform:"translate(0, 5px)"},[`[data-position='bottom-left'] .${Yi}`]:{top:"-2px",left:"8px"},[`.${Ri}[data-position='bottom-right']`]:{transform:"translate(-100%, 5px)"},[`[data-position='bottom-right'] .${Yi}`]:{top:"-2px",right:"8px"}};function Zi(t){let e=t&&document.getElementById(t);return e||(e=document.createElement("div"),e.setAttribute("id",t),document.body.appendChild(e)),e}class Ui extends b{static tag="poptip";get visible(){return"visible"===this.visibility}static defaultOptions=(()=>({style:{x:0,y:0,width:0,height:0,target:null,visibility:"hidden",text:"",position:"top",follow:!1,offset:[0,0],domStyles:Ki,template:`<div class="${qi}"></div>`}}))();container;visibility="visible";map=(()=>new Map)();domStyles="";constructor(t){super(K({style:{id:"component-poptip"}},Ui.defaultOptions,t)),this.initShape(),this.render(this.attributes,this)}render(t,e){this.visibility=this.style.visibility,this.updatePoptipElement()}update(t){this.attr(K({},this.style,t)),this.render(this.attributes,this)}bind(t,e){if(!t)return;const{text:n}=this.style,i=i=>{let r=t,s=this.style,a=n;if(e){const{html:t,target:n,...c}="function"==typeof e?e.call(null,i):e;o={},h=c,(l=this.style)&&ji(o,l),h&&ji(o,h),s=o,(n||!1===n)&&(r=n),"string"==typeof t&&(a=t)}var o,l,h;const{position:c,arrowPointAtCenter:u,follow:d,offset:p}=s;if(r){const{clientX:t,clientY:e}=i,[n,s]=function(t,e,n,i){let r=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(arguments.length>5&&void 0!==arguments[5]&&arguments[5])return[t,e];const{x:s,y:a,width:o,height:l}=n.getBoundingClientRect();switch(i){case"top":return r?[s+o/2,a]:[t,a];case"left":return r?[s,a+l/2]:[s,e];case"bottom":return r?[s+o/2,a+l]:[t,a+l];case"right":return r?[s+o,a+l/2]:[s+o,e];case"top-right":case"right-top":return[s+o,a];case"left-bottom":case"bottom-left":return[s,a+l];case"right-bottom":case"bottom-right":return[s+o,a+l];default:return[s,a]}}(t,e,r,c,u,d);this.showTip(n,s,{text:a,position:c,offset:p})}else this.hideTip()},r=()=>{this.hideTip()};t.addEventListener("mousemove",i),t.addEventListener("mouseleave",r),this.map.set(t,[i,r])}unbind(t){if(this.map.has(t)){const[e,n]=this.map.get(t)||[];e&&t.removeEventListener("mousemove",e),n&&t.removeEventListener("mouseleave",n),this.map.delete(t)}}clear(){this.container.innerHTML=""}destroy(){[...this.map.keys()].forEach(t=>this.unbind(t)),this.container?.remove(),super.destroy()}showTip(t,e,n){const i=Ze(n,"text");if((!i||"string"==typeof i)&&(this.applyStyles(),t&&e&&n)){const{offset:r,position:s}=n;if(s&&this.container.setAttribute("data-position",s),this.setOffsetPosition(t,e,r),"string"==typeof i){const t=this.container.querySelector(`.${qi}`);t&&(t.innerHTML=i)}this.visibility="visible",this.container.style.visibility="visible"}}hideTip(){this.visibility="hidden",this.container.style.visibility="hidden"}getContainer(){return this.container}getClassName(){const{containerClassName:t}=this.style;return`${Ri}${t?` ${t}`:""}`}initShape(){const{id:t}=this.style;this.container=function(t){const e=(t=>{let e;return function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];return e||(e=t.apply(void 0,i)),e}})(Zi)(t);return e}(t),this.container.className=this.getClassName(),this.container.addEventListener("mousemove",()=>this.showTip()),this.container.addEventListener("mouseleave",()=>this.hideTip())}updatePoptipElement(){const{container:t}=this;this.clear();const{id:e,template:n,text:i}=this.style;this.container.setAttribute("id",e),this.container.className=this.getClassName();const r=`<span class="${Yi}"></span>`;var s;t.innerHTML=r,W(n)?t.innerHTML+=n:n&&((s=n)instanceof Element||s instanceof Document)&&t.appendChild(n),i&&(t.getElementsByClassName(qi)[0].textContent=i),this.applyStyles(),this.container.style.visibility=this.visibility}applyStyles(){const t=m({},Ki,this.style.domStyles),e=Object.entries(t).reduce((t,e)=>{let[n,i]=e;const r=Object.entries(i).reduce((t,e)=>{let[n,i]=e;return`${t}${n}: ${i};`},"");return`${t}${n}{${r}}`},"");if(this.domStyles!==e){this.domStyles=e;let t=this.container.querySelector("style");t&&this.container.removeChild(t),t=document.createElement("style"),t.innerHTML=e,this.container.appendChild(t)}}setOffsetPosition(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.style.offset;const[i=0,r=0]=n;this.container.style.left=`${t+i}px`,this.container.style.top=`${e+r}px`}}const Xi=Ae({layout:"flex",markerGroup:"marker-group",marker:"marker",labelGroup:"label-group",label:"label",valueGroup:"value-group",value:"value",backgroundGroup:"background-group",background:"background"},"legend-category-item"),Ji={offset:[0,20],domStyles:{".component-poptip":{opacity:"1",padding:"8px 12px",background:"#fff",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"},".component-poptip-arrow":{display:"none"},".component-poptip-text":{color:"#000",lineHeight:"20px"}}};class Qi extends b{constructor(t){super(t,{span:[1,1],marker:()=>new e.Circle({style:{r:6}}),markerSize:10,labelFill:"#646464",valueFill:"#646464",labelFontSize:12,valueFontSize:12,labelTextBaseline:"middle",valueTextBaseline:"middle"})}poptipGroup;markerGroup;labelGroup;valueGroup;background;get showValue(){const{valueText:t}=this.attributes;return!!t&&("string"==typeof t||"number"==typeof t?""!==t:"function"==typeof t||""!==t.attr("text"))}get actualSpace(){const t=this.labelGroup,e=this.valueGroup,{markerSize:n}=this.attributes,{width:i,height:r}=t.node().getBBox(),{width:s,height:a}=e.node().getBBox();return{markerWidth:n,labelWidth:i,valueWidth:s,height:Math.max(n,r,a)}}get span(){const{span:t}=this.attributes;if(!t)return[1,1];const[e,n]=P(t),i=this.showValue?n:0,r=e+i;return[e/r,i/r]}get shape(){const{markerSize:t,width:e}=this.attributes,n=this.actualSpace,{markerWidth:i,height:r}=n;let{labelWidth:s,valueWidth:a}=this.actualSpace;const[o,l]=this.spacing;if(e){const n=e-t-o-l,[i,r]=this.span;[s,a]=[i*n,r*n]}return{width:i+s+a+o+l,height:r,markerWidth:i,labelWidth:s,valueWidth:a}}get spacing(){const{spacing:t}=this.attributes;if(!t)return[0,0];const[e,n]=P(t);return this.showValue?[e,n]:[e,0]}get layout(){const{markerWidth:t,labelWidth:e,valueWidth:n,width:i,height:r}=this.shape,[s,a]=this.spacing;return{height:r,width:i,markerWidth:t,labelWidth:e,valueWidth:n,position:[t/2,t+s,t+e+s+a]}}get scaleSize(){const t=function(t){const e=t.querySelector(Xi.marker.class);return e?e.style:{}}(this.markerGroup.node()),{markerSize:e,markerStrokeWidth:n=t.strokeWidth,markerLineWidth:i=t.lineWidth,markerStroke:r=t.stroke}=this.attributes,s=+(n||i||(r?1:0))*Math.sqrt(2),{width:a,height:o}=this.markerGroup.node().getBBox();return(1-s/Math.max(a,o))*e}renderMarker(t){const{marker:e}=this.attributes,n=B(this.attributes,"marker");this.markerGroup=t.maybeAppendByClassName(Xi.markerGroup,"g").style("zIndex",0),z(!!e,this.markerGroup,()=>{const t=this.markerGroup.node(),i=t.childNodes?.[0],r="string"==typeof e?new D({style:{symbol:e},className:Xi.marker.name}):e();i?r.nodeName===i.nodeName?i instanceof D?i.update({...n,symbol:e}):(Un(i,r),T(i).styles(n)):(i.remove(),T(r).attr("className",Xi.marker.name).styles(n),t.appendChild(r)):(r instanceof D||T(r).attr("className",Xi.marker.name).styles(n),t.appendChild(r)),this.markerGroup.node().scale(1/this.markerGroup.node().getScale()[0]);const s=nn(this.markerGroup.node(),this.scaleSize,!0);this.markerGroup.node().style._transform=`scale(${s})`})}renderLabel(t){const{text:e,...n}=B(this.attributes,"label");this.labelGroup=t.maybeAppendByClassName(Xi.labelGroup,"g").style("zIndex",0),this.labelGroup.maybeAppendByClassName(Xi.label,()=>en(e)).styles(n)}renderValue(t){const{text:e,...n}=B(this.attributes,"value");this.valueGroup=t.maybeAppendByClassName(Xi.valueGroup,"g").style("zIndex",0),z(this.showValue,this.valueGroup,()=>{this.valueGroup.maybeAppendByClassName(Xi.value,()=>en(e)).styles(n)})}createPoptip(){const{poptip:t}=this.attributes,{render:e,...n}=t||{},i=new Ui({style:m(Ji,n)});return this.poptipGroup=i,i}bindPoptip(t){const{poptip:e}=this.attributes;e&&(this.poptipGroup||this.createPoptip()).bind(t,()=>{const{labelText:t,valueText:n,markerFill:i}=this.attributes,r="string"==typeof t?t:t?.attr("text"),s="string"==typeof n?n:n?.attr("text");if("function"==typeof e.render)return{html:e.render({label:r,value:s,color:i})};let a="";return"string"!=typeof r&&"number"!=typeof r||(a+=`<div class="component-poptip-label">${r}</div>`),"string"!=typeof s&&"number"!=typeof s||(a+=`<div class="component-poptip-value">${s}</div>`),{html:a}})}renderPoptip(t){const{poptip:e}=this.attributes;e&&[t.maybeAppendByClassName(Xi.value,"g").node(),t.maybeAppendByClassName(Xi.label,"g").node()].forEach(t=>{t&&this.bindPoptip(t)})}renderBackground(t){const{width:e,height:n}=this.shape,i=B(this.attributes,"background");this.background=t.maybeAppendByClassName(Xi.backgroundGroup,"g").style("zIndex",-1),this.background.maybeAppendByClassName(Xi.background,"rect").styles({width:e,height:n,...i})}adjustLayout(){const{layout:{labelWidth:t,valueWidth:e,height:n,position:[i,r,s]}}=this,a=n/2;this.markerGroup.styles({transform:`translate(${i}, ${a})${this.markerGroup.node().style._transform}`}),this.labelGroup.styles({transform:`translate(${r}, ${a})`}),Tn(this.labelGroup.select(Xi.label.class).node(),Math.ceil(t)),this.showValue&&(this.valueGroup.styles({transform:`translate(${s}, ${a})`}),Tn(this.valueGroup.select(Xi.value.class).node(),Math.ceil(e)))}render(t,e){const n=T(e),{x:i=0,y:r=0}=t;n.styles({transform:`translate(${i}, ${r})`}),this.renderMarker(n),this.renderLabel(n),this.renderValue(n),this.renderBackground(n),this.renderPoptip(n),this.adjustLayout()}}const tr=Ae({page:"item-page",navigator:"navigator",item:"item"},"items"),er=function(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return t?e(t):n};class nr extends b{constructor(t){super(t,{data:[],gridRow:1/0,gridCol:void 0,padding:0,width:1e3,height:100,rowPadding:0,colPadding:0,layout:"flex",orientation:"horizontal",click:Gi,mouseenter:Gi,mouseleave:Gi})}navigator;navigatorShape=[0,0];get pageViews(){return this.navigator.getContainer()}get grid(){const{gridRow:t,gridCol:e,data:n}=this.attributes;if(!t&&!e)throw new Error("gridRow and gridCol can not be set null at the same time");return t&&e?[t,e]:t?[t,n.length]:[n.length,e]}get renderData(){const{data:t,layout:e,poptip:n}=this.attributes,i=B(this.attributes,"item");return t.map((r,s)=>{const{id:a=s,label:o,value:l}=r;return{id:`${a}`,index:s,style:{layout:e,labelText:o,valueText:l,poptip:n,...Object.fromEntries(Object.entries(i).map(e=>{let[n,i]=e;return[n,Ge(i,[r,s,t])]}))}}})}getGridLayout(){const{orientation:t,width:e,rowPadding:n,colPadding:i}=this.attributes,[r]=this.navigatorShape,[s,a]=this.grid,o=a*s;let l=0;return this.pageViews.children.map((h,c)=>{const u=Math.floor(c/o),d=c%o,p=this.ifHorizontal(a,s),g=[Math.floor(d/p),d%p];"vertical"===t&&g.reverse();const[f,m]=g,y=(e-r-(a-1)*i)/a,b=h.getBBox().height;let[x,v]=[0,0];return"horizontal"===t?([x,v]=[l,f*(b+n)],l=m===a-1?0:l+y+i):([x,v]=[m*(y+i),l],l=f===s-1?0:l+b+n),{page:u,index:c,row:f,col:m,pageIndex:d,width:y,height:b,x,y:v}})}getFlexLayout(){const{width:t,height:e,rowPadding:n,colPadding:i}=this.attributes,[r]=this.navigatorShape,[s,a]=this.grid,[o,l]=[t-r,e];let[h,c,u,d,p,g,f,m]=[0,0,0,0,0,0,0,0];return this.pageViews.children.map((t,e)=>{const{width:r,height:y}=t.getBBox(),b=0===f?0:i,x=f+b+r;return x<=o&&er(p,t=>t<a)?([h,c,f]=[f+b,m,x],{width:r,height:y,x:h,y:c,page:u,index:e,pageIndex:d++,row:g,col:p++}):([g,p,f,m]=[g+1,0,0,m+y+n],m+y<=l&&er(g,t=>t<s)?([h,c,f]=[f,m,r],{width:r,height:y,x:h,y:c,page:u,index:e,pageIndex:d++,row:g,col:p++}):([h,c,f,m,u,d,g,p]=[0,0,r,0,u+1,0,0,0],{width:r,height:y,x:h,y:c,page:u,index:e,pageIndex:d++,row:g,col:p++}))})}get itemsLayout(){this.navigatorShape=[0,0];const t="grid"===this.attributes.layout?this.getGridLayout:this.getFlexLayout,e=t.call(this);return e.slice(-1)[0].page>0?(this.navigatorShape=[55,0],t.call(this)):e}ifHorizontal(t,e){const{orientation:n}=this.attributes;return Si(n,t,e)}flattenPage(t){t.querySelectorAll(tr.item.class).forEach(e=>{t.appendChild(e)}),t.querySelectorAll(tr.page.class).forEach(e=>{t.removeChild(e).destroy()})}renderItems(t){const{click:e,mouseenter:n,mouseleave:i}=this.attributes;this.flattenPage(t);const r=this.dispatchCustomEvent.bind(this);T(t).selectAll(tr.item.class).data(this.renderData,t=>t.id).join(t=>t.append(t=>{let{style:e}=t;return new Qi({style:e})}).attr("className",tr.item.name).on("click",function(){e?.(this),r("itemClick",{item:this})}).on("pointerenter",function(){n?.(this),r("itemMouseenter",{item:this})}).on("pointerleave",function(){i?.(this),r("itemMouseleave",{item:this})}),t=>t.each(function(t){let{style:e}=t;this.update(e)}),t=>t.remove())}relayoutNavigator(){const{layout:t,width:e}=this.attributes,n=this.pageViews.children[0]?.getBBox().height||0,[i,r]=this.navigatorShape;this.navigator.update("grid"===t?{pageWidth:e-i,pageHeight:n-r}:{})}adjustLayout(){const t=Object.entries(Hi(this.itemsLayout,"page")).map(t=>{let[e,n]=t;return{page:e,layouts:n}}),n=[...this.navigator.getContainer().children];t.forEach(t=>{let{layouts:i}=t;const r=this.pageViews.appendChild(new e.Group({className:tr.page.name}));i.forEach(t=>{const{x:e,y:i,index:s,width:a,height:o}=t,l=n[s];var h,c,u,d;r.appendChild(l),c=t,u=l,(d=W(h="__layout__")?h.split("."):h).forEach(function(t,e){e<d.length-1?(V(u[t])||(u[t]=E(d[e+1])?[]:{}),u=u[t]):u[t]=c}),l.update({x:e,y:i,width:a,height:o})})}),this.relayoutNavigator()}renderNavigator(t){const{orientation:e}=this.attributes,n=B(this.attributes,"nav"),i=m({orientation:e},n),r=this;return t.selectAll(tr.navigator.class).data(["nav"]).join(t=>t.append(()=>new Di({style:i})).attr("className",tr.navigator.name).each(function(){r.navigator=this}),t=>t.each(function(){this.update(i)}),t=>t.remove()),this.navigator}getBBox(){return this.navigator.getBBox()}render(t,e){const{data:n}=this.attributes;if(!n||0===n.length)return;const i=this.renderNavigator(T(e));this.renderItems(i.getContainer()),this.adjustLayout()}dispatchCustomEvent(t,n){const i=new e.CustomEvent(t,{detail:n});this.dispatchEvent(i)}}class ir extends b{constructor(t){super(t,Mi)}titleGroup;title;itemsGroup;items;renderTitle(t,e,n){const{showTitle:i,titleText:r}=this.attributes,s=B(this.attributes,"title"),[a,o]=M(s);this.titleGroup=t.maybeAppendByClassName(Pi.titleGroup,"g").styles(o);const l={width:e,height:n,...a,text:i?r:""};this.title=this.titleGroup.maybeAppendByClassName(Pi.title,()=>new fi({style:l})).update(l)}renderItems(t,e){const{x:n,y:i,width:r,height:s}=e,a=B(this.attributes,"title",!0),[o,l]=M(a),h={...o,width:r,height:s,x:0,y:0};this.itemsGroup=t.maybeAppendByClassName(Pi.itemsGroup,"g").styles({...l,transform:`translate(${n}, ${i})`});const c=this;this.itemsGroup.selectAll(Pi.items.class).data(["items"]).join(t=>t.append(()=>new nr({style:h})).attr("className",Pi.items.name).each(function(){c.items=T(this)}),t=>t.update(h),t=>t.remove())}adjustLayout(){const{showTitle:t}=this.attributes;if(t){const{x:t,y:e}=this.title.node().getAvailableSpace();this.itemsGroup.node().style.transform=`translate(${t}, ${e})`}}get availableSpace(){const{showTitle:t,width:e,height:n}=this.attributes;return t?this.title.node().getAvailableSpace():new ci(0,0,e,n)}getBBox(){const t=this.title?.node(),e=this.items?.node();return t&&e?function(t,e){const{position:n,spacing:i,inset:r,text:s}=t.attributes,a=t.getBBox(),o=e.getBBox(),l=pi(n),[h,c,u,d]=P(s?i:0),[p,g,f,m]=P(r),[y,b]=[d+c,h+u],[x,v]=[m+g,p+f];if("l"===l[0])return new ci(a.x,a.y,o.width+a.width+y+x,Math.max(o.height+v,a.height));if("t"===l[0])return new ci(a.x,a.y,Math.max(o.width+x,a.width),o.height+a.height+b+v);const[w,k]=[e.attributes.width||o.width,e.attributes.height||o.height];return new ci(o.x,o.y,w+a.width+y+x,k+a.height+b+v)}(t,e):super.getBBox()}render(t,e){const{width:n,height:i,x:r=0,y:s=0}=this.attributes,a=T(e);e.style.transform=`translate(${r}, ${s})`,this.renderTitle(a,n,i),this.renderItems(a,this.availableSpace),this.adjustLayout()}}function rr(t){var e=document.createElement("div");e.innerHTML=t;var n=e.childNodes[0];return n&&e.contains(n)&&e.removeChild(n),n}const sr=(t,e)=>{null!=e?t.replaceChildren?Array.isArray(e)?t.replaceChildren(...e):t.replaceChildren(e):(t.innerHTML="",Array.isArray(e)?e.forEach(e=>t.appendChild(e)):t.appendChild(e)):t.innerHTML=""};function ar(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return{CONTAINER:`${t}tooltip`,TITLE:`${t}tooltip-title`,LIST:`${t}tooltip-list`,LIST_ITEM:`${t}tooltip-list-item`,NAME:`${t}tooltip-list-item-name`,MARKER:`${t}tooltip-list-item-marker`,NAME_LABEL:`${t}tooltip-list-item-name-label`,VALUE:`${t}tooltip-list-item-value`,CROSSHAIR_X:`${t}tooltip-crosshair-x`,CROSSHAIR_Y:`${t}tooltip-crosshair-y`}}const or={overflow:"hidden","white-space":"nowrap","text-overflow":"ellipsis"};function lr(){const t=ar(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"");return{[`.${t.CONTAINER}`]:{position:"absolute",visibility:"visible","z-index":8,transition:"visibility 0.2s cubic-bezier(0.23, 1, 0.32, 1), left 0.4s cubic-bezier(0.23, 1, 0.32, 1), top 0.4s cubic-bezier(0.23, 1, 0.32, 1)","background-color":"rgba(255, 255, 255, 0.96)","box-shadow":"0 6px 12px 0 rgba(0, 0, 0, 0.12)","border-radius":"4px",color:"rgba(0, 0, 0, 0.65)","font-size":"12px","line-height":"20px",padding:"12px","min-width":"120px","max-width":"360px","font-family":"Roboto-Regular"},[`.${t.TITLE}`]:{color:"rgba(0, 0, 0, 0.45)"},[`.${t.LIST}`]:{margin:"0px","list-style-type":"none",padding:"0px"},[`.${t.LIST_ITEM}`]:{"list-style-type":"none",display:"flex","line-height":"2em","align-items":"center","justify-content":"space-between","white-space":"nowrap"},[`.${t.MARKER}`]:{width:"8px",height:"8px","border-radius":"50%",display:"inline-block","margin-right":"4px"},[`.${t.NAME}`]:{display:"flex","align-items":"center","max-width":"216px"},[`.${t.NAME_LABEL}`]:{flex:1,...or},[`.${t.VALUE}`]:{display:"inline-block",float:"right",flex:1,"text-align":"right","min-width":"28px","margin-left":"30px",color:"rgba(0, 0, 0, 0.85)",...or},[`.${t.CROSSHAIR_X}`]:{position:"absolute",width:"1px","background-color":"rgba(0, 0, 0, 0.25)"},[`.${t.CROSSHAIR_Y}`]:{position:"absolute",height:"1px","background-color":"rgba(0, 0, 0, 0.25)"}}}class hr extends b{static tag="tooltip";timestamp=-1;get HTMLTooltipElement(){return this.element}getContainer(){return this.element}get elementSize(){return{width:this.element.offsetWidth,height:this.element.offsetHeight}}get HTMLTooltipItemsElements(){const{data:t,template:e}=this.attributes;return t.map((t,n)=>{let{name:i="",color:r="black",index:s,...a}=t;const o={name:i,color:r,index:s??n,...a};return rr((l=e.item,h=o,l&&h?l.replace(/\\?\{([^{}]+)\}/g,function(t,e){return"\\"===t.charAt(0)?t.slice(1):void 0===h[e]?"":h[e]}):l));var l,h})}element;constructor(t){const e=t.style?.template?.prefixCls,n=ar(e);super(t,{data:[],x:0,y:0,visibility:"visible",title:"",position:"bottom-right",offset:[5,5],enterable:!1,container:{x:0,y:0},bounding:null,template:{prefixCls:"",container:`<div class="${n.CONTAINER}"></div>`,title:`<div class="${n.TITLE}"></div>`,item:`<li class="${n.LIST_ITEM}" data-index={index}>\n        <span class="${n.NAME}">\n          <span class="${n.MARKER}" style="background:{color}"></span>\n          <span class="${n.NAME_LABEL}" title="{name}">{name}</span>\n        </span>\n        <span class="${n.VALUE}" title="{value}">{value}</span>\n      </li>`},style:lr(e)}),this.initShape(),this.render(this.attributes,this)}render(t,e){this.renderHTMLTooltipElement(),this.updatePosition()}destroy(){this.element?.remove(),super.destroy()}show(t,e){if(void 0!==t&&void 0!==e){const n=()=>{this.attributes.x=t??this.attributes.x,this.attributes.y=e??this.attributes.y,this.updatePosition()};"hidden"===this.element.style.visibility?this.closeTransition(n):n()}this.element.style.visibility="visible"}hide(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;const{enterable:n}=this.attributes;n&&this.isCursorEntered(t,e)||(this.element.style.visibility="hidden")}initShape(){const{template:t}=this.attributes;this.element=rr(t.container),this.id&&this.element.setAttribute("id",this.id)}prevCustomContentKey=this.attributes.contentKey;renderCustomContent(){if(void 0!==this.prevCustomContentKey&&this.prevCustomContentKey===this.attributes.contentKey)return;this.prevCustomContentKey=this.attributes.contentKey;const{content:t}=this.attributes;t&&("string"==typeof t?this.element.innerHTML=t:sr(this.element,t))}renderHTMLTooltipElement(){const{template:t,title:e,enterable:n,style:i,content:r}=this.attributes,s=ar(t.prefixCls),a=this.element;if(this.element.style.pointerEvents=n?"auto":"none",r)this.renderCustomContent();else{e?(a.innerHTML=t.title,a.getElementsByClassName(s.TITLE)[0].innerHTML=e):a.getElementsByClassName(s.TITLE)?.[0]?.remove();const n=this.HTMLTooltipItemsElements,i=document.createElement("ul");i.className=s.LIST,sr(i,n);const r=this.element.querySelector(`.${s.LIST}`);r?r.replaceWith(i):a.appendChild(i)}A(a,i)}getRelativeOffsetFromCursor(t){const{position:e,offset:n}=this.attributes,i=(t||e).split("-"),r={left:[-1,0],right:[1,0],top:[0,-1],bottom:[0,1]},{width:s,height:a}=this.elementSize;let o=[-s/2,-a/2];return i.forEach(t=>{const[e,i]=o,[l,h]=r[t];o=[e+(s/2+n[0])*l,i+(a/2+n[1])*h]}),o}setOffsetPosition(t){let[e,n]=t;const{x:i=0,y:r=0,container:{x:s,y:a}}=this.attributes;this.element.style.left=`${+i+s+e}px`,this.element.style.top=`${+r+a+n}px`}updatePosition(){const{showDelay:t=60}=this.attributes,e=Date.now();this.timestamp>0&&e-this.timestamp<t||(this.timestamp=e,this.setOffsetPosition(this.autoPosition(this.getRelativeOffsetFromCursor())))}autoPosition(t){let[e,n]=t;const{x:i,y:r,bounding:s,position:a}=this.attributes;if(!s)return[e,n];const{offsetWidth:o,offsetHeight:l}=this.element,[h,c]=[+i+e,+r+n],u={left:"right",right:"left",top:"bottom",bottom:"top"},{x:d,y:p,width:g,height:f}=s,m={left:h<d,right:h+o>d+g,top:c<p,bottom:c+l>p+f},y=[];a.split("-").forEach(t=>{m[t]?y.push(u[t]):y.push(t)});const b=y.join("-");return this.getRelativeOffsetFromCursor(b)}isCursorEntered(t,e){if(this.element){const{x:n,y:i,width:r,height:s}=this.element.getBoundingClientRect();return new ci(n,i,r,s).isPointIn(t,e)}return!1}closeTransition(t){const e=this.element.style.transition;this.element.style.transition="none",t(),setTimeout(()=>{this.element.style.transition=e},10)}}const cr={default:{sizeStyle:{width:44,height:22,radius:11},tagStyle:{textStyle:{fontSize:12,lineHeight:16,fill:"#fff"},padding:0},markerStyle:{size:11}},small:{sizeStyle:{width:28,height:16,radius:8},tagStyle:{textStyle:{fontSize:10,lineHeight:14,fill:"#fff"},padding:0},markerStyle:{size:8}},mini:{sizeStyle:{width:20,height:14,radius:7},tagStyle:{textStyle:{fontSize:7,lineHeight:10,fill:"#fff"},padding:0},markerStyle:{size:7}}};function ur(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const i=Number(t.style.height)-2*e;return{x:n?Number(t.style.width)+Number(t.style.x)-e-i:Number(t.style.x)+e,y:Number(t.style.y)+e,width:i,height:i,radius:i/2}}class dr extends b{static tag="switch";checked;constructor(t){super(t,{x:0,y:0,size:"default",spacing:2,checked:!0,disabled:!1})}render(t,e){const{size:n,spacing:i,disabled:r,checked:s,unCheckedChildren:a,checkedChildren:o}=t,l=T(e).maybeAppendByClassName("switch-content","g").node(),h=l.getLocalBounds(),{sizeStyle:c,tagStyle:u}=Ze(cr,n,cr.default),d=r?"no-drop":"pointer",p=s?"#1890FF":"#00000040";let g=c;const f=s?o:a;(o||a)&&T(l).maybeAppendByClassName("switch-tag",()=>new j({})).call(t=>{const e=t.node();e.update({cursor:d,backgroundStyle:null,text:!1,marker:!1,...u,...f});const{max:n,min:i}=e?.getLocalBounds()||{},r=n[0]-i[0]+c.radius,a=n[1]-i[1],o=Math.max(r+c.height+2,c.width);g={...c,width:o},e.update(function(t,e){let{width:n,height:i}=e,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return{x:arguments.length>3&&void 0!==arguments[3]&&!arguments[3]?Number(t.width)+Number(t.x)-n:Number(t.x)+r,y:Number(t.y)+(Number(t.height)-i)/2}}({x:h.min[0],y:h.min[1],width:o,height:g.height},{width:r,height:a},g.radius,s))});const m=T(l).maybeAppendByClassName("switch-background","rect").styles({zIndex:(l.style.zIndex||0)-1,x:h.min[0],y:h.min[1],fill:p,cursor:d,fillOpacity:r?.4:1,...g}).node(),y=T(l).maybeAppendByClassName("switch-background-stroke","rect").styles({zIndex:(l.style.zIndex||0)-2,x:h.min[0],y:h.min[1],stroke:p,lineWidth:0,...g}).node();T(l).maybeAppendByClassName("switch-handle","rect").styles({fill:"#fff",cursor:d}).call(t=>{const e=t.node(),n=ur(m,i,s),r=ur(m,i,!s);e.attr("x")&&!Dt(n,r)&&this.checked!==s?(e.attr(r),e.getAnimations()[0]?.cancel(),y.getAnimations()[0]?.cancel(),e.animate([{x:r.x},{x:n.x}],{duration:120,fill:"both"}),y.animate([{lineWidth:0,strokeOpacity:.5},{lineWidth:14,strokeOpacity:0}],{duration:400,easing:"ease-on"})):e.attr(n)}),this.checked=!!s}}const pr={tagText:"",lineStroke:"#416180",lineStrokeOpacity:.45,lineLineWidth:1,lineLineDash:[5,5]},gr=m({},pr,{type:"line",tagPosition:"start",tagAlign:"center",tagVerticalAlign:"bottom"}),fr=m({},pr,{type:"circle",defaultRadius:0}),mr=m({},pr,{type:"polygon",defaultRadius:0,startAngle:0});class yr extends b{static tag="crosshair-base";pointer;shapesGroup;tagShape;crosshairShape;get localPointer(){const[t,e]=this.getPosition(),[n,i]=this.pointer;return[n-t,i-e]}get tagStyle(){return B(this.attributes,"tag")}get crosshairStyle(){return{...B(this.attributes,"line"),d:this.crosshairPath}}constructor(t){super(t,pr)}render(t,e){const n=T(e).maybeAppendByClassName(".crosshair-group","g").node();this.shapesGroup=n;const i=this.tagStyle,r=this.crosshairStyle;this.tagShape=T(n).maybeAppendByClassName("crosshair-tag",()=>new j({style:i})).styles(i).node(),this.crosshairShape=T(n).maybeAppendByClassName(".crosshair-path","path").styles(r).node(),this.adjustLayout()}setPointer(t){this.pointer=t}}class br extends yr{static tag="line-crosshair";static defaultOptions=(()=>({style:gr}))();get crosshairPath(){const{startPos:[t,e],endPos:[n,i]}=this.attributes;return[["M",0,0],["L",n-t,i-e],["Z"]]}get localPointer(){if(!this.pointer)return this.attributes.startPos;const[t,e]=this.getPosition(),[n,i]=this.pointer;return[n-t,i-e]}get isVertical(){const{startPos:[t,e],endPos:[n,i]}=this.attributes;return t===n&&e!==i}get tagShapeSpace(){const{width:t,height:e}=Rn(this.tagShape);return{width:t,height:e}}constructor(t){super(m({},br.defaultOptions,t))}update(t){super.update(t)}setPointer(t){super.setPointer(t),this.adjustPosition()}setText(t){this.tagShape.update({text:t}),this.adjustTag()}adjustLayout(){this.adjustPosition(),this.adjustTag()}adjustPosition(){const[t,e]=this.localPointer,{startPos:[n,i]}=this.attributes,r=this.getOrientVal([n,e],[t,i]);this.shapesGroup.setLocalPosition(r)}adjustTag(){const{tagText:t,tagPosition:e,startPos:[n,i],endPos:[r,o]}=this.attributes;if(!t||""===t)return void a(this.tagShape);s(this.tagShape);const{width:l,height:h}=this.tagShapeSpace,[c,u]=this.getOrientVal({start:[-l/2,h/2],end:[r-n+l/2,h/2]},{start:[0,0],end:[0,o-i+h]})[e];this.tagShape.setLocalPosition(c,u)}getOrientVal(t,e){return this.isVertical?e:t}}class xr extends yr{static tag="circle-crosshair";static defaultOptions=(()=>({style:fr}))();get crosshairPath(){return this.createCirclePath()}constructor(t){super(m({},xr.defaultOptions,t))}update(t){super.update(t)}setPointer(t){let[e,n]=t;super.setPointer([e,n]);const[i,r]=this.localPointer,{center:[s,a]}=this.attributes,o=this.createCirclePath(((i-s)**2+(r-a)**2)**.5);this.crosshairShape.attr({d:o})}adjustLayout(){a(this.tagShape)}createCirclePath(t){const{center:[e,n],defaultRadius:i}=this.attributes;return _(e,n,t||i)}}function vr(t,e){let[n,i]=t,[r,s]=e;return((n-r)**2+(i-s)**2)**.5}function wr(t,e,n,i,r,s){return(t===e||Math.min(t,e)<=r&&r<=Math.max(t,e))&&(n===i||Math.min(n,i)<=s&&s<=Math.max(n,i))}function kr(t,e,n){const i=t;(!t.length||e<t[0]||e===t[0]&&n<t[1])&&(i[0]=e,i[1]=n)}function Sr(t,e,n,i){let[r,s]=t,[a,o]=e,[l,h]=n,[c,u]=i;const d=[];if((u-h)*(a-r)===(o-s)*(c-l))(o-s)*(l-r)===(h-s)*(a-r)&&(wr(r,a,s,o,l,h)&&kr(d,l,h),wr(r,a,s,o,c,u)&&kr(d,c,u),wr(l,c,h,u,r,s)&&kr(d,r,s),wr(l,c,h,u,a,o)&&kr(d,a,o));else{const t=(l*(u-h)+s*(c-l)-h*(c-l)-r*(u-h))/((a-r)*(u-h)-(c-l)*(o-s)),e=(r*(o-s)+h*(a-r)-s*(a-r)-l*(o-s))/((c-l)*(o-s)-(a-r)*(u-h));t>=0&&t<=1&&e>=0&&e<=1&&(d[0]=r+t*(a-r),d[1]=s+t*(o-s))}return d}class Ar extends yr{static tag="polygon-crosshair";static defaultOptions=(()=>({style:mr}))();get crosshairPath(){return this.createPolygonPath()}constructor(t){super(m({},Ar.defaultOptions,t))}update(t){super.update(t)}get points(){const{startAngle:t,sides:e}=this.attributes,n=2*Math.PI/e,i=[1,0],r=[];for(let s=0;s<e;s+=1)r.push(Xt(i,[0,0],t/180*Math.PI+n*s));return r}setPointer(t){let[e,n]=t;super.setPointer([e,n]);const[i,r]=this.localPointer,{center:s}=this.attributes,[a,o]=this.intersection([i,r]);if(!a||!o)return;const l=vr(s,[i,r])/vr(s,[a,o]),h=this.createPolygonPath(l);this.crosshairShape.attr({d:h})}adjustLayout(){a(this.tagShape)}createPolygonPath(t){const{defaultRadius:e,center:[n,i]}=this.attributes,r=this.points.map((r,s)=>{let[a,o]=r;const[l,h]=jt([a,o],t||e);return[0===s?"M":"L",n+l,i+h]});return r.push(["Z"]),r}intersection(t){let[e,n]=t;const{points:i}=this,{center:[r,s]}=this.attributes;let a,o;for(let t=1;t<=i.length;t+=1){const[l,h]=i[t-1],[c,u]=i[t%i.length],d=Sr([e,n],[r,s],[l+r,h+s],[c+r,u+s]);0!==d.length&&([a,o]=d)}return[a,o]}}const Lr=C({fill:"rgba(0,0,0,0.45)",fontSize:10,textAlign:"start",textBaseline:"middle",overflow:"clip"},"label"),Br={width:12,height:12,radius:2,stroke:"#dadada",lineWidth:1,fill:"#ffffff",cursor:"pointer"},Cr={width:12,height:12,radius:2,stroke:"#3471F9",lineWidth:1,fill:"#3471F9",cursor:"pointer"},Mr={d:[["M",3,6],["L","5","8.5"],["L","8.5","4"]],lineWidth:1,cursor:"pointer"};class Er extends b{static tag="checkbox";checkboxBoxShape;checked;constructor(t){super(t,{labelText:"",spacing:4,checked:!1,...Lr})}render(t,e){const{checked:n,spacing:i}=t;this.checked=!!n;const r=O(e,".checkbox-content","g").attr("className","checkbox-content").node(),s=B(t,"box"),a=B(t,"checked"),o=B(t,"label"),l={...this.checked?Cr:Br,...s},h={...Mr,...a};this.checkboxBoxShape=O(r,".checkbox-box","rect").styles({className:"checkbox-box",zIndex:(r.style.zIndex||0)-1,...l}).node(),O(this.checkboxBoxShape,".checkbox-checked","path").styles({className:"checkbox-box-checked",stroke:"#fff",...h});const{x:c,y:u}=function(t,e){const n=t.getLocalBounds();return{x:n.halfExtents[0]?n.max[0]+(e||0):t.style.x,y:n.halfExtents[1]?(n.min[1]+n.max[1])/2:t.style.y}}(this.checkboxBoxShape,Number(i));O(r,".checkbox-label","text").styles({className:"checkbox-label",x:c,y:u,...o})}}function Pr(t){let e=1/0,n=1/0,i=-1/0,r=-1/0;for(let s=0;s<t.length;s++){const{x:a,y:o,width:l,height:h}=t[s],[c,u]=[a+l,o+h];a<e&&(e=a),o<n&&(n=o),c>i&&(i=c),u>r&&(r=u)}return new ci(e,n,i-e,r-n)}const $r=function(t,e,n){const{width:i,height:r}=t,{flexDirection:s="row",flexWrap:a="nowrap",justifyContent:o="flex-start",alignContent:l="flex-start",alignItems:h="flex-start"}=n,c="row"===s,u="row"===s||"column"===s,d=c?u?[1,0]:[-1,0]:u?[0,1]:[0,-1];let[p,g]=[0,0];const f=e.map(t=>{const{width:e,height:n}=t,[i,r]=[p,g];return[p,g]=[p+e*d[0],g+n*d[1]],new ci(i,r,e,n)}),m=Pr(f),y={"flex-start":0,"flex-end":c?i-m.width:r-m.height,center:c?(i-m.width)/2:(r-m.height)/2},b=f.map(t=>{const{x:e,y:n}=t,i=ci.fromRect(t);return i.x=c?e+y[o]:e,i.y=c?n:n+y[o],i}),x=(Pr(b),t=>{const[e,n]=c?["height",r]:["width",i];switch(h){case"flex-start":default:return 0;case"flex-end":return n-t[e];case"center":return n/2-t[e]/2}});return b.map(t=>{const{x:e,y:n}=t,i=ci.fromRect(t);return i.x=c?e:e+x(i),i.y=c?n+x(i):n,i}).map(e=>{const n=ci.fromRect(e);return n.x+=t.x??0,n.y+=t.y??0,n})},Tr=function(t,e,n){return[]},Or=(t,e,n)=>{if(0===e.length)return[];const i={flex:$r,grid:Tr},r=n.display in i?i[n.display]:null;return r?.call(null,t,e,n)||[]};class Nr extends e.Group{layoutEvents=(()=>[e.ElementEvent.BOUNDS_CHANGED,e.ElementEvent.INSERTED,e.ElementEvent.REMOVED])();$margin=(()=>P(0))();$padding=(()=>P(0))();set margin(t){this.$margin=P(t)}get margin(){return this.$margin}set padding(t){this.$padding=P(t)}get padding(){return this.$padding}getBBox(){const{x:t=0,y:e=0,width:n,height:i}=this.attributes,[r,s,a,o]=this.$margin;return new ci(t-o,e-r,n+o+s,i+r+a)}appendChild(t,e){return t.isMutationObserved=!0,super.appendChild(t,e),t}getAvailableSpace(){const{width:t,height:e}=this.attributes,[n,i,r,s]=this.$padding,[a,,,o]=this.$margin;return new ci(s+o,n+a,t-s-i,e-n-r)}constructor(t){super(t);const{margin:e=0,padding:n=0}=t.style||{};this.margin=e,this.padding=n,this.isMutationObserved=!0,this.bindEvents()}layout(){if(this.attributes.display&&this.isConnected&&!this.children.some(t=>!t.isConnected))try{const{x:t,y:e}=this.attributes;this.style.transform=`translate(${t}, ${e})`;const n=Or(this.getAvailableSpace(),this.children.map(t=>t.getBBox()),this.attributes);this.children.forEach((t,e)=>{const{x:i,y:r}=n[e];t.style.transform=`translate(${i}, ${r})`})}catch(t){}}bindEvents(){this.layoutEvents.forEach(t=>{this.addEventListener(t,t=>{t.target&&(t.target.isMutationObserved=!0,this.layout())})})}attributeChangedCallback(t,e,n){"margin"===t?this.margin=n:"padding"===t&&(this.padding=n),this.layout()}}class zr extends b{static defaultOptions={style:{value:"",label:"",cursor:"pointer"}};hoverColor="#f5f5f5";selectedColor="#e6f7ff";background=(()=>this.appendChild(new e.Rect({})))();label=(()=>this.background.appendChild(new e.Group({})))();get padding(){return P(this.style.padding)}renderLabel(){const{label:t,value:e}=this.style,n=B(this.attributes,"label");T(this.label).maybeAppend(".label",()=>en(t)).attr("className","label").styles(n),this.label.attr("__data__",e)}renderBackground(){const t=this.label.getBBox(),[e,n,i,r]=this.padding,{width:s,height:a}=t,o=s+r+n,l=a+e+i,h=B(this.attributes,"background"),{width:c=0,height:u=0,selected:d}=this.style;this.background.attr({...h,width:Math.max(o,c),height:Math.max(l,u),fill:d?this.selectedColor:"#fff"}),this.label.attr({transform:`translate(${r}, ${(l-a)/2})`})}constructor(t){super(m({},zr.defaultOptions,t))}render(){this.renderLabel(),this.renderBackground()}bindEvents(){this.addEventListener("pointerenter",()=>{this.style.selected||this.background.attr("fill",this.hoverColor)}),this.addEventListener("pointerleave",()=>{this.style.selected||this.background.attr("fill",this.style.backgroundFill)});const t=this;this.addEventListener("click",()=>{const{label:e,value:n,onClick:i}=this.style;i?.(n,{label:e,value:n},t)})}}class _r extends b{static defaultOptions={style:{x:0,y:0,width:140,height:32,options:[],bordered:!0,defaultValue:"",selectRadius:8,selectStroke:"#d9d9d9",showDropdownIcon:!0,placeholderText:"请选择",placeholderFontSize:12,placeholderTextBaseline:"top",placeholderFill:"#c2c2c2",dropdownFill:"#fff",dropdownStroke:"#d9d9d9",dropdownRadius:8,dropdownShadowBlur:4,dropdownShadowColor:"rgba(0, 0, 0, 0.08)",dropdownPadding:8,dropdownSpacing:10,optionPadding:[8,12],optionFontSize:12,optionTextBaseline:"top",optionBackgroundFill:"#fff",optionBackgroundRadius:4,optionLabelFontSize:12,optionLabelTextBaseline:"top"}};currentValue=(()=>_r.defaultOptions.style?.defaultValue)();isPointerInSelect=!1;setValue(t){this.currentValue=t,this.render()}getValue(){return this.currentValue}get dropdownPadding(){return P(this.style.dropdownPadding)}select=(()=>this.appendChild(new e.Rect({className:"select",style:{cursor:"pointer",width:0,height:0}})))();dropdown=(()=>this.appendChild(new e.Rect({className:"dropdown"})))();renderSelect(){const{x:t,y:e,width:n,height:i,bordered:r,showDropdownIcon:s}=this.style,a=B(this.attributes,"select"),o=B(this.attributes,"placeholder");this.select.attr({x:t,y:e,width:n,height:i,...a,fill:"#fff",strokeWidth:r?1:0});const l=this.dropdownPadding;s&&T(this.select).maybeAppend(".dropdown-icon","path").style("d","M-5,-3.5 L0,3.5 L5,-3.5").style("transform",`translate(${t+n-10-l[1]-l[3]}, ${e+i/2})`).style("lineWidth",1).style("stroke",this.select.style.stroke);const h=this.style.options?.find(t=>t.value===this.currentValue),c={x:t+l[3],...o};T(this.select).selectAll(".placeholder").data(h?[]:[1]).join(t=>t.append("text").attr("className","placeholder").styles(c).style("y",function(){const t=this.getBBox();return e+(i-t.height)/2}),t=>t.styles(c),t=>t.remove());const u=B(this.attributes,"optionLabel"),d={x:t+l[3],...u};T(this.select).selectAll(".value").data(h?[h]:[]).join(t=>t.append(t=>en(t.label)).attr("className","value").styles(d).style("y",function(){const t=this.getBBox();return e+(i-t.height)/2}),t=>t.styles(d),t=>t.remove())}renderDropdown(){const{x:t,y:n,width:i,height:r,options:s,onSelect:o,open:l}=this.style,h=B(this.attributes,"dropdown"),c=B(this.attributes,"option"),u=this.dropdownPadding;T(this.dropdown).maybeAppend(".dropdown-container","g").attr("className","dropdown-container").selectAll(".dropdown-item").data(s,t=>t.value).join(t=>t.append(t=>new zr({className:"dropdown-item",style:{...t,...c,width:i-u[1]-u[3],selected:t.value===this.currentValue,onClick:(t,n,i)=>{this.setValue(t),o?.(t,n,i),this.dispatchEvent(new e.CustomEvent("change",{detail:{value:t,option:n,item:i}})),a(this.dropdown)}}})).each(function(t,e){const n=this.parentNode?.children,i=n.reduce((t,n,i)=>(i<e&&(t+=n.getBBox().height),t),0);this.attr("transform",`translate(${u[3]}, ${u[0]+i})`)}),t=>t.update(t=>({selected:t.value===this.currentValue})),t=>t.remove());const d=this.dropdown.getElementsByClassName("dropdown-container")?.[0]?.getBBox(),{spacing:p}=h;this.dropdown.attr({transform:`translate(${t}, ${n+r+p})`,width:d.width+u[1]+u[3],height:d.height+u[0]+u[2],...h}),!l&&a(this.dropdown)}constructor(t){super(m({},_r.defaultOptions,t));const{defaultValue:e}=this.style;e&&this.style.options?.some(t=>t.value===e)&&(this.currentValue=e)}render(){this.renderSelect(),this.renderDropdown()}bindEvents(){this.addEventListener("click",t=>{t.stopPropagation()}),this.select.addEventListener("click",()=>{"visible"===this.dropdown.style.visibility?a(this.dropdown):s(this.dropdown)}),this.addEventListener("pointerenter",()=>{this.isPointerInSelect=!0}),this.addEventListener("pointerleave",()=>{this.isPointerInSelect=!1}),document?.addEventListener("click",()=>{this.isPointerInSelect||a(this.dropdown)})}}const Ir=["year","month","day","hour","minute","second"],Fr=["YYYY","MM","DD","hh","mm","ss"];function Gr(t){return t instanceof Date?t:new Date(t)}function Hr(t){let[e,n]=t;const i=Ir.indexOf(e),r=Ir.indexOf(n);let s="";for(let t=i;t<=r;t+=1)if(s+=Fr[t],t<r){let e="-";2===t?e=" ":t>2&&(e=":"),s+=e}return s}function Vr(t,e){const n={YYYY:t.getFullYear(),MM:t.getMonth()+1,DD:t.getDate(),HH:t.getHours(),mm:t.getMinutes(),ss:t.getSeconds()};let i=e;return Object.keys(n).forEach(t=>{const e=n[t];i=i.replace(t,"YYYY"===t?`${e}`:`${e}`.padStart(2,"0"))}),i}function Wr(t,e){return Gr(t).getTime()-Gr(e).getTime()}function Dr(t,e){const[n,i]=[Gr(t),Gr(e)];return n.getFullYear()!==i.getFullYear()?"year":n.getMonth()!==i.getMonth()?"month":n.getDay()!==i.getDay()?"day":n.getHours()!==i.getHours()?"hour":n.getMinutes()!==i.getMinutes()?"minute":"second"}function jr(t,e){const n=new Date(t);return{year:t=>{t.setMonth(0),t.setHours(0,0,0,0)},month:t=>{t.setDate(1),t.setHours(0,0,0,0)},day:t=>t.setHours(0,0,0,0),hour:t=>t.setMinutes(0,0,0),minute:t=>t.setSeconds(0,0),second:t=>t.setMilliseconds(0)}[e](n),Vr(n,Hr(["year",e]))}class Rr extends b{static tag="IconBase";static defaultOptions={style:{x:0,y:0,size:10,color:"#565758",backgroundRadius:4,backgroundFill:"#e2e2e2"}};static backgroundOpacities={default:0,hover:.8,active:1};showBackground=!0;get label(){return"BaseIcon"}indicator;background=(()=>this.appendChild(new e.Rect({})))();icon=(()=>this.appendChild(new e.Group({})))();get lineWidth(){return Math.log10(this.attributes.size)}get padding(){return P(this.attributes.size/5)}get iconSize(){const{size:t}=this.attributes,[e,n,i,r]=this.padding;return Math.max(t-Math.max(r+n,e+i),2*this.lineWidth+1)}renderBackground(){const{x:t,y:e,size:n}=this.attributes,i=n/2,r=B(this.attributes,"background");this.background.attr({x:t-i,y:e-i,width:n,height:n,...r})}showIndicator(){if(!this.label)return;const{size:t}=this.attributes,{x:e,y:n}=this.background.getBBox();this.indicator.update({x:e+t/2,y:n-5,labelText:this.label,visibility:"visible"})}hideIndicator(){this.indicator.update({visibility:"hidden"})}constructor(t){super(m({},{style:{backgroundOpacity:Rr.backgroundOpacities.default}},Rr.defaultOptions,t))}connectedCallback(){super.connectedCallback();const{size:t}=this.attributes,{x:e,y:n}=this.background.getBBox(),i=this.ownerDocument?.defaultView;i&&(this.indicator=i.appendChild(new ki({style:{x:e+t/2,y:n-t/2,visibility:"hidden",position:"top",radius:3,zIndex:100}})))}disconnectedCallback(){this.indicator.destroy()}render(){this.renderIcon(),this.showBackground&&this.renderBackground()}bindEvents(){const{onClick:t}=this.attributes;if(this.addEventListener("click",()=>{t?.(this)}),this.showBackground){const t=()=>this.background.attr({opacity:Rr.backgroundOpacities.default}),e=()=>this.background.attr({opacity:Rr.backgroundOpacities.hover}),n=()=>this.background.attr({opacity:Rr.backgroundOpacities.active});this.addEventListener("pointerenter",()=>{e(),this.showIndicator()}),this.addEventListener("pointerleave",()=>{t(),this.hideIndicator()}),this.addEventListener("pointerdown",()=>{n()}),this.addEventListener("pointerup",()=>{t()})}}}const Yr=function(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"#565758";return new e.Path({style:{fill:n,d:`M ${t},${t} L -${t},0 L ${t},-${t} Z`,transformOrigin:"center"}})};class qr extends Rr{get label(){return"播放"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,i=this.iconSize,r=i/3*3**.5*.8,s=[[t+r,e],[t-r/2,e-i/2*.8],[t-r/2,e+i/2*.8],[t+r,e]];T(this.icon).maybeAppend(".play","polygon").styles({points:s,fill:n})}}class Kr extends Rr{get label(){return"暂停"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,i=this.iconSize,r=i/3,s=[[t-r,e-i/2],[t-r,e+i/2],[t-r/2,e+i/2],[t-r/2,e-i/2],[t-r,e-i/2],[t+r/2,e-i/2],[t+r/2,e+i/2],[t+r,e+i/2],[t+r,e-i/2]];T(this.icon).maybeAppend(".pause","polygon").styles({points:s,fill:n})}}class Zr extends Rr{get label(){return"范围时间"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,{iconSize:i,lineWidth:r}=this,s=r;T(this.icon).maybeAppend(".left-line","line").styles({x1:t-i/2,y1:e-i/2,x2:t-i/2,y2:e+i/2,stroke:n,lineWidth:r}),T(this.icon).maybeAppend(".right-line","line").styles({x1:t+i/2,y1:e-i/2,x2:t+i/2,y2:e+i/2,stroke:n,lineWidth:r}),T(this.icon).maybeAppend(".left-arrow","line").styles({x1:t,y1:e,x2:t-i/2+2*s,y2:e,stroke:n,lineWidth:r,markerEnd:Yr(2*r,n)}),T(this.icon).maybeAppend(".right-arrow","line").styles({x1:t,y1:e,x2:t+i/2-2*s,y2:e,stroke:n,lineWidth:r,markerEnd:Yr(2*r,n)})}}class Ur extends Rr{get label(){return"单一时间"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,{iconSize:i,lineWidth:r}=this;T(this.icon).maybeAppend(".line","line").styles({x1:t,y1:e-i/2,x2:t,y2:e+i/2,stroke:n,lineWidth:r});const s=r;T(this.icon).maybeAppend(".left-arrow","line").styles({x1:t-i/2-2*s,y1:e,x2:t-2*s,y2:e,stroke:n,lineWidth:r,markerEnd:Yr(2*r,n)}),T(this.icon).maybeAppend(".right-arrow","line").styles({x1:t+i/2+2*s,y1:e,x2:t+2*s,y2:e,stroke:n,lineWidth:r,markerEnd:Yr(2*r,n)})}}const Xr=t=>[[-t/2,-t/2],[-t/2,t/2],[t/2,t/2]];class Jr extends Rr{get label(){return"折线图"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,{iconSize:i,lineWidth:r}=this,s=r,a=(i-2*s-r)/4,o=(i-2*s-r)/2,[l,h]=[t-i/2+s,e+i/2-2*s];T(this.icon).maybeAppend(".coordinate","polyline").styles({points:Xr(i).map(n=>{let[i,r]=n;return[i+t,r+e]}),stroke:n,lineWidth:r}),T(this.icon).maybeAppend(".line","polyline").styles({points:[[l,h],[l+a,h-o],[l+2*a,h],[l+4*a,h-2*o]],stroke:n,lineWidth:r})}}class Qr extends Rr{get label(){return"条形图"}get data(){return[1,4,2,4,3]}renderIcon(){const{data:t}=this,{x:e,y:n,color:i}=this.attributes,{iconSize:r,lineWidth:s}=this,a=s,o=(r-a)/t.length,l=(r-2*a)/4,[h,c]=[e-r/2+2*a,n+r/2-a];T(this.icon).maybeAppend(".coordinate","polyline").styles({points:Xr(r).map(t=>{let[i,r]=t;return[i+e,r+n]}),stroke:i,lineWidth:s}),T(this.icon).maybeAppend(".bars","g").selectAll(".column").data(this.data.map((t,e)=>({value:t,index:e}))).join(t=>t.append("line").attr("className","column").style("x1",t=>{let{index:e}=t;return h+o*e}).style("y1",c).style("x2",t=>{let{index:e}=t;return h+o*e}).style("y2",t=>{let{value:e}=t;return c-l*e}).styles({y1:c,stroke:i,lineWidth:s}))}}class ts extends Rr{static tag="SpeedSelect";showBackground=!1;get padding(){return P(0)}renderIcon(){const{iconSize:t}=this,{x:e,y:n,speed:i=1}=this.attributes,r=(s=this.attributes,a=["x","y","transform","transformOrigin","width","height","size","color","speed"],function(t,e,n){if(!g(t)&&!p(t))return t;var i=n;return Q(t,function(t,n){i=e(i,t,n)}),i}(s,function(t,e,n){return a.includes(n)||(t[n]=e),t},{}));var s,a;const o=Ht(t,20,1/0),l={...r,x:e-o/2,y:n-10,width:o,height:20,defaultValue:i,bordered:!1,showDropdownIcon:!1,selectRadius:2,dropdownPadding:this.padding,dropdownRadius:2,dropdownSpacing:t/5,placeholderFontSize:t/2,optionPadding:0,optionLabelFontSize:t/2,optionBackgroundRadius:1,options:[{label:"1x",value:1},{label:"1.5x",value:1.5},{label:"2x",value:2}]};T(this.icon).maybeAppend(".speed",()=>new _r({style:l})).attr("className","speed").each(function(){this.update(l)})}}class es extends b{static tag="ToggleIcon";icon=(()=>this.appendChild(new e.Group({})))();currentType;getType(){return this.currentType}constructor(t){super(t),this.currentType=this.attributes.type}render(){const{onChange:t,...e}=this.attributes;T(this.icon).selectAll(".icon").data([this.currentType]).join(t=>t.append(t=>{const e=this.toggles.find(e=>{let[n]=e;return n===t})?.[1];if(!e)throw new Error(`Invalid type: ${t}`);return new e({})}).attr("className","icon").styles(e,!1).update({}),t=>t.styles({restStyles:e}).update({}),t=>t.remove())}bindEvents(){const{onChange:t}=this.attributes;this.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation();const n=(this.toggles.findIndex(t=>{let[e]=t;return e===this.currentType})+1)%this.toggles.length,i=this.toggles[n][0];t?.(this.currentType),this.currentType=i,this.render()})}}class ns extends es{toggles=(()=>[["play",qr],["pause",Kr]])();constructor(t){super(m({},{style:{type:"play"}},t))}}class is extends es{toggles=(()=>[["range",Zr],["value",Ur]])();constructor(t){super(m({},{style:{type:"range"}},t))}}class rs extends es{toggles=(()=>[["line",Jr],["column",Qr]])();constructor(t){super(m({},{style:{type:"column"}},t))}}const ss={reset:class extends Rr{arcPath(t,e,n){const[i,r]=[n,n],s=i=>[t+n*Math.cos(i),e+n*Math.sin(i)],[a,o]=s(-5/4*Math.PI),[l,h]=s(1/4*Math.PI);return`M${a},${o},A${i},${r},0,1,1,${l},${h}`}get label(){return"重置"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,i=this.iconSize,{lineWidth:r}=this,s=r+.5;T(this.icon).maybeAppend(".reset","path").styles({stroke:n,lineWidth:r,d:this.arcPath(t,e,i/2-r),markerStart:Yr(s,n)})}},speed:ts,backward:class extends Rr{get label(){return"快退"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,i=this.iconSize,r=i/2,s=i/2/3**.5,a=[[t,e],[t,e-s],[t-r,e],[t,e+s],[t,e],[t+r,e-s],[t+r,e+s],[t,e]];T(this.icon).maybeAppend(".backward","polygon").styles({points:a,fill:n})}},playPause:ns,forward:class extends Rr{get label(){return"快进"}renderIcon(){const{x:t,y:e,color:n}=this.attributes,i=this.iconSize,r=i/2,s=i/2/3**.5,a=[[t,e],[t,e-s],[t+r,e],[t,e+s],[t,e],[t-r,e-s],[t-r,e+s],[t,e]];T(this.icon).maybeAppend(".forward","polygon").styles({points:a,fill:n})}},selectionType:is,chartType:rs,split:class extends Rr{showBackground=!1;constructor(t){super(m({},{style:{color:"#d8d9d9"}},t))}renderIcon(){const{x:t,y:e,color:n}=this.attributes,{iconSize:i,lineWidth:r}=this;T(this.icon).maybeAppend(".split","line").styles({x1:t,y1:e-i/2,x2:t,y2:e+i/2,stroke:n,lineWidth:r})}}};class as extends b{static defaultOptions={style:{x:0,y:0,width:300,height:40,padding:0,align:"center",iconSize:25,iconSpacing:0,speed:1,state:"pause",chartType:"line",selectionType:"range",backgroundFill:"#fbfdff",backgroundStroke:"#ebedf0",functions:[["reset","speed"],["backward","playPause","forward"],["selectionType","chartType"]]}};background=(()=>this.appendChild(new e.Rect({})))();functions=(()=>this.appendChild(new e.Group({})))();speedSelect;get padding(){return P(this.attributes.padding)}renderBackground(){const{x:t,y:e,width:n,height:i}=this.style,r=B(this.attributes,"background");this.background.attr({x:t,y:e,width:n,height:i,...r})}renderFunctions(){const{functions:t,iconSize:e,iconSpacing:n,x:i,y:r,width:s,height:a,align:o}=this.attributes,{padding:[,l,,h]}=this,c=t.reduce((t,e)=>t.length&&e.length?t.concat("split",...e):t.concat(...e),[]),u=c.length*(e+n)-n,d={left:h+e/2,center:(s-u)/2+e/2,right:s-u-h-l+e/2}[o]||0;this.speedSelect?.destroy(),this.functions.removeChildren(),c.forEach((t,s)=>{const o=ss[t],l={x:i+s*(e+n)+d,y:r+a/2,size:e};if(o===ts?(l.speed=this.attributes.speed,l.onSelect=e=>this.handleFunctionChange(t,{value:e})):[ns,is,rs].includes(o)?(l.onChange=e=>this.handleFunctionChange(t,{value:e}),o===ns&&(l.type="play"===this.attributes.state?"pause":"play"),o===is&&(l.type="range"===this.attributes.selectionType?"value":"range"),o===rs&&(l.type="line"===this.attributes.chartType?"column":"line")):l.onClick=()=>this.handleFunctionChange(t,{value:t}),o===ts){const t=this.ownerDocument?.defaultView;t&&(this.speedSelect=new o({style:{...l,zIndex:100}}),t.appendChild(this.speedSelect))}else this.functions.appendChild(new o({style:l}))})}constructor(t){super(m({},as.defaultOptions,t))}disconnectedCallback(){super.disconnectedCallback(),this.speedSelect?.destroy()}render(){this.renderBackground(),this.renderFunctions()}handleFunctionChange(t,e){const{onChange:n}=this.attributes;n?.(t,e)}}class os extends e.Circle{static defaultOptions={style:{r:5,fill:"#3f7cf7",lineWidth:0,stroke:"#3f7cf7",strokeOpacity:.5,cursor:"pointer"}};constructor(t){super(m({},os.defaultOptions,t)),this.bindEvents()}bindEvents(){this.addEventListener("mouseenter",()=>{this.attr("lineWidth",Math.ceil(+(this.style.r||0)/2))}),this.addEventListener("mouseleave",()=>{this.attr("lineWidth",0)})}}class ls extends b{static defaultOptions={style:{x:0,y:0,width:10,height:50,iconSize:10,type:"start",backgroundFill:"#fff",backgroundFillOpacity:.5,iconStroke:"#9a9a9a",iconLineWidth:1,borderStroke:"#e8e8e8",borderLineWidth:1}};renderBackground(){const{x:t,y:e,width:n,height:i}=this.attributes,r=B(this.attributes,"background");T(this).maybeAppend("background","rect").attr("className","background").styles({x:t-n/2,y:e-i/2,width:n,height:i,...r})}renderIcon(){const{x:t,y:e,iconSize:n}=this.attributes,i=B(this.attributes,"icon"),r=n/2;T(this).maybeAppend("icon-left-line","line").attr("className","icon-left-line").styles({x1:t-1,y1:e-r,x2:t-1,y2:e+r,...i}),T(this).maybeAppend("icon-right-line","line").attr("className","icon-right-line").styles({x1:t+1,y1:e-r,x2:t+1,y2:e+r,...i})}renderBorder(){const{x:t,y:e,width:n,height:i,type:r}=this.attributes,s=B(this.attributes,"border"),a="start"===r?+n/2:-n/2;T(this).maybeAppend("border","line").attr("className","border").styles({x1:a+t,y1:e-i/2,x2:a+t,y2:e+i/2,...s})}render(){this.renderBackground(),this.renderIcon(),this.renderBorder()}constructor(t){super(m({},ls.defaultOptions,t))}}function hs(t){const e=String(Math.floor(t/3600)).padStart(2,"0"),n=String(Math.floor(t%3600/60)).padStart(2,"0"),i=String(Math.floor(t%60)).padStart(2,"0");return t<3600?`${n}:${i}`:`${e}:${n}:${i}`}class cs extends b{static defaultOptions={style:{x:0,y:0,axisLabelFill:"#6e6e6e",axisLabelTextAlign:"left",axisLabelTextBaseline:"top",axisLabelTransform:"translate(5, -12)",axisLineLineWidth:1,axisLineStroke:"#cacdd1",axisTickLength:15,axisTickLineWidth:1,axisTickStroke:"#cacdd1",chartShowLabel:!1,chartType:"line",controllerAlign:"center",controllerHeight:40,data:[],interval:"day",loop:!1,playMode:"acc",selectionType:"range",type:"time"}};axis=(()=>this.appendChild(new bi({style:{type:"linear",startPos:[0,0],endPos:[0,0],data:[],showArrow:!1,animate:!1}})))();timeline=(()=>this.appendChild(new Te({style:{onChange:t=>{this.handleSliderChange(t)}}})))();controller=(()=>this.appendChild(new as({})))();states={};playInterval;get data(){const{data:t}=this.attributes;return t.sort((t,e)=>t.time<e.time?-1:t.time>e.time?1:0)}get space(){const{x:t,y:e,width:n,height:i,type:r,controllerHeight:s}=this.attributes,a=Ht(+i-s,0,+i),o=new ci(t,e+ +i-s,+n,s);let l,h=0;"chart"===r?(h=35,l=new ci(t,e+a-h,+n,h)):l=new ci;const c="time"===r?10:a;return{axisBBox:l,controllerBBox:o,timelineBBox:new ci(t,e+("time"===r?a:a-c),+n,c-h)}}setBySliderValues(t){const{data:e}=this,[n,i]=Array.isArray(t)?t:[0,t],r=e.length,s=e[Math.floor(n*r)],a=e[Math.ceil(i*r)-(Array.isArray(t)?0:1)];this.states.values=[s?.time??e[0].time,a?.time??1/0]}setByTimebarValues(t){const{data:e}=this,[n,i]=Array.isArray(t)?t:[void 0,t],r=e.find(t=>{let{time:e}=t;return e===n}),s=e.find(t=>{let{time:e}=t;return e===i});this.states.values=[r?.time??e[0]?.time,s?.time??1/0]}setByIndex(t){const{data:e}=this,[n,i]=t;this.states.values=[e[n]?.time??e[0].time,this.data[i]?.time??1/0]}get sliderValues(){const{values:t,selectionType:e}=this.states,[n,i]=Array.isArray(t)?t:[void 0,t],{data:r}=this,s=r.length,a="value"===e;return[(()=>{const t=r.findIndex(t=>{let{time:e}=t;return e===n});return a?0:t>-1?t/s:0})(),(()=>{if(i===1/0)return 1;const t=r.findIndex(t=>{let{time:e}=t;return e===i});return t>-1?t/s:a?.5:1})()]}get values(){const{values:t,selectionType:e}=this.states,[n,i]=Array.isArray(t)?t:[this.data[0].time,t];return"value"===e?i:[n,i]}getDatumByRatio(t){const{data:e}=this,n=e.length;return e[Math.floor(t*(n-1))]}get chartHandleIconShape(){const{selectionType:t}=this.states,{timelineBBox:{height:n}}=this.space;return"range"===t?t=>new ls({style:{type:t,height:n,iconSize:n/6}}):()=>new e.Line({style:{x1:0,y1:-n/2,x2:0,y2:n/2,lineWidth:2,stroke:"#c8c8c8"}})}getChartStyle(t){const{x:e,y:n,width:i,height:r}=t,{selectionType:s,chartType:a}=this.states,{data:o}=this,{type:l,labelFormatter:h}=this.attributes,{type:c,...u}=B(this.attributes,"chart"),d="range"===s;if("time"===l)return{handleIconShape:()=>new os({}),selectionFill:"#2e7ff8",selectionFillOpacity:1,showLabelOnInteraction:!0,handleLabelDy:d?-15:0,autoFitLabel:d,handleSpacing:d?-15:0,trackFill:"#edeeef",trackLength:i,trackOpacity:.5,trackRadius:r/2,trackSize:r/2,type:s,values:this.sliderValues,formatter:t=>{if(h)return h(t);const e=this.getDatumByRatio(t).time;return"number"==typeof e?hs(e):Vr(e,"YYYY-MM-DD HH:mm:ss")},transform:`translate(${e}, ${n})`,zIndex:1,...u};const p="range"===s?5:0,g=o.map(t=>{let{value:e}=t;return e});return{handleIconOffset:p,handleIconShape:this.chartHandleIconShape,selectionFill:"#fff",selectionFillOpacity:.5,selectionType:"invert",sparklineSpacing:.1,sparklineColumnLineWidth:0,sparklineColor:"#d4e5fd",sparklineAreaOpacity:1,sparklineAreaLineWidth:0,sparklineData:g,sparklineType:a,sparklineScale:.8,trackLength:i,trackSize:r,type:s,values:this.sliderValues,transform:`translate(${e}, ${n})`,zIndex:1,...u}}renderChart(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.space.timelineBBox;this.timeline.update(this.getChartStyle(t))}updateSelection(){this.timeline.setValues(this.sliderValues,!0),this.handleSliderChange(this.sliderValues)}getAxisStyle(t){const{data:e}=this,{interval:n,labelFormatter:i}=this.attributes,r=B(this.attributes,"axis"),{x:s,y:a,width:o}=t,l=[...e,{time:0}].map((t,e,n)=>{let{time:i}=t;return{label:`${i}`,value:e/(n.length-1),time:i}});return{startPos:[s,a],endPos:[s+o,a],data:l,labelFilter:(t,e)=>e<l.length-1,labelFormatter:t=>{let{time:e}=t;return i?i(e):function(t,e){return"number"==typeof t?hs(t):function(t,e){const n=new Date(t);switch(e){case"half-hour":case"hour":case"four-hour":return[0,6,12,18].includes(n.getHours())&&0===n.getMinutes()?Vr(n,"HH:mm\nYYYY-MM-DD"):Vr(n,"HH:mm");case"half-day":return n.getHours()<12?`AM\n${Vr(n,"YYYY-MM-DD")}`:"PM";case"day":return[1,10,20].includes(n.getDate())?Vr(n,"DD\nYYYY-MM"):Vr(n,"DD");case"week":return n.getDate()<=7?Vr(n,"DD\nYYYY-MM"):Vr(n,"DD");case"month":return[0,6].includes(n.getMonth())?Vr(n,"MM月\nYYYY"):Vr(n,"MM月");case"season":return[0].includes(n.getMonth())?Vr(n,"MM月\nYYYY"):Vr(n,"MM月");case"year":return Vr(n,"YYYY");default:return Vr(n,"YYYY-MM-DD HH:mm")}}(t,e)}(e,n)},...r}}renderAxis(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.space.axisBBox;const{type:e}=this.attributes;"chart"===e&&this.axis.update(this.getAxisStyle(t))}renderController(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.space.controllerBBox;const{type:e}=this.attributes,{state:n,speed:i,selectionType:r,chartType:s}=this.states,a=B(this.attributes,"controller"),o=this,l={...t,iconSize:20,speed:i,state:n,selectionType:r,chartType:s,onChange(t,e){let{value:n}=e;switch(t){case"reset":o.internalReset();break;case"speed":o.handleSpeedChange(n);break;case"backward":o.internalBackward();break;case"playPause":"play"===n?o.internalPlay():o.internalPause();break;case"forward":o.internalForward();break;case"selectionType":o.handleSelectionTypeChange(n);break;case"chartType":o.handleChartTypeChange(n)}},...a};"time"===e&&(l.functions=[["reset","speed"],["backward","playPause","forward"],["selectionType"]]),this.controller.update(l)}dispatchOnChange(t){const{data:e}=this,{onChange:n}=this.attributes,{values:i,selectionType:r}=this.states,[s,a]=i,o=a===1/0?e.at(-1).time:a;t&&((t,e)=>{if(Array.isArray(t)){if(!Array.isArray(e))return!1;if(t[0]===e[0]){if(t[1]===e[1])return!0;if(t[1]===1/0||e[1]===1/0)return!0}return!1}return!Array.isArray(e)&&t===e})(t,"range"===r?[s,o]:o)||n?.("range"===r?[s,o]:o)}handleSliderChange=t=>{const e=(()=>{const t=this.states.values;return Array.isArray(t)?[...t]:t})();this.setBySliderValues(t),this.dispatchOnChange(e)};internalReset(t){const{selectionType:e}=this.states;this.internalPause(),this.setBySliderValues("range"===e?[0,1]:[0,0]),this.renderController(),this.updateSelection(),t||(this.attributes?.onReset?.(),this.dispatchOnChange())}reset(){this.internalReset()}moveSelection(t,e){const{data:n}=this,i=n.length,{values:r,selectionType:s,playMode:a}=this.states,[o,l]=r,h=n.findIndex(t=>{let{time:e}=t;return e===o});let c=n.findIndex(t=>{let{time:e}=t;return e===l});-1===c&&(c=i);const u="backward"===t?-1:1;let d;"range"===s?"acc"===a?(d=[h,c+u],-1===u&&h===c&&(d=[h,i])):d=[h+u,c+u]:d=[h,c+u];const p=(t=>{const[e,n]=t.sort((t,e)=>t-e),r=t=>Ht(t,0,i);return n>i?"value"===s?[0,0]:"acc"===a?[r(e),r(e)]:[0,r(n-e)]:e<0?"acc"===a?[0,r(n)]:[r(e+i-n),i]:[r(e),r(n)]})(d);return this.setByIndex(p),this.updateSelection(),p}internalBackward(t){const e=this.moveSelection("backward",t);return t||(this.attributes?.onBackward?.(),this.dispatchOnChange()),e}backward(){this.internalBackward()}internalPlay(t){const{data:e}=this,{loop:n}=this.attributes,{speed:i=1}=this.states;this.playInterval=window.setInterval(()=>{this.internalForward()[1]!==e.length||n||(this.internalPause(),this.renderController())},1e3/i),this.states.state="play",!t&&this.attributes?.onPlay?.()}play(){this.internalPlay()}internalPause(t){clearInterval(this.playInterval),this.states.state="pause",!t&&this.attributes?.onPause?.()}pause(){this.internalPause()}internalForward(t){const e=this.moveSelection("forward",t);return t||(this.attributes?.onForward?.(),this.dispatchOnChange()),e}forward(){this.internalForward()}handleSpeedChange(t){this.states.speed=t;const{state:e}=this.states;"play"===e&&(this.internalPause(!0),this.internalPlay(!0)),this.attributes?.onSpeedChange?.(t)}handleSelectionTypeChange(t){this.states.selectionType=t,this.renderChart(),this.attributes?.onSelectionTypeChange?.(t)}handleChartTypeChange(t){this.states.chartType=t,this.renderChart(),this.attributes?.onChartTypeChange?.(t)}constructor(t){super(m({},cs.defaultOptions,t));const{selectionType:e,chartType:n,speed:i,state:r,playMode:s,values:a}=this.attributes;this.states={chartType:n,playMode:s,selectionType:e,speed:i,state:r},this.setByTimebarValues(a)}render(){const{axisBBox:t,controllerBBox:e,timelineBBox:n}=this.space;this.renderController(e),this.renderAxis(t),this.renderChart(n),"play"===this.states.state&&this.internalPlay()}destroy(){super.destroy(),this.internalPause(!0)}}function us(t){const e=document.createElement("div");return e.innerHTML=t,e.firstChild}function ds(t){return"string"==typeof t?us(t):t}var ps=i(345),gs=i.n(ps);function fs(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1024,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1014;return(i,r,s)=>gs()(t).map(t=>{const a=[];return a.push(!0===t.relative?t.code.toLowerCase():t.code.toUpperCase()),[[t.x1,t.y1],[t.x2,t.y2],[t.x,t.y]].forEach(o=>{const[l,h]=o;void 0!==l&&a.push(!0===t.relative?l/e*2*s:i-s+2*s*(l/e)),void 0!==h&&a.push(!0===t.relative?h/n*2*s:r-s+2*s*(h/n))}),a})}function ms(t){const e=/<path\s+d="(.*?)"/i.exec(t),n=/viewBox="\d+\s+\d+\s+(\d+)\s+(\d+)"/i.exec(t);if(null===e||e.length<2)return()=>[];let i=1024,r=1024;return null!==n&&n.length>=3&&(Number.isNaN(parseInt(n[1],10))||(i=parseInt(n[1],10)),Number.isNaN(parseInt(n[2],10))||(r=parseInt(n[2],10))),fs(e[1],i,r)}function ys(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(n,i,r)=>{const s=r.value;let a;"function"==typeof s&&(r.value=function(){for(var n=arguments.length,i=new Array(n),r=0;r<n;r++)i[r]=arguments[r];if(a)return;const o=this;e&&s.apply(o,i),a=window.setTimeout(()=>{s.apply(o,i),a=null},t)})}}function bs(t){const e=localStorage.getItem("__debug__");return(n,i,r)=>{const s=`[${i}] ${t}`,a=r.value;"function"==typeof a&&(r.value=function(){e&&console.time(s);for(var t=arguments.length,n=new Array(t),i=0;i<t;i++)n[i]=arguments[i];a.apply(this,n),e&&console.timeEnd(s)})}}})(),r})());