{"name": "@ant-design/plots", "version": "2.6.3", "description": "G2Plot Statistical chart", "bugs": {"url": "https://github.com/ant-design/ant-design-charts/issues"}, "repository": {"type": "git", "url": "git+https://github.com/ant-design/ant-design-charts.git"}, "main": "lib/index.js", "unpkg": "dist/plots.min.js", "module": "es/index.js", "types": "es/index.d.ts", "files": ["/lib", "/es", "/dist"], "dependencies": {"@antv/event-emitter": "^0.1.3", "@antv/g": "^6.1.7", "@antv/g2": "^5.2.7", "@antv/g2-extension-plot": "^0.2.1", "lodash": "^4.17.21", "@ant-design/charts-util": "0.0.2"}, "peerDependencies": {"react": ">=16.8.4", "react-dom": ">=16.8.4"}, "sideEffects": false, "license": "MIT", "devDependencies": {"@testing-library/react": "^16.3.0", "@types/jest": "^26.0.24", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "react-test-renderer": "^18.3.1"}, "scripts": {"start": "pnpm lib:es --w", "build": "run-s clean lib dist", "build:lib": "run-s clean lib", "ci": "pnpm run build && pnpm run test:coverage", "clean": "rimraf lib es dist", "dist": "webpack --config webpack.config.js --mode production", "profile": "webpack --config webpack.config.js --mode production --profile --json > stats.json", "lib": "run-p lib:*", "lib:cjs": "tsc -p tsconfig.prod.json --target ES5 --module commonjs --outDir lib", "lib:es": "tsc -p tsconfig.prod.json --target ES5 --module ESNext --outDir es", "lint": "eslint --cache --ext .js,.jsx,.ts,.tsx  --fix --format=pretty ./src && pnpm run lint:prettier", "lint:prettier": "pnpm run prettier &&  git diff && prettier --version && prettier --check \"src/**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "prettier": "prettier --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "test": "jest", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s"}}