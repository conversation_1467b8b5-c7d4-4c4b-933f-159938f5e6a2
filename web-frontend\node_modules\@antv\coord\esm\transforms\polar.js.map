{"version": 3, "file": "polar.js", "sourceRoot": "src/", "sources": ["transforms/polar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,sDAAsD;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,OAAO,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAEvC;;;;;;;;;GASG;AACH,MAAM,CAAC,IAAM,KAAK,GAAsB,UAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;IAC5D,IAAA,KAAA,OAAmD,MAAkB,IAAA,EAApE,UAAU,QAAA,EAAE,QAAQ,QAAA,EAAE,WAAW,QAAA,EAAE,WAAW,QAAsB,CAAC;IAC5E,IAAM,MAAM,GAAG,IAAI,MAAM,CAAC;QACxB,KAAK,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;KAClC,CAAC,CAAC;IACH,IAAM,KAAK,GAAG,IAAI,MAAM,CAAC;QACvB,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;KAC9B,CAAC,CAAC;IACH,IAAM,MAAM,GAAG,MAAM,GAAG,KAAK,CAAC;IAC9B,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;IACnC,IAAM,EAAE,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,OAAO;QACL,SAAS,YAAC,MAAe;YACjB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC5B,IAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAEzB,uBAAuB;YACvB,IAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YACnC,IAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAEnC,8BAA8B;YAC9B,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACzB,IAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACzB,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;QACD,WAAW,YAAC,MAAe;YACnB,IAAA,KAAA,OAAW,MAAM,IAAA,EAAhB,EAAE,QAAA,EAAE,EAAE,QAAU,CAAC;YACxB,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAChC,IAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YAChC,IAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAA,CAAC,EAAI,CAAC,CAAA,GAAG,SAAA,CAAC,EAAI,CAAC,CAAA,CAAC,CAAC;YACrC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACnD,IAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC/B,IAAM,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC5B,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;QAClB,CAAC;KACF,CAAC;AACJ,CAAC,CAAC"}