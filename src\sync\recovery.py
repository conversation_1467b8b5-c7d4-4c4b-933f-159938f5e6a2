#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
福彩3D数据同步系统 - 恢复管理器

负责系统故障恢复、数据修复和备份管理

作者: Augment Code AI Assistant
创建时间: 2025-08-12
"""

import os
import shutil
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

class RecoveryManager:
    """恢复管理器"""
    
    def __init__(self, config: Dict[str, Any], sync_manager):
        self.config = config
        self.sync_manager = sync_manager
        self.recovery_config = config.get('recovery', {})
        
        # 恢复配置
        self.auto_recovery = self.recovery_config.get('auto_recovery', True)
        self.backup_config = self.recovery_config.get('backup', {})
        self.repair_config = self.recovery_config.get('repair', {})
        
        # 备份配置
        self.backup_enabled = self.backup_config.get('enabled', True)
        self.backup_path = self.backup_config.get('path', 'backups/sync')
        self.backup_keep_days = self.backup_config.get('keep_days', 30)
        
        # 修复配置
        self.auto_fix_inconsistency = self.repair_config.get('auto_fix_inconsistency', True)
        self.auto_fill_missing = self.repair_config.get('auto_fill_missing', True)
        
        logger.info("恢复管理器初始化完成")
    
    def create_backup(self, backup_type: str = "manual") -> bool:
        """创建数据库备份"""
        if not self.backup_enabled:
            logger.info("备份功能已禁用")
            return True
        
        try:
            # 确保备份目录存在
            os.makedirs(self.backup_path, exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 备份主数据库
            master_db = self.config.get('databases', {}).get('master', {}).get('path', 'data/lottery.db')
            if os.path.exists(master_db):
                backup_file = os.path.join(self.backup_path, f"master_{backup_type}_{timestamp}.db")
                shutil.copy2(master_db, backup_file)
                logger.info(f"主数据库备份完成: {backup_file}")
            
            # 备份从数据库
            slaves = self.config.get('databases', {}).get('slaves', [])
            for slave in slaves:
                slave_path = slave.get('path')
                if slave_path and os.path.exists(slave_path):
                    slave_name = os.path.basename(slave_path).replace('.db', '')
                    backup_file = os.path.join(self.backup_path, f"{slave_name}_{backup_type}_{timestamp}.db")
                    shutil.copy2(slave_path, backup_file)
                    logger.info(f"从数据库备份完成: {backup_file}")
            
            # 清理过期备份
            self._cleanup_old_backups()
            
            return True
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False
    
    def restore_from_backup(self, backup_file: str, target_db: str) -> bool:
        """从备份恢复数据库"""
        try:
            if not os.path.exists(backup_file):
                logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            # 创建当前数据库的备份
            if os.path.exists(target_db):
                backup_current = f"{target_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(target_db, backup_current)
                logger.info(f"当前数据库已备份: {backup_current}")
            
            # 恢复数据库
            shutil.copy2(backup_file, target_db)
            logger.info(f"数据库恢复完成: {target_db}")
            
            return True
            
        except Exception as e:
            logger.error(f"恢复数据库失败: {e}")
            return False
    
    def check_data_consistency(self) -> Dict[str, Any]:
        """检查数据一致性"""
        consistency_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'consistent',
            'issues': [],
            'recommendations': []
        }
        
        try:
            # 获取数据库配置
            master_path = self.config.get('databases', {}).get('master', {}).get('path', 'data/lottery.db')
            slaves = self.config.get('databases', {}).get('slaves', [])
            
            if not os.path.exists(master_path):
                consistency_report['issues'].append("主数据库不存在")
                consistency_report['overall_status'] = 'critical'
                return consistency_report
            
            # 获取主数据库的最新数据
            master_data = self._get_latest_records(master_path, 10)
            
            # 检查每个从数据库
            for slave in slaves:
                slave_path = slave.get('path')
                if not slave_path:
                    continue
                
                if not os.path.exists(slave_path):
                    consistency_report['issues'].append(f"从数据库不存在: {slave_path}")
                    consistency_report['recommendations'].append(f"重新创建从数据库: {slave_path}")
                    consistency_report['overall_status'] = 'inconsistent'
                    continue
                
                # 比较数据
                slave_data = self._get_latest_records(slave_path, 10)
                
                if len(master_data) != len(slave_data):
                    consistency_report['issues'].append(f"数据数量不一致: {slave_path}")
                    consistency_report['recommendations'].append(f"同步数据到: {slave_path}")
                    consistency_report['overall_status'] = 'inconsistent'
                
                # 检查最新记录是否一致
                if master_data and slave_data:
                    master_latest = master_data[0]
                    slave_latest = slave_data[0]
                    
                    if master_latest['issue'] != slave_latest['issue']:
                        consistency_report['issues'].append(f"最新期号不一致: {slave_path}")
                        consistency_report['recommendations'].append(f"同步最新数据到: {slave_path}")
                        consistency_report['overall_status'] = 'inconsistent'
            
            logger.info(f"数据一致性检查完成: {consistency_report['overall_status']}")
            return consistency_report
            
        except Exception as e:
            logger.error(f"数据一致性检查失败: {e}")
            consistency_report['overall_status'] = 'error'
            consistency_report['issues'].append(f"检查失败: {str(e)}")
            return consistency_report
    
    def auto_repair(self) -> Dict[str, Any]:
        """自动修复数据问题"""
        repair_result = {
            'timestamp': datetime.now().isoformat(),
            'success': False,
            'actions': [],
            'errors': []
        }
        
        try:
            # 检查数据一致性
            consistency_report = self.check_data_consistency()
            
            if consistency_report['overall_status'] == 'consistent':
                repair_result['success'] = True
                repair_result['actions'].append("数据一致性正常，无需修复")
                return repair_result
            
            # 创建修复前备份
            if self.backup_enabled:
                backup_success = self.create_backup("pre_repair")
                if backup_success:
                    repair_result['actions'].append("创建修复前备份")
                else:
                    repair_result['errors'].append("创建备份失败")
            
            # 执行修复操作
            if self.auto_fix_inconsistency:
                # 强制同步所有数据库
                sync_result = self.sync_manager.execute_sync(force=True)
                if sync_result['success']:
                    repair_result['actions'].append("强制同步所有数据库")
                else:
                    repair_result['errors'].append("强制同步失败")
            
            # 再次检查一致性
            final_check = self.check_data_consistency()
            if final_check['overall_status'] == 'consistent':
                repair_result['success'] = True
                repair_result['actions'].append("数据一致性修复成功")
            else:
                repair_result['errors'].append("修复后仍存在一致性问题")
            
            logger.info(f"自动修复完成: {'成功' if repair_result['success'] else '失败'}")
            return repair_result
            
        except Exception as e:
            logger.error(f"自动修复失败: {e}")
            repair_result['errors'].append(str(e))
            return repair_result
    
    def _get_latest_records(self, db_path: str, limit: int = 10) -> List[Dict[str, Any]]:
        """获取数据库最新记录"""
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT issue, draw_date, hundreds, tens, units
                FROM lottery_data
                ORDER BY id DESC
                LIMIT ?
            """, (limit,))
            
            records = []
            for row in cursor.fetchall():
                records.append({
                    'issue': row[0],
                    'draw_date': row[1],
                    'hundreds': row[2],
                    'tens': row[3],
                    'units': row[4]
                })
            
            conn.close()
            return records
            
        except Exception as e:
            logger.error(f"获取数据库记录失败 {db_path}: {e}")
            return []
    
    def _cleanup_old_backups(self):
        """清理过期备份"""
        try:
            if not os.path.exists(self.backup_path):
                return
            
            cutoff_date = datetime.now() - timedelta(days=self.backup_keep_days)
            
            for filename in os.listdir(self.backup_path):
                if filename.endswith('.db'):
                    file_path = os.path.join(self.backup_path, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        logger.info(f"删除过期备份: {filename}")
            
        except Exception as e:
            logger.error(f"清理过期备份失败: {e}")
    
    def get_recovery_status(self) -> Dict[str, Any]:
        """获取恢复系统状态"""
        return {
            'auto_recovery_enabled': self.auto_recovery,
            'backup_enabled': self.backup_enabled,
            'backup_path': self.backup_path,
            'backup_keep_days': self.backup_keep_days,
            'auto_fix_inconsistency': self.auto_fix_inconsistency,
            'auto_fill_missing': self.auto_fill_missing
        }
