"""
主同步管理器
统一管理福彩3D数据的自动同步流程
"""

import logging
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import threading
import time

from .utils import load_config, setup_logging
from .data_source import DataSourceManager
from .database_manager import DatabaseManager

logger = logging.getLogger(__name__)

class SyncManager:
    """主同步管理器"""
    
    def __init__(self, config_path: str = "src/sync/config.yaml"):
        """初始化同步管理器"""
        self.config = load_config(config_path)
        self.logger = setup_logging(self.config)
        
        # 初始化组件
        self.data_source = DataSourceManager(self.config)
        self.database_manager = DatabaseManager(self.config)
        
        # 同步配置
        self.sync_config = self.config.get('sync', {})
        self.auto_generate_predictions = self.sync_config.get('strategy', {}).get('auto_generate_predictions', True)
        
        # 状态跟踪
        self.last_sync_time = None
        self.sync_stats = {
            'total_syncs': 0,
            'successful_syncs': 0,
            'failed_syncs': 0,
            'last_error': None
        }
        
        # 线程锁
        self._sync_lock = threading.Lock()
        
        logger.info("同步管理器初始化完成")
    
    def execute_sync(self, force: bool = False) -> Dict[str, Any]:
        """执行数据同步"""
        with self._sync_lock:
            return self._do_sync(force)
    
    def _do_sync(self, force: bool = False) -> Dict[str, Any]:
        """执行同步的内部方法"""
        sync_result = {
            'success': False,
            'timestamp': datetime.now().isoformat(),
            'steps': [],
            'errors': [],
            'stats': {}
        }
        
        try:
            logger.info("🚀 开始执行数据同步")
            self.sync_stats['total_syncs'] += 1
            
            # 步骤1: 获取最新数据
            step1_result = self._step1_fetch_data()
            sync_result['steps'].append(step1_result)
            
            if not step1_result['success']:
                sync_result['errors'].append("获取数据失败")
                return sync_result
            
            latest_data = step1_result['data']
            
            # 步骤2: 检查是否需要更新
            if not force:
                step2_result = self._step2_check_update_needed(latest_data)
                sync_result['steps'].append(step2_result)
                
                if not step2_result['update_needed']:
                    logger.info("数据已是最新，无需更新")
                    sync_result['success'] = True
                    return sync_result
            
            # 步骤3: 更新主数据库
            step3_result = self._step3_update_master_db(latest_data)
            sync_result['steps'].append(step3_result)
            
            if not step3_result['success']:
                sync_result['errors'].append("更新主数据库失败")
                return sync_result
            
            # 步骤4: 同步到从数据库
            step4_result = self._step4_sync_slave_dbs()
            sync_result['steps'].append(step4_result)
            
            # 步骤5: 生成预测数据（如果启用）
            if self.auto_generate_predictions:
                step5_result = self._step5_generate_predictions()
                sync_result['steps'].append(step5_result)
            
            # 步骤6: 验证同步结果
            step6_result = self._step6_verify_sync()
            sync_result['steps'].append(step6_result)
            
            # 更新统计信息
            sync_result['success'] = True
            self.sync_stats['successful_syncs'] += 1
            self.last_sync_time = datetime.now()
            
            logger.info("✅ 数据同步完成")
            
        except Exception as e:
            logger.error(f"❌ 数据同步失败: {e}")
            sync_result['errors'].append(str(e))
            self.sync_stats['failed_syncs'] += 1
            self.sync_stats['last_error'] = str(e)
        
        return sync_result
    
    def _step1_fetch_data(self) -> Dict[str, Any]:
        """步骤1: 获取最新数据"""
        logger.info("📥 步骤1: 获取最新数据")
        
        try:
            fetch_count = self.sync_config.get('strategy', {}).get('fetch_latest_count', 10)
            latest_data = self.data_source.get_latest_data(fetch_count)
            
            if not latest_data:
                return {
                    'step': 'fetch_data',
                    'success': False,
                    'error': '无法获取数据源数据',
                    'data': []
                }
            
            # 验证数据一致性
            if not self.data_source.validate_data_consistency(latest_data):
                return {
                    'step': 'fetch_data',
                    'success': False,
                    'error': '数据一致性验证失败',
                    'data': []
                }
            
            logger.info(f"成功获取 {len(latest_data)} 条数据")
            return {
                'step': 'fetch_data',
                'success': True,
                'data': latest_data,
                'count': len(latest_data)
            }
            
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return {
                'step': 'fetch_data',
                'success': False,
                'error': str(e),
                'data': []
            }
    
    def _step2_check_update_needed(self, latest_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤2: 检查是否需要更新"""
        logger.info("🔍 步骤2: 检查是否需要更新")
        
        try:
            if not latest_data:
                return {
                    'step': 'check_update',
                    'success': True,
                    'update_needed': False,
                    'reason': '没有新数据'
                }
            
            # 获取数据源最新期号
            source_latest = latest_data[-1]['issue']
            
            # 获取主数据库最新期号
            db_latest = self.database_manager.get_master_latest_issue()
            
            logger.info(f"数据源最新期号: {source_latest}")
            logger.info(f"数据库最新期号: {db_latest}")
            
            if db_latest and source_latest <= db_latest:
                return {
                    'step': 'check_update',
                    'success': True,
                    'update_needed': False,
                    'reason': f'数据库已是最新 (期号: {db_latest})',
                    'source_latest': source_latest,
                    'db_latest': db_latest
                }
            
            return {
                'step': 'check_update',
                'success': True,
                'update_needed': True,
                'reason': f'发现新数据: {source_latest}',
                'source_latest': source_latest,
                'db_latest': db_latest
            }
            
        except Exception as e:
            logger.error(f"检查更新需求失败: {e}")
            return {
                'step': 'check_update',
                'success': False,
                'error': str(e),
                'update_needed': True  # 出错时默认需要更新
            }
    
    def _step3_update_master_db(self, latest_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """步骤3: 更新主数据库"""
        logger.info("💾 步骤3: 更新主数据库")

        try:
            updated_count = self.database_manager.update_master_database(latest_data)

            return {
                'step': 'update_master_db',
                'success': True,
                'updated_count': updated_count,
                'message': f'主数据库更新完成，新增 {updated_count} 条记录'
            }

        except Exception as e:
            logger.error(f"更新主数据库失败: {e}")
            return {
                'step': 'update_master_db',
                'success': False,
                'error': str(e)
            }

    def _step4_sync_slave_dbs(self) -> Dict[str, Any]:
        """步骤4: 同步到从数据库"""
        logger.info("🔄 步骤4: 同步到从数据库")

        try:
            sync_results = self.database_manager.sync_to_slave_databases()

            success_count = sum(1 for success in sync_results.values() if success)
            total_count = len(sync_results)

            return {
                'step': 'sync_slave_dbs',
                'success': success_count == total_count,
                'sync_results': sync_results,
                'success_count': success_count,
                'total_count': total_count,
                'message': f'从数据库同步完成: {success_count}/{total_count} 成功'
            }

        except Exception as e:
            logger.error(f"同步从数据库失败: {e}")
            return {
                'step': 'sync_slave_dbs',
                'success': False,
                'error': str(e)
            }

    def _step5_generate_predictions(self) -> Dict[str, Any]:
        """步骤5: 生成预测数据"""
        logger.info("🎯 步骤5: 生成预测数据")

        try:
            # 这里调用智能推荐引擎生成新预测
            # 为了避免循环导入，使用动态导入
            import sys
            sys.path.append('src')

            try:
                from fusion.smart_recommendation_engine import SmartRecommendationEngine

                engine = SmartRecommendationEngine()
                next_issue_info = engine._get_next_issue_info()

                if next_issue_info:
                    # 生成推荐并转换为预测格式
                    recommendations = engine.generate_daily_recommendations()

                    if recommendations.get('status') == 'success':
                        # 这里可以调用预测保存逻辑
                        logger.info(f"成功生成 {next_issue_info.get('issue')} 期预测")
                        return {
                            'step': 'generate_predictions',
                            'success': True,
                            'issue': next_issue_info.get('issue'),
                            'message': '预测数据生成完成'
                        }
                    else:
                        return {
                            'step': 'generate_predictions',
                            'success': False,
                            'error': '智能推荐生成失败'
                        }
                else:
                    return {
                        'step': 'generate_predictions',
                        'success': False,
                        'error': '无法获取下一期信息'
                    }

            except ImportError as e:
                logger.warning(f"无法导入智能推荐引擎: {e}")
                return {
                    'step': 'generate_predictions',
                    'success': False,
                    'error': '智能推荐引擎不可用'
                }

        except Exception as e:
            logger.error(f"生成预测数据失败: {e}")
            return {
                'step': 'generate_predictions',
                'success': False,
                'error': str(e)
            }

    def _step6_verify_sync(self) -> Dict[str, Any]:
        """步骤6: 验证同步结果"""
        logger.info("✅ 步骤6: 验证同步结果")

        try:
            consistency_report = self.database_manager.check_database_consistency()

            return {
                'step': 'verify_sync',
                'success': consistency_report['consistent'],
                'consistency_report': consistency_report,
                'message': '同步验证完成' if consistency_report['consistent'] else '发现数据不一致'
            }

        except Exception as e:
            logger.error(f"验证同步结果失败: {e}")
            return {
                'step': 'verify_sync',
                'success': False,
                'error': str(e)
            }

    def get_sync_status(self) -> Dict[str, Any]:
        """获取同步状态"""
        return {
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'stats': self.sync_stats.copy(),
            'database_status': self.database_manager.check_database_consistency(),
            'data_source_status': self.data_source.get_data_source_status()
        }
