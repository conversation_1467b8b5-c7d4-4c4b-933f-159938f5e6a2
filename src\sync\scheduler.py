"""
定时任务管理器
负责管理数据同步的定时任务
"""

import logging
from datetime import datetime, time
from typing import Dict, Any, Callable, Optional
import threading
import schedule
import time as time_module

from .utils import get_next_draw_time

logger = logging.getLogger(__name__)

class ScheduleManager:
    """定时任务管理器"""
    
    def __init__(self, config: Dict[str, Any], sync_callback: Callable):
        self.config = config.get('sync', {}).get('schedule', {})
        self.sync_callback = sync_callback
        
        # 配置参数
        self.enabled = self.config.get('enabled', True)
        self.daily_time = self.config.get('daily_time', '21:30')
        self.startup_check = self.config.get('startup_check', True)
        self.health_check_interval = self.config.get('health_check_interval', 6)  # 小时
        
        # 运行状态
        self.is_running = False
        self.scheduler_thread = None
        self._stop_event = threading.Event()
        
        logger.info(f"定时任务管理器初始化完成")
        logger.info(f"启用状态: {self.enabled}")
        logger.info(f"每日同步时间: {self.daily_time}")
        logger.info(f"健康检查间隔: {self.health_check_interval}小时")
    
    def start(self):
        """启动定时任务"""
        if not self.enabled:
            logger.info("定时任务已禁用，跳过启动")
            return
        
        if self.is_running:
            logger.warning("定时任务已在运行")
            return
        
        logger.info("🕐 启动定时任务管理器")
        
        # 清除之前的任务
        schedule.clear()
        
        # 设置定时任务
        self._setup_scheduled_tasks()
        
        # 启动时检查
        if self.startup_check:
            logger.info("执行启动时数据检查")
            threading.Thread(target=self._startup_sync, daemon=True).start()
        
        # 启动调度器线程
        self.is_running = True
        self._stop_event.clear()
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("定时任务管理器启动完成")
    
    def stop(self):
        """停止定时任务"""
        if not self.is_running:
            return
        
        logger.info("🛑 停止定时任务管理器")
        
        self.is_running = False
        self._stop_event.set()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        schedule.clear()
        logger.info("定时任务管理器已停止")
    
    def _setup_scheduled_tasks(self):
        """设置定时任务"""
        # 每日同步任务
        schedule.every().day.at(self.daily_time).do(self._daily_sync_job)
        logger.info(f"已设置每日同步任务: {self.daily_time}")
        
        # 健康检查任务
        schedule.every(self.health_check_interval).hours.do(self._health_check_job)
        logger.info(f"已设置健康检查任务: 每{self.health_check_interval}小时")
        
        # 每小时检查一次是否有遗漏的开奖数据
        schedule.every().hour.at(":30").do(self._hourly_check_job)
        logger.info("已设置每小时检查任务")
    
    def _run_scheduler(self):
        """运行调度器"""
        logger.info("调度器线程启动")
        
        while self.is_running and not self._stop_event.is_set():
            try:
                schedule.run_pending()
                time_module.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time_module.sleep(60)
        
        logger.info("调度器线程结束")
    
    def _startup_sync(self):
        """启动时同步"""
        try:
            logger.info("🚀 执行启动时数据同步检查")
            result = self.sync_callback(force=False)
            
            if result['success']:
                logger.info("✅ 启动时数据检查完成")
            else:
                logger.warning(f"⚠️ 启动时数据检查发现问题: {result.get('errors', [])}")
                
        except Exception as e:
            logger.error(f"启动时数据检查失败: {e}")
    
    def _daily_sync_job(self):
        """每日同步任务"""
        try:
            logger.info("🕘 执行每日定时同步")
            result = self.sync_callback(force=False)
            
            if result['success']:
                logger.info("✅ 每日定时同步完成")
            else:
                logger.error(f"❌ 每日定时同步失败: {result.get('errors', [])}")
                
        except Exception as e:
            logger.error(f"每日定时同步异常: {e}")
    
    def _health_check_job(self):
        """健康检查任务"""
        try:
            logger.info("🏥 执行健康检查")
            
            # 这里可以添加更多健康检查逻辑
            # 比如检查数据新鲜度、数据库一致性等
            
            # 简单的数据新鲜度检查
            result = self.sync_callback(force=False)
            
            if not result['success']:
                logger.warning(f"健康检查发现问题: {result.get('errors', [])}")
            else:
                logger.info("健康检查通过")
                
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
    
    def _hourly_check_job(self):
        """每小时检查任务"""
        try:
            logger.debug("🔍 执行每小时数据检查")
            
            # 轻量级检查，不强制同步
            # 主要检查是否有新的开奖数据
            current_hour = datetime.now().hour
            
            # 在开奖时间附近（21-23点）更频繁检查
            if 21 <= current_hour <= 23:
                logger.info("开奖时间段，执行数据检查")
                result = self.sync_callback(force=False)
                
                if result['success'] and any(step.get('updated_count', 0) > 0 for step in result.get('steps', [])):
                    logger.info("🎉 检测到新开奖数据并已同步")
            
        except Exception as e:
            logger.error(f"每小时检查异常: {e}")
    
    def trigger_manual_sync(self, force: bool = False) -> Dict[str, Any]:
        """手动触发同步"""
        logger.info(f"🔧 手动触发同步 (force={force})")
        
        try:
            result = self.sync_callback(force=force)
            
            if result['success']:
                logger.info("✅ 手动同步完成")
            else:
                logger.error(f"❌ 手动同步失败: {result.get('errors', [])}")
            
            return result
            
        except Exception as e:
            logger.error(f"手动同步异常: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_schedule_status(self) -> Dict[str, Any]:
        """获取调度状态"""
        jobs_info = []
        
        for job in schedule.jobs:
            jobs_info.append({
                'job': str(job.job_func),
                'next_run': job.next_run.isoformat() if job.next_run else None,
                'interval': str(job.interval),
                'unit': job.unit
            })
        
        return {
            'enabled': self.enabled,
            'is_running': self.is_running,
            'daily_time': self.daily_time,
            'health_check_interval': self.health_check_interval,
            'jobs_count': len(schedule.jobs),
            'jobs': jobs_info,
            'next_draw_time': get_next_draw_time().isoformat()
        }
    
    def reschedule_daily_sync(self, new_time: str):
        """重新安排每日同步时间"""
        try:
            # 验证时间格式
            datetime.strptime(new_time, '%H:%M')
            
            # 清除旧的每日任务
            schedule.clear('daily_sync')
            
            # 设置新的每日任务
            schedule.every().day.at(new_time).tag('daily_sync').do(self._daily_sync_job)
            
            self.daily_time = new_time
            logger.info(f"每日同步时间已更新为: {new_time}")
            
            return True
            
        except ValueError:
            logger.error(f"无效的时间格式: {new_time}")
            return False
        except Exception as e:
            logger.error(f"重新安排每日同步失败: {e}")
            return False
