!function(t,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports):"function"==typeof define&&define.amd?define(["exports"],r):r((t="undefined"!=typeof globalThis?globalThis:t||self).Coordinate={})}(this,function(t){"use strict";function x(t){var r="function"==typeof Symbol&&Symbol.iterator,n=r&&t[r],e=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return{value:(t=t&&e>=t.length?void 0:t)&&t[e++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")}function k(t,r){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var e,o,a=n.call(t),i=[];try{for(;(void 0===r||0<r--)&&!(e=a.next()).done;)i.push(e.value)}catch(t){o={error:t}}finally{try{e&&!e.done&&(n=a.return)&&n.call(a)}finally{if(o)throw o.error}}return i}function O(t,r,n){if(n||2===arguments.length)for(var e,o=0,a=r.length;o<a;o++)!e&&o in r||((e=e||Array.prototype.slice.call(r,0,o))[o]=r[o]);return t.concat(e||Array.prototype.slice.call(r))}function q(t){return i(t,"Function")}var C={}.toString,i=function(t,r){return C.call(t)==="[object "+r+"]"},W=function(t){return Array.isArray?Array.isArray(t):i(t,"Array")};var B=function(t){if("object"!=typeof(r=t)||null===r||!i(t,"Object"))return!1;var r;if(null===Object.getPrototypeOf(t))return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(t)===n},$=function(t){return i(t,"Number")},z=Object.values?function(t){return Object.values(t)}:function(n){var t,e=[],r=n,o=function(t,r){q(n)&&"prototype"===r||e.push(t)};if(r)if(W(r))for(var a=0,i=r.length;a<i&&!1!==o(r[a],a);a++);else if(t=typeof r,null!==r&&"object"==t||"function"==t)for(var u in r)if(r.hasOwnProperty(u)&&!1===o(r[u],u))break;return e},E=5;function r(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];for(var e=0;e<r.length;e+=1)!function t(r,n,e,o){for(var a in e=e||0,o=o||E,n){var i;n.hasOwnProperty(a)&&(null!==(i=n[a])&&B(i)?(B(r[a])||(r[a]={}),e<o?t(r[a],i,e+1,o):r[a]=n[a]):W(i)?(r[a]=[],r[a]=r[a].concat(i)):void 0!==i&&(r[a]=i))}}(t,r[e]);return t}function A(t){return t}!function(a,i){var u;if(q(a))return(u=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n,e=i?i.apply(this,t):t[0],o=u.cache;return o.has(e)?o.get(e):(n=a.apply(this,t),o.set(e,n),n)}).cache=new Map;throw new TypeError("Expected a function")}(function(t,r){var n=(r=void 0===r?{}:r).fontSize,e=r.fontFamily,o=r.fontWeight,a=r.fontStyle,r=r.fontVariant;return(u=u||document.createElement("canvas").getContext("2d")).font=[a,r,o,n+"px",e].join(" "),u.measureText(i(t,"String")?t:"").width},function(t,r){return function(){for(var t=0,r=0,n=arguments.length;r<n;r++)t+=arguments[r].length;for(var e=Array(t),o=0,r=0;r<n;r++)for(var a=arguments[r],i=0,u=a.length;i<u;i++,o++)e[o]=a[i];return e}([t],z(r=void 0===r?{}:r)).join("")});var u,P="undefined"!=typeof Float32Array?Float32Array:Array;function g(){var t=new P(9);return P!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[5]=0,t[6]=0,t[7]=0),t[0]=1,t[4]=1,t[8]=1,t}Math.hypot||(Math.hypot=function(){for(var t=0,r=arguments.length;r--;)t+=arguments[r]*arguments[r];return Math.sqrt(t)});var J=function(t,r,n){var e=r[0],o=r[1],a=r[2],i=r[3],u=r[4],s=r[5],l=r[6],h=r[7],r=r[8],c=n[0],p=n[1],f=n[2],d=n[3],g=n[4],v=n[5],m=n[6],y=n[7],n=n[8];return t[0]=c*e+p*i+f*l,t[1]=c*o+p*u+f*h,t[2]=c*a+p*s+f*r,t[3]=d*e+g*i+v*l,t[4]=d*o+g*u+v*h,t[5]=d*a+g*s+v*r,t[6]=m*e+y*i+n*l,t[7]=m*o+y*u+n*h,t[8]=m*a+y*s+n*r,t};function R(){var t=new P(16);return P!=Float32Array&&(t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[11]=0,t[12]=0,t[13]=0,t[14]=0),t[0]=1,t[5]=1,t[10]=1,t[15]=1,t}var L=function(t,r,n){var e=r[0],o=r[1],a=r[2],i=r[3],u=r[4],s=r[5],l=r[6],h=r[7],c=r[8],p=r[9],f=r[10],d=r[11],g=r[12],v=r[13],m=r[14],r=r[15],y=n[0],M=n[1],w=n[2],b=n[3];return t[0]=y*e+M*u+w*c+b*g,t[1]=y*o+M*s+w*p+b*v,t[2]=y*a+M*l+w*f+b*m,t[3]=y*i+M*h+w*d+b*r,y=n[4],M=n[5],w=n[6],b=n[7],t[4]=y*e+M*u+w*c+b*g,t[5]=y*o+M*s+w*p+b*v,t[6]=y*a+M*l+w*f+b*m,t[7]=y*i+M*h+w*d+b*r,y=n[8],M=n[9],w=n[10],b=n[11],t[8]=y*e+M*u+w*c+b*g,t[9]=y*o+M*s+w*p+b*v,t[10]=y*a+M*l+w*f+b*m,t[11]=y*i+M*h+w*d+b*r,y=n[12],M=n[13],w=n[14],b=n[15],t[12]=y*e+M*u+w*c+b*g,t[13]=y*o+M*s+w*p+b*v,t[14]=y*a+M*l+w*f+b*m,t[15]=y*i+M*h+w*d+b*r,t};function U(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return t?r.reduce(function(r,n){return function(t){return n(r(t))}},t):A}function _(t){return t instanceof Float32Array||t instanceof Array}function V(t,r,n){for(;t<r;)t+=2*Math.PI;for(;n<t;)t-=2*Math.PI;return t}e=new P(3),P!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0),e=new P(4),P!=Float32Array&&(e[0]=0,e[1]=0,e[2]=0,e[3]=0);function G(t,r,n,e,o){var a=(t=k(t,2))[0],t=t[1],i=g();return a=[a,t],(t=i)[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=a[0],t[7]=a[1],t[8]=1,t}function l(t,...r){return r.reduce((r,n)=>t=>r(n(t)),t)}function h(r,n){return n-r?t=>(t-r)/(n-r):t=>.5}const H=Math.sqrt(50),Q=Math.sqrt(10),X=Math.sqrt(2);function s(t,r,n){r=(r-t)/Math.max(0,n),t=Math.floor(Math.log(r)/Math.LN10),n=r/10**t;return 0<=t?(n>=H?10:n>=Q?5:n>=X?2:1)*10**t:-(10**-t)/(n>=H?10:n>=Q?5:n>=X?2:1)}const Y=(t,r,n=5)=>{t=[t,r];let e=0,o=t.length-1,a=t[e],i=t[o],u;return i<a&&([a,i]=[i,a],[e,o]=[o,e]),0<(u=s(a,i,n))?(a=Math.floor(a/u)*u,i=Math.ceil(i/u)*u,u=s(a,i,n)):u<0&&(a=Math.ceil(a*u)/u,i=Math.floor(i*u)/u,u=s(a,i,n)),0<u?(t[e]=Math.floor(a/u)*u,t[o]=Math.ceil(i/u)*u):u<0&&(t[e]=Math.ceil(a*u)/u,t[o]=Math.floor(i*u)/u),t};function Z(t){return null!=t&&!Number.isNaN(t)}function tt(r,i){return t=>{t.prototype.rescale=function(){this.initRange(),this.nice();var[t]=this.chooseTransforms();this.composeOutput(t,this.chooseClamp(t))},t.prototype.initRange=function(){var t=this.options["interpolator"];this.options.range=r(t)},t.prototype.composeOutput=function(t,r){var n,{domain:e,interpolator:o,round:a}=this.getOptions(),e=i(e.map(t)),a=a?(n=o,t=>{t=n(t);return $(t)?Math.round(t):t}):o;this.output=l(a,e,r,t)},t.prototype.invert=void 0}}var n,e={exports:{}},o={exports:{}},rt=function(t){return!(!t||"string"==typeof t)&&(t instanceof Array||Array.isArray(t)||0<=t.length&&(t.splice instanceof Function||Object.getOwnPropertyDescriptor(t,t.length-1)&&"String"!==t.constructor.name))},nt=Array.prototype.concat,et=Array.prototype.slice,ot=o.exports=function(t){for(var r=[],n=0,e=t.length;n<e;n++){var o=t[n];rt(o)?r=nt.call(r,et.call(o)):r.push(o)}return r},c=(ot.wrap=function(t){return function(){return t(ot(arguments))}},{aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}),a=o.exports,at=Object.hasOwnProperty,it=Object.create(null);for(n in c)at.call(c,n)&&(it[c[n]]=n);var p=e.exports={to:{},get:{}};function f(t,r,n){return Math.min(Math.max(r,t),n)}function d(t){t=Math.round(t).toString(16).toUpperCase();return t.length<2?"0"+t:t}p.get=function(t){var r,n;switch(t.substring(0,3).toLowerCase()){case"hsl":r=p.get.hsl(t),n="hsl";break;case"hwb":r=p.get.hwb(t),n="hwb";break;default:r=p.get.rgb(t),n="rgb"}return r?{model:n,value:r}:null},p.get.rgb=function(t){if(!t)return null;var r,n,e,o=[0,0,0,1];if(r=t.match(/^#([a-f0-9]{6})([a-f0-9]{2})?$/i)){for(e=r[2],r=r[1],n=0;n<3;n++){var a=2*n;o[n]=parseInt(r.slice(a,2+a),16)}e&&(o[3]=parseInt(e,16)/255)}else if(r=t.match(/^#([a-f0-9]{3,4})$/i)){for(e=(r=r[1])[3],n=0;n<3;n++)o[n]=parseInt(r[n]+r[n],16);e&&(o[3]=parseInt(e+e,16)/255)}else{if(r=t.match(/^rgba?\(\s*([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)(?=[\s,])\s*(?:,\s*)?([+-]?\d+)\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/))for(n=0;n<3;n++)o[n]=parseInt(r[n+1],0);else{if(!(r=t.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*,?\s*([+-]?[\d\.]+)\%\s*(?:[,|\/]\s*([+-]?[\d\.]+)(%?)\s*)?\)$/)))return(r=t.match(/^(\w+)$/))?"transparent"===r[1]?[0,0,0,0]:at.call(c,r[1])?((o=c[r[1]])[3]=1,o):null:null;for(n=0;n<3;n++)o[n]=Math.round(2.55*parseFloat(r[n+1]))}r[4]&&(r[5]?o[3]=.01*parseFloat(r[4]):o[3]=parseFloat(r[4]))}for(n=0;n<3;n++)o[n]=f(o[n],0,255);return o[3]=f(o[3],0,1),o},p.get.hsl=function(t){var r;return(t=t&&t.match(/^hsla?\(\s*([+-]?(?:\d{0,3}\.)?\d+)(?:deg)?\s*,?\s*([+-]?[\d\.]+)%\s*,?\s*([+-]?[\d\.]+)%\s*(?:[,|\/]\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/))?(r=parseFloat(t[4]),[(parseFloat(t[1])%360+360)%360,f(parseFloat(t[2]),0,100),f(parseFloat(t[3]),0,100),f(isNaN(r)?1:r,0,1)]):null},p.get.hwb=function(t){var r;return(t=t&&t.match(/^hwb\(\s*([+-]?\d{0,3}(?:\.\d+)?)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?(?=\.\d|\d)(?:0|[1-9]\d*)?(?:\.\d*)?(?:[eE][+-]?\d+)?)\s*)?\)$/))?(r=parseFloat(t[4]),[(parseFloat(t[1])%360+360)%360,f(parseFloat(t[2]),0,100),f(parseFloat(t[3]),0,100),f(isNaN(r)?1:r,0,1)]):null},p.to.hex=function(){var t=a(arguments);return"#"+d(t[0])+d(t[1])+d(t[2])+(t[3]<1?d(Math.round(255*t[3])):"")},p.to.rgb=function(){var t=a(arguments);return t.length<4||1===t[3]?"rgb("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+")":"rgba("+Math.round(t[0])+", "+Math.round(t[1])+", "+Math.round(t[2])+", "+t[3]+")"},p.to.rgb.percent=function(){var t=a(arguments),r=Math.round(t[0]/255*100),n=Math.round(t[1]/255*100),e=Math.round(t[2]/255*100);return t.length<4||1===t[3]?"rgb("+r+"%, "+n+"%, "+e+"%)":"rgba("+r+"%, "+n+"%, "+e+"%, "+t[3]+")"},p.to.hsl=function(){var t=a(arguments);return t.length<4||1===t[3]?"hsl("+t[0]+", "+t[1]+"%, "+t[2]+"%)":"hsla("+t[0]+", "+t[1]+"%, "+t[2]+"%, "+t[3]+")"},p.to.hwb=function(){var t=a(arguments),r="";return 4<=t.length&&1!==t[3]&&(r=", "+t[3]),"hwb("+t[0]+", "+t[1]+"%, "+t[2]+"%"+r+")"},p.to.keyword=function(t){return it[t.slice(0,3)]};var ut=e.exports;function v(t,r,n){let e=n;return e<0&&(e+=1),1<e&&--e,e<1/6?t+6*(r-t)*e:e<.5?r:e<2/3?t+(r-t)*(2/3-e)*6:t}function st(t){var r,n,e,t=ut.get(t);return t?({model:t,value:r}=t,"rgb"===t?r:"hsl"===t?(r=(t=r)[0]/360,n=t[1]/100,e=t[2]/100,t=t[3],0==n?[255*e,255*e,255*e,t]:[255*v(n=2*e-(e=e<.5?e*(1+n):e+n-e*n),e,r+1/3),255*v(n,e,r),255*v(n,e,r-1/3),t]):null):null}const m=(r,n)=>t=>r*(1-t)+n*t,lt=(t,r)=>{if("number"==typeof t&&"number"==typeof r)return m(t,r);if("string"!=typeof t||"string"!=typeof r)return()=>t;{var n=t,e=r;const s=st(n),l=st(e);return null===s||null===l?s?()=>n:()=>e:r=>{var n=new Array(4);for(let t=0;t<4;t+=1){var e=s[t],o=l[t];n[t]=e*(1-r)+o*r}var[t,a,i,u]=n;return`rgba(${Math.round(t)}, ${Math.round(a)}, ${Math.round(i)}, ${u})`}}},ht=(t,r)=>{const n=m(t,r);return t=>Math.round(n(t))};function ct({map:t,initKey:r},n){r=r(n);return t.has(r)?t.get(r):n}function pt(t){return"object"==typeof t?t.valueOf():t}class ft extends Map{constructor(t){if(super(),this.map=new Map,this.initKey=pt,null!==t)for(var[r,n]of t)this.set(r,n)}get(t){return super.get(ct({map:this.map,initKey:this.initKey},t))}has(t){return super.has(ct({map:this.map,initKey:this.initKey},t))}set(t,r){return super.set(([{map:t,initKey:n},e]=[{map:this.map,initKey:this.initKey},t],n=n(e),t.has(n)?t.get(n):(t.set(n,e),e)),r);var n,e}delete(t){return super.delete(([{map:t,initKey:r},n]=[{map:this.map,initKey:this.initKey},t],r=r(n),t.has(r)&&(n=t.get(r),t.delete(r)),n));var r,n}}class dt{constructor(t){this.options=r({},this.getDefaultOptions()),this.update(t)}getOptions(){return this.options}update(t={}){this.options=r({},this.options,t),this.rescale(t)}rescale(t){}}const y=Symbol("defaultUnknown");function gt(r,n,e){for(let t=0;t<n.length;t+=1)r.has(n[t])||r.set(e(n[t]),t)}function vt(t){var{value:t,from:r,to:n,mapper:e,notFoundReturn:o}=t;let a=e.get(t);if(void 0===a){if(o!==y)return o;a=r.push(t)-1,e.set(t,a)}return n[a%n.length]}function mt(t){return t instanceof Date?t=>""+t:"object"==typeof t?t=>JSON.stringify(t):t=>t}class yt extends dt{getDefaultOptions(){return{domain:[],range:[],unknown:y}}constructor(t){super(t)}map(t){return 0===this.domainIndexMap.size&&gt(this.domainIndexMap,this.getDomain(),this.domainKey),vt({value:this.domainKey(t),mapper:this.domainIndexMap,from:this.getDomain(),to:this.getRange(),notFoundReturn:this.options.unknown})}invert(t){return 0===this.rangeIndexMap.size&&gt(this.rangeIndexMap,this.getRange(),this.rangeKey),vt({value:this.rangeKey(t),mapper:this.rangeIndexMap,from:this.getRange(),to:this.getDomain(),notFoundReturn:this.options.unknown})}rescale(t){var[r]=this.options.domain,[n]=this.options.range;this.domainKey=mt(r),this.rangeKey=mt(n),this.rangeIndexMap?(t&&!t.range||this.rangeIndexMap.clear(),t&&!t.domain&&!t.compare||(this.domainIndexMap.clear(),this.sortedDomain=void 0)):(this.rangeIndexMap=new Map,this.domainIndexMap=new Map)}clone(){return new yt(this.options)}getRange(){return this.options.range}getDomain(){var t,r;return this.sortedDomain||({domain:t,compare:r}=this.options,this.sortedDomain=r?[...t].sort(r):t),this.sortedDomain}}function Mt(t){const{domain:r,range:n,paddingOuter:e,paddingInner:o,flex:a,round:i,align:u}=t;var s=r.length,l=0<(l=(t=s)-(h=a).length)?[...h,...new Array(l).fill(1)]:l<0?h.slice(0,t):h,[t,h]=n,h=h-t,c=h/(2/s*e+1-1/s*o);const p=c*o/s;c-=s*p;const f=function(t){const r=Math.min(...t);return t.map(t=>t/r)}(l),d=c/f.reduce((t,r)=>t+r);var l=new ft(r.map((t,r)=>{r=f[r]*d;return[t,i?Math.floor(r):r]})),g=new ft(r.map((t,r)=>{r=f[r]*d+p;return[t,i?Math.floor(r):r]})),c=Array.from(g.values()).reduce((t,r)=>t+r),t=t+(h-(c-c/s*o))*u;let v=i?Math.round(t):t;var m=new Array(s);for(let t=0;t<s;t+=1){m[t]=(y=v,Math.round(1e12*y)/1e12);var y=r[t];v+=g.get(y)}return{valueBandWidth:l,valueStep:g,adjustedRange:m}}class wt extends yt{getDefaultOptions(){return{domain:[],range:[0,1],align:.5,round:!1,paddingInner:0,paddingOuter:0,padding:0,unknown:y,flex:[]}}constructor(t){super(t)}clone(){return new wt(this.options)}getStep(t){return void 0===this.valueStep?1:"number"==typeof this.valueStep?this.valueStep:void 0===t?Array.from(this.valueStep.values())[0]:this.valueStep.get(t)}getBandWidth(t){return void 0===this.valueBandWidth?1:"number"==typeof this.valueBandWidth?this.valueBandWidth:void 0===t?Array.from(this.valueBandWidth.values())[0]:this.valueBandWidth.get(t)}getRange(){return this.adjustedRange}getPaddingInner(){var{padding:t,paddingInner:r}=this.options;return 0<t?t:r}getPaddingOuter(){var{padding:t,paddingOuter:r}=this.options;return 0<t?t:r}rescale(){super.rescale();var{align:t,domain:r,range:n,round:e,flex:o}=this.options,{adjustedRange:t,valueBandWidth:n,valueStep:e}=function(t){var r=t["domain"];if(0===(r=r.length))return{valueBandWidth:void 0,valueStep:void 0,adjustedRange:[]};if(!(null==(n=t.flex)||!n.length))return Mt(t);var{range:n,paddingOuter:t,paddingInner:e,round:o,align:a}=t;let i,u,s=n[0];return n=n[1]-s,i=n/Math.max(1,2*t+(r-e)),o&&(i=Math.floor(i)),s+=(n-i*(r-e))*a,u=i*(1-e),o&&(s=Math.round(s),u=Math.round(u)),t=new Array(r).fill(0).map((t,r)=>s+r*i),{valueStep:i,valueBandWidth:u,adjustedRange:t}}({align:t,range:n,round:e,flex:o,paddingInner:this.getPaddingInner(),paddingOuter:this.getPaddingOuter(),domain:r});this.valueStep=e,this.valueBandWidth=n,this.adjustedRange=t}}const M=(t,r,n)=>{let e,o,a=t,i=r;if(a===i&&0<n)return[a];let u=s(a,i,n);if(0===u||!Number.isFinite(u))return[];if(0<u){a=Math.ceil(a/u),i=Math.floor(i/u),o=new Array(e=Math.ceil(i-a+1));for(let t=0;t<e;t+=1)o[t]=(a+t)*u}else{u=-u,a=Math.ceil(a*u),i=Math.floor(i*u),o=new Array(e=Math.ceil(i-a+1));for(let t=0;t<e;t+=1)o[t]=(a+t)/u}return o},bt=(t,r,n)=>{var[t,e]=t,[r,o]=r;let a,i;return l(i=t<e?(a=h(t,e),n(r,o)):(a=h(e,t),n(o,r)),a)},xt=(e,t,r)=>{const o=Math.min(e.length,t.length)-1,a=new Array(o),i=new Array(o);var n=e[0]>e[o],u=n?[...e].reverse():e,s=n?[...t].reverse():t;for(let t=0;t<o;t+=1)a[t]=h(u[t],u[t+1]),i[t]=r(s[t],s[t+1]);return t=>{var r=function(t,r,n,e,o){let a=n||0,i=e||t.length;for(var u=o||(t=>t);a<i;){var s=Math.floor((a+i)/2);u(t[s])>r?i=s:a=s+1}return a}(e,t,1,o)-1,n=a[r];return l(i[r],n)(t)}},kt=(t,r,n,e)=>{return(2<Math.min(t.length,r.length)?xt:bt)(t,r,e?ht:n)};class Ot extends dt{getDefaultOptions(){return{domain:[0,1],range:[0,1],nice:!1,clamp:!1,round:!1,interpolate:m,tickCount:5}}map(t){return Z(t)?this.output(t):this.options.unknown}invert(t){return Z(t)?this.input(t):this.options.unknown}nice(){var t,r,n,e;this.options.nice&&([t,r,n,...e]=this.getTickMethodOptions(),this.options.domain=this.chooseNice()(t,r,n,...e))}getTicks(){var t=this.options["tickMethod"],[r,n,e,...o]=this.getTickMethodOptions();return t(r,n,e,...o)}getTickMethodOptions(){var{domain:t,tickCount:r}=this.options;return[t[0],t[t.length-1],r]}chooseNice(){return Y}rescale(){this.nice();var[t,r]=this.chooseTransforms();this.composeOutput(t,this.chooseClamp(t)),this.composeInput(t,r,this.chooseClamp(r))}chooseClamp(t){var{clamp:r,range:n}=this.options,t=this.options.domain.map(t),n=Math.min(t.length,n.length);if(r){r=t[0],t=t[n-1];const e=t<r?t:r,o=t<r?r:t;return t=>Math.min(Math.max(e,t),o)}return A}composeOutput(t,r){var{domain:n,range:e,round:o,interpolate:a}=this.options,n=kt(n.map(t),e,a,o);this.output=l(n,r,t)}composeInput(t,r,n){var{domain:e,range:o}=this.options,o=kt(o,e.map(t),m);this.input=l(r,n,o)}}class w extends Ot{getDefaultOptions(){return{domain:[0,1],range:[0,1],unknown:void 0,nice:!1,clamp:!1,round:!1,interpolate:lt,tickMethod:M,tickCount:5}}chooseTransforms(){return[A,A]}clone(){return new w(this.options)}}class At extends wt{getDefaultOptions(){return{domain:[],range:[0,1],align:.5,round:!1,padding:0,unknown:y,paddingInner:1,paddingOuter:0}}constructor(t){super(t)}getPaddingInner(){return 1}clone(){return new At(this.options)}update(t){super.update(t)}getPaddingOuter(){return this.options.padding}}function jt(t,r){for(var n=[],e=0,o=t.length;e<o;e++)n.push(t[e].substr(0,r));return n}var b,o=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],e=["January","February","March","April","May","June","July","August","September","October","November","December"],j=jt(e,3);!function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];for(var e=0,o=r;e<o.length;e++){var a,i=o[e];for(a in i)t[a]=i[a]}}({},{dayNamesShort:jt(o,3),dayNames:o,monthNamesShort:j,monthNames:e,amPm:["am","pm"],DoFn:function(t){return t+["th","st","nd","rd"][3<t%10?0:(t-t%10!=10?1:0)*t%10]}});var I;let It=b=class extends w{getDefaultOptions(){return{domain:[0,1],unknown:void 0,nice:!1,clamp:!1,round:!1,interpolator:A,tickMethod:M,tickCount:5}}constructor(t){super(t)}clone(){return new b(this.options)}};It=b=function(t,r,n,e){var o,a=arguments.length,i=a<3?r:null===e?e=Object.getOwnPropertyDescriptor(r,n):e;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(t,r,n,e);else for(var u=t.length-1;0<=u;u--)(o=t[u])&&(i=(a<3?o(i):3<a?o(r,n,i):o(r,n))||i);return 3<a&&i&&Object.defineProperty(r,n,i),i}([tt(function(t){return[t(0),t(1)]},t=>{var[t,r]=t;return l(m(0,1),h(t,r))})],It);let St=I=class extends w{getDefaultOptions(){return{domain:[0,.5,1],unknown:void 0,nice:!1,clamp:!1,round:!1,interpolator:A,tickMethod:M,tickCount:5}}constructor(t){super(t)}clone(){return new I(this.options)}};St=I=function(t,r,n,e){var o,a=arguments.length,i=a<3?r:null===e?e=Object.getOwnPropertyDescriptor(r,n):e;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(t,r,n,e);else for(var u=t.length-1;0<=u;u--)(o=t[u])&&(i=(a<3?o(i):3<a?o(r,n,i):o(r,n))||i);return 3<a&&i&&Object.defineProperty(r,n,i),i}([tt(function(t){return[t(0),t(.5),t(1)]},t=>{const[r,n,e]=t,o=l(m(0,.5),h(r,n)),a=l(m(.5,1),h(n,e));return t=>(r>e?t<n?a:o:t<n?o:a)(t)})],St);function Ft(t,r,n,e,o){var a=new w({range:[r,r+e]}),i=new w({range:[n,n+o]});return{transform:function(t){var t=k(t,2),r=t[0],t=t[1];return[a.map(r),i.map(t)]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1];return[a.invert(r),i.invert(t)]}}}function Dt(t,r,n,e,o){return(0,k(t,1)[0])(r,n,e,o)}function Pt(t,r,n,e,o){return k(t,1)[0]}function Rt(t,r,n,e,o){var a=(t=k(t,4))[0],i=t[1],u=t[2],t=t[3],s=new w({range:[u,t]}),l=new w({range:[a,i]}),h=1<(u=o/e)?1:u,c=1<u?1/u:1;return{transform:function(t){var t=k(t,2),r=t[0],t=t[1],r=l.map(r),t=s.map(t);return[.5*(t*Math.cos(r)*h)+.5,.5*(t*Math.sin(r)*c)+.5]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1],r=2*(r-.5)/h,t=2*(t-.5)/c,n=Math.sqrt(Math.pow(r,2)+Math.pow(t,2)),t=V(Math.atan2(t,r),a,i);return[l.invert(t),s.invert(n)]}}}function Tt(t,r,n,e,o){return{transform:function(t){var t=k(t,2),r=t[0];return[t[1],r]},untransform:function(t){var t=k(t,2),r=t[0];return[t[1],r]}}}function Kt(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return S.apply(void 0,O([[-1,-1]],k(r),!1))}function Nt(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return S.apply(void 0,O([[-1,1]],k(r),!1))}function qt(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return S.apply(void 0,O([[1,-1]],k(r),!1))}function Ct(t,r,n,e,o){var a,t=k(t,1)[0],i=g();return i=i,t=t,a=Math.sin(t),t=Math.cos(t),i[0]=t,i[1]=a,i[2]=0,i[3]=-a,i[4]=t,i[5]=0,i[6]=0,i[7]=0,i[8]=1,i}function Wt(t,r,n,e,o){var a=(t=k(t,4))[0],i=t[1],u=t[2],s=(t[3]-u)/(+i/(2*Math.PI)+1),l=s/(2*Math.PI),h=new w({range:[u,u+.99*s]}),c=new w({range:[a,i]}),p=1<(t=o/e)?1:t,f=1<t?1/t:1;return{transform:function(t){var t=k(t,2),r=t[0],t=t[1],r=c.map(r),t=h.map(t);return[.5*(Math.cos(r)*(l*r+t)*p)+.5,.5*(Math.sin(r)*(l*r+t)*f)+.5]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1],r=2*(r-.5)/p,t=2*(t-.5)/f,n=Math.sqrt(Math.pow(r,2)+Math.pow(t,2)),t=V(Math.atan2(t,r)+Math.floor(n/s)*Math.PI*2,a,i),r=n-l*t;return[c.invert(t),h.invert(r)]}}}function Bt(t,r,n,e,o){var u=(t=k(t,4))[0],s=t[1],a=t[2],t=t[3],l=new w({range:[a,t]});return{transform:function(t){for(var r=[],n=t.length,e=new At({domain:new Array(n).fill(0).map(function(t,r){return r}),range:[u,s]}),o=0;o<n;o++){var a=t[o],i=e.map(o),a=l.map(a);r.push(i,a)}return r},untransform:function(t){for(var r=[],n=0;n<t.length;n+=2){var e=t[n+1];r.push(l.invert(e))}return r}}}var S=function(t,r,n,e,o){var t=k(t,2),a=t[0],t=t[1],i=g();return a=[a,t],(t=i)[0]=a[0],t[1]=0,t[2]=0,t[3]=0,t[4]=a[1],t[5]=0,t[6]=0,t[7]=0,t[8]=1,t};function $t(t){return 1/Math.tan(t)}function zt(t,r,n,e,o){var a=$t(k(t,1)[0]);return{transform:function(t){var t=k(t,2),r=t[0],t=t[1];return[r+t*a,t]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1];return[r-t*a,t]}}}function Et(t,r,n,e,o){var a=$t(k(t,1)[0]);return{transform:function(t){var t=k(t,2),r=t[0];return[r,t[1]+r*a]},untransform:function(t){var t=k(t,2),r=t[0];return[r,t[1]-r*a]}}}function F(t,r,n,e,o){var a=t<r,o=(a?r-e:o-r)||o-e,e=a?-1:1;return e*o*(n+1)/(n+o/((t-r)*e))+r}function D(t,r,n,e,o){var a=t<r,o=(a?r-e:o-r)||o-e;return o/(o*(n+1)/(t-r)-n*(a?-1:1))+r}function T(t,r,n){return n?new w({range:[0,1],domain:[0,r]}).map(t):t}function Jt(t,r,n,e,o){var a=(t=k(t,3))[0],i=t[1],t=t[2],u=T(a,e,void 0!==t&&t);return{transform:function(t){var t=k(t,2),r=t[0],t=t[1];return[F(r,u,i,0,1),t]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1];return[D(r,u,i,0,1),t]}}}function Lt(t,r,n,e,o){var a=(t=k(t,3))[0],i=t[1],t=t[2],u=T(a,o,void 0!==t&&t);return{transform:function(t){t=k(t,2);return[t[0],F(t[1],u,i,0,1)]},untransform:function(t){t=k(t,2);return[t[0],D(t[1],u,i,0,1)]}}}function Ut(t,r,n,e,o){var a=(t=k(t,5))[0],i=t[1],u=t[2],s=t[3],t=t[4],l=T(a,e,t=void 0!==t&&t),h=T(i,o,t);return{transform:function(t){var t=k(t,2),r=t[0],t=t[1];return[F(r,l,u,0,1),F(t,h,s,0,1)]},untransform:function(t){var t=k(t,2),r=t[0],t=t[1];return[D(r,l,u,0,1),D(t,h,s,0,1)]}}}function _t(t,r,n,e,o){var a=(t=k(t,5))[0],i=t[1],u=t[2],s=t[3],t=void 0!==(t=t[4])&&t,l=new w({range:[0,e]}),h=new w({range:[0,o]}),c=t?a:l.map(a),p=t?i:h.map(i);return{transform:function(t){var t=k(t,2),r=t[0],t=t[1],n=l.map(r)-c,e=h.map(t)-p,o=Math.sqrt(n*n+e*e);return u<o?[r,t]:(r=F(o,0,s,0,u),t=Math.atan2(e,n),o=c+r*Math.cos(t),e=p+r*Math.sin(t),[l.invert(o),h.invert(e)])},untransform:function(t){var t=k(t,2),r=t[0],t=t[1],n=l.map(r)-c,e=h.map(t)-p,o=Math.sqrt(n*n+e*e);return u<o?[r,t]:(r=D(o,0,s,0,u),t=Math.atan2(e,n),o=c+r*Math.cos(t),e=p+r*Math.sin(t),[l.invert(o),h.invert(e)])}}}function Vt(t,r,n,e,o,a,i){var u=new w({range:[r,r+o]}),s=new w({range:[n,n+a]}),l=new w({range:[e,e+i]});return{transform:function(t){var t=k(t,3),r=t[0],n=t[1],t=t[2];return[u.map(r),s.map(n),l.map(t)]},untransform:function(t){var t=k(t,3),r=t[0],n=t[1],t=t[2];return[u.invert(r),s.invert(n),l.invert(t)]}}}function Gt(t,r,n,e,o,a,i){var u,s=(t=k(t,3))[0],l=t[1],t=t[2];return u=R(),s=[s,l,t],u[0]=1,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=1,u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=1,u[11]=0,u[12]=s[0],u[13]=s[1],u[14]=s[2],u[15]=1,u}function Ht(t,r,n,e,o,a,i){return{transform:function(t){var t=k(t,3),r=t[0];return[t[1],r,t[2]]},untransform:function(t){var t=k(t,3),r=t[0];return[t[1],r,t[2]]}}}function Qt(t,r,n,e,o,a,i){var u,s=(t=k(t,3))[0],l=t[1],t=t[2];return u=R(),s=[s,l,t],u[0]=s[0],u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=s[1],u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=s[2],u[11]=0,u[12]=0,u[13]=0,u[14]=0,u[15]=1,u}K.prototype.update=function(t){this.options=r({},this.options,t),this.recoordinate()},K.prototype.clone=function(){return new K(this.options)},K.prototype.getOptions=function(){return this.options},K.prototype.clear=function(){this.update({transformations:[]})},K.prototype.getSize=function(){var t=this.options;return[t.width,t.height]},K.prototype.getCenter=function(){var t=this.options,r=t.x,n=t.y;return[(2*r+t.width)/2,(2*n+t.height)/2]},K.prototype.transform=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this.options.transformations;return this.update({transformations:O(O([],k(n),!1),[O([],k(t),!1)],!1)}),this},K.prototype.map=function(t){return this.output(t)},K.prototype.invert=function(t){return this.input(t)},K.prototype.recoordinate=function(){this.output=this.compose(),this.input=this.compose(!0)},K.prototype.compose=function(t){function r(t,r){var o;u.push((r=void 0===r?!0:r)?(o=t,function(t){for(var r=[],n=0;n<t.length-1;n+=2){var e=[t[n],t[n+1]],e=o(e);r.push.apply(r,O([],k(e),!1))}return r}):t)}var n,e,o=(t=void 0===t?!1:t)?O([],k(this.options.transformations),!1).reverse():this.options.transformations,a=t?function(t){return t.untransform}:function(t){return t.transform},i=[],u=[];try{for(var s=x(o),l=s.next();!l.done;l=s.next()){var h,c,p,f,d,g,v=k(l.value),m=v[0],y=v.slice(1),M=this.transformers[m];M&&(c=(h=this.options).x,p=h.y,f=h.width,d=h.height,_(g=M(O([],k(y),!1),c,p,f,d))?i.push(g):(i.length&&(r(this.createMatrixTransform(i,t)),i.splice(0,i.length)),r(a(g)||A,"parallel"!==m)))}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=s.return)&&e.call(s)}finally{if(n)throw n.error}}return i.length&&r(this.createMatrixTransform(i,t)),U.apply(void 0,O([],k(u),!1))},K.prototype.createMatrixTransform=function(t,r){var n,e,o,a,i,u,s,l,h,c,p,f,d=g();return r&&t.reverse(),t.forEach(function(t){return J(d,d,t)}),r&&(r=t=d,(f=new P(9))[0]=r[0],f[1]=r[1],f[2]=r[2],f[3]=r[3],f[4]=r[4],f[5]=r[5],f[6]=r[6],f[7]=r[7],f[8]=r[8],f=(r=f)[0],n=r[1],e=r[2],o=r[3],a=r[4],i=r[5],u=r[6],s=r[7],l=(r=r[8])*a-i*s,p=f*l+n*(h=-r*o+i*u)+e*(c=s*o-a*u))&&(t[0]=l*(p=1/p),t[1]=(-r*n+e*s)*p,t[2]=(i*n-e*a)*p,t[3]=h*p,t[4]=(r*f-e*u)*p,t[5]=(-i*f+e*o)*p,t[6]=c*p,t[7]=(-s*f+n*u)*p,t[8]=(a*f-n*o)*p),function(t){var r,n,e,o,a,t=[t[0],t[1],1];return e=d,o=(n=r=t)[0],a=n[1],n=n[2],r[0]=o*e[0]+a*e[3]+n*e[6],r[1]=o*e[1]+a*e[4]+n*e[7],r[2]=o*e[2]+a*e[5]+n*e[8],[t[0],t[1]]}};o=K;function K(t){this.options={x:0,y:0,width:300,height:150,transformations:[]},this.transformers={cartesian:Ft,translate:G,custom:Dt,matrix:Pt,polar:Rt,transpose:Tt,scale:S,"shear.x":zt,"shear.y":Et,reflect:Kt,"reflect.x":Nt,"reflect.y":qt,rotate:Ct,helix:Wt,parallel:Bt,fisheye:Ut,"fisheye.x":Jt,"fisheye.y":Lt,"fisheye.circular":_t},this.update(t)}N.prototype.update=function(t){this.options=r({},this.options,t),this.recoordinate()},N.prototype.clone=function(){return new N(this.options)},N.prototype.getOptions=function(){return this.options},N.prototype.clear=function(){this.update({transformations:[]})},N.prototype.getSize=function(){var t=this.options;return[t.width,t.height,t.depth]},N.prototype.getCenter=function(){var t=this.options,r=t.x,n=t.y,e=t.z;return[(2*r+t.width)/2,(2*n+t.height)/2,(2*e+t.depth)/2]},N.prototype.transform=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=this.options.transformations;return this.update({transformations:O(O([],k(n),!1),[O([],k(t),!1)],!1)}),this},N.prototype.map=function(t){return this.output(t)},N.prototype.invert=function(t){return this.input(t)},N.prototype.recoordinate=function(){this.output=this.compose(),this.input=this.compose(!0)},N.prototype.compose=function(t){function r(t,r){var o;u.push((r=void 0===r?!0:r)?(o=t,function(t){for(var r=[],n=0;n<t.length-1;n+=3){var e=[t[n],t[n+1],t[n+2]],e=o(e);r.push.apply(r,O([],k(e),!1))}return r}):t)}var n,e,o=(t=void 0===t?!1:t)?O([],k(this.options.transformations),!1).reverse():this.options.transformations,a=t?function(t){return t.untransform}:function(t){return t.transform},i=[],u=[];try{for(var s=x(o),l=s.next();!l.done;l=s.next()){var h,c,p,f,d,g,v,m,y=k(l.value),M=y[0],w=y.slice(1),b=this.transformers[M];b&&(c=(h=this.options).x,p=h.y,f=h.z,d=h.width,g=h.height,v=h.depth,_(m=b(O([],k(w),!1),c,p,f,d,g,v))?i.push(m):(i.length&&(r(this.createMatrixTransform(i,t)),i.splice(0,i.length)),r(a(m)||A,!0)))}}catch(t){n={error:t}}finally{try{l&&!l.done&&(e=s.return)&&e.call(s)}finally{if(n)throw n.error}}return i.length&&r(this.createMatrixTransform(i,t)),U.apply(void 0,O([],k(u),!1))},N.prototype.createMatrixTransform=function(t,r){var n,e,o,a,i,u,s,l,h,c,p,f,d,g,v,m,y,M,w,b,x,k,O,A,j,I,S,F,D=R();return r&&t.reverse(),t.forEach(function(t){return L(D,D,t)}),r&&(r=t=D,(F=new P(16))[0]=r[0],F[1]=r[1],F[2]=r[2],F[3]=r[3],F[4]=r[4],F[5]=r[5],F[6]=r[6],F[7]=r[7],F[8]=r[8],F[9]=r[9],F[10]=r[10],F[11]=r[11],F[12]=r[12],F[13]=r[13],F[14]=r[14],F[15]=r[15],F=(r=F)[0],n=r[1],e=r[2],o=r[3],a=r[4],i=r[5],u=r[6],s=r[7],l=r[8],h=r[9],c=r[10],p=r[11],f=r[12],d=r[13],g=r[14],r=r[15],S=(v=F*i-n*a)*(I=c*r-p*g)-(m=F*u-e*a)*(j=h*r-p*d)+(y=F*s-o*a)*(A=h*g-c*d)+(M=n*u-e*i)*(O=l*r-p*f)-(w=n*s-o*i)*(k=l*g-c*f)+(b=e*s-o*u)*(x=l*d-h*f))&&(t[0]=(i*I-u*j+s*A)*(S=1/S),t[1]=(e*j-n*I-o*A)*S,t[2]=(d*b-g*w+r*M)*S,t[3]=(c*w-h*b-p*M)*S,t[4]=(u*O-a*I-s*k)*S,t[5]=(F*I-e*O+o*k)*S,t[6]=(g*y-f*b-r*m)*S,t[7]=(l*b-c*y+p*m)*S,t[8]=(a*j-i*O+s*x)*S,t[9]=(n*O-F*j-o*x)*S,t[10]=(f*w-d*y+r*v)*S,t[11]=(h*y-l*w-p*v)*S,t[12]=(i*k-a*A-u*x)*S,t[13]=(F*A-n*k+e*x)*S,t[14]=(d*m-f*M-g*v)*S,t[15]=(l*M-h*m+c*v)*S),function(t){var r,n,e,o,a,i,t=[t[0],t[1],t[2],1];return e=D,o=(n=r=t)[0],a=n[1],i=n[2],n=n[3],r[0]=e[0]*o+e[4]*a+e[8]*i+e[12]*n,r[1]=e[1]*o+e[5]*a+e[9]*i+e[13]*n,r[2]=e[2]*o+e[6]*a+e[10]*i+e[14]*n,r[3]=e[3]*o+e[7]*a+e[11]*i+e[15]*n,[t[0],t[1],t[2]]}};j=N;function N(t){this.options={x:0,y:0,z:0,width:300,height:150,depth:150,transformations:[]},this.transformers={cartesian3D:Vt,translate3D:Gt,scale3D:Qt,transpose3D:Ht},this.update(t)}t.Coordinate=o,t.Coordinate3D=j,Object.defineProperty(t,"__esModule",{value:!0})});
