import { CreateTransformer } from '../type';
/**
 * Apply reflect transformation for current vector.
 * @param args same as scale
 * @returns transformer
 */
export declare const reflect: CreateTransformer;
/**
 * Apply reflect transformation for current vector.
 * @param args same as scale
 * @returns transformer
 */
export declare const reflectX: CreateTransformer;
/**
 * Apply reflect transformation for current vector.
 * @param args same as scale
 * @returns transformer
 */
export declare const reflectY: CreateTransformer;
