export type { Chart as G2Chart } from '@antv/g2';
export declare const Chart: new (options?: import("@antv/g2/lib/api/runtime").RuntimeOptions) => import("@antv/g2/lib/api/extend").API<import("@antv/g2").G2Spec, {
    "interaction.drillDown": typeof import("@antv/g2-extension-plot").DrillDown;
    "mark.sunburst": import("@antv/g2-extension-plot/lib/mark/types").CompositeMarkComponent<import("@antv/g2-extension-plot").SunburstOptions>;
    'data.fetch': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").FetchOptions>;
    'data.inline': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").InlineOptions>;
    'data.sortBy': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").SortByOptions>;
    'data.sort': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").SortOptions>;
    'data.filter': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").FilterDataOptions>;
    'data.pick': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").PickOptions>;
    'data.rename': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").RenameOptions>;
    'data.fold': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").FoldOptions>;
    'data.slice': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").SliceOptions>;
    'data.custom': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").CustomOptions>;
    'data.map': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").MapOptions>;
    'data.join': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").JoinOptions>;
    'data.kde': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").KDEOptions>;
    'data.log': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").LogDataOptions>;
    'data.wordCloud': import("@antv/g2/lib/runtime").DataComponent<Partial<import("@antv/g2/lib/data").WordCloudOptions>>;
    'data.ema': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").EMAOptions>;
    'transform.stackY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").StackYOptions>;
    'transform.binX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").BinXOptions>;
    'transform.bin': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").BinOptions>;
    'transform.dodgeX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").DodgeXOptions>;
    'transform.jitter': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").JitterOptions>;
    'transform.jitterX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").JitterXOptions>;
    'transform.jitterY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").JitterYOptions>;
    'transform.symmetryY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SymmetryYOptions>;
    'transform.diffY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").DiffYOptions>;
    'transform.stackEnter': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").StackEnterOptions>;
    'transform.normalizeY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").NormalizeYOptions>;
    'transform.select': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SelectOptions>;
    'transform.selectX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SelectXOptions>;
    'transform.selectY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SelectYOptions>;
    'transform.groupX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").GroupXOptions>;
    'transform.groupY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").GroupYOptions>;
    'transform.groupColor': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").GroupColorOptions>;
    'transform.group': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").GroupOptions>;
    'transform.sortX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SortXOptions>;
    'transform.sortY': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SortYOptions>;
    'transform.sortColor': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SortColorOptions>;
    'transform.flexX': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").FlexXOptions>;
    'transform.pack': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").PackOptions>;
    'transform.sample': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").SampleOptions>;
    'transform.filter': import("@antv/g2/lib/runtime").TransformComponent<import("@antv/g2").FilterOptions>;
    'coordinate.cartesian': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2/lib/coordinate").CartesianOptions>;
    'coordinate.polar': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2/lib/coordinate").PolarOptions>;
    'coordinate.transpose': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2/lib/coordinate").TransposeOptions>;
    'coordinate.theta': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2").ThetaCoordinate>;
    'coordinate.parallel': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2/lib/coordinate").ParallelOptions>;
    'coordinate.fisheye': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2").FisheyeCoordinate>;
    'coordinate.radial': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2/lib/coordinate").RadialOptions>;
    'coordinate.radar': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2").RadarCoordinate>;
    'coordinate.helix': import("@antv/g2/lib/runtime").CoordinateComponent<import("@antv/g2").HelixCoordinate>;
    'encode.constant': import("@antv/g2/lib/runtime").EncodeComponent<import("@antv/g2/lib/encode").ConstantOptions>;
    'encode.field': import("@antv/g2/lib/runtime").EncodeComponent<import("@antv/g2/lib/encode").FieldOptions>;
    'encode.transform': import("@antv/g2/lib/runtime").EncodeComponent<import("@antv/g2/lib/encode").TransformOptions>;
    'encode.column': import("@antv/g2/lib/runtime").EncodeComponent<import("@antv/g2/lib/encode").ColumnOptions>;
    'mark.interval': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").IntervalOptions>;
    'mark.rect': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").RectOptions>;
    'mark.line': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").LineOptions>;
    'mark.point': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").PointOptions>;
    'mark.text': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").TextOptions>;
    'mark.cell': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").CellOptions>;
    'mark.area': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").AreaOptions>;
    'mark.link': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").LinkOptions>;
    'mark.image': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").ImageOptions>;
    'mark.polygon': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").PolygonOptions>;
    'mark.box': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").BoxOptions>;
    'mark.vector': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").VectorOptions>;
    'mark.lineX': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").LineXOptions>;
    'mark.lineY': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").LineYOptions>;
    'mark.connector': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").ConnectorOptions>;
    'mark.range': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").RangeOptions>;
    'mark.rangeX': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").RangeXOptions>;
    'mark.rangeY': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").RangeYOptions>;
    'mark.path': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark/path").PathOptions>;
    'mark.shape': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").ShapeOptions>;
    'mark.density': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").DensityOptions>;
    'mark.heatmap': import("@antv/g2").MarkComponent<import("@antv/g2/lib/mark").HeatmapOptions>;
    'mark.wordCloud': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").WordCloudOptions>;
    'palette.category10': import("@antv/g2/lib/runtime").PaletteComponent<import("@antv/g2/lib/palette").Category10Options>;
    'palette.category20': import("@antv/g2/lib/runtime").PaletteComponent<import("@antv/g2/lib/palette").Category20Options>;
    'scale.linear': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").LinearOptions>;
    'scale.ordinal': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").OrdinalOptions>;
    'scale.band': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").BandOptions>;
    'scale.identity': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").IdentityOptions>;
    'scale.point': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").PointOptions>;
    'scale.time': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").TimeOptions>;
    'scale.log': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").LogOptions>;
    'scale.pow': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").PowOptions>;
    'scale.sqrt': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").SqrtOptions>;
    'scale.threshold': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").ThresholdOptions>;
    'scale.quantile': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").QuantileOptions>;
    'scale.quantize': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").QuantizeOptions>;
    'scale.sequential': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").SequentialOptions>;
    'scale.constant': import("@antv/g2/lib/runtime").ScaleComponent<import("@antv/g2/lib/scale").ConstantOptions>;
    'theme.classic': import("@antv/g2/lib/runtime").ThemeComponent<import("@antv/g2/lib/runtime").G2Theme>;
    'theme.classicDark': import("@antv/g2/lib/runtime").ThemeComponent<import("@antv/g2/lib/runtime").G2Theme>;
    'theme.academy': import("@antv/g2/lib/runtime").ThemeComponent<import("@antv/g2/lib/runtime").G2Theme>;
    'theme.light': import("@antv/g2/lib/runtime").ThemeComponent<import("@antv/g2/lib/runtime").G2Theme>;
    'theme.dark': import("@antv/g2/lib/runtime").ThemeComponent<import("@antv/g2/lib/runtime").G2Theme>;
    'component.axisX': import("@antv/g2").GuideComponentComponent<import("@antv/g2").AxisOptions>;
    'component.axisY': import("@antv/g2").GuideComponentComponent<import("@antv/g2").AxisOptions>;
    'component.legendCategory': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component").LegendCategoryOptions>;
    'component.legendContinuous': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component").LegendContinuousOptions>;
    'component.legends': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component").LegendsOptions>;
    'component.title': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/runtime").G2Title>;
    'component.sliderX': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component/slider").SliderOptions>;
    'component.sliderY': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component/slider").SliderOptions>;
    'component.scrollbarX': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component/scrollbar").ScrollbarOptions>;
    'component.scrollbarY': import("@antv/g2").GuideComponentComponent<import("@antv/g2/lib/component/scrollbar").ScrollbarOptions>;
    'animation.scaleInX': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.scaleOutX': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.scaleInY': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.scaleOutY': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.waveIn': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.fadeIn': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.fadeOut': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.zoomIn': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.zoomOut': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.pathIn': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.morphing': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation").MorphingOptions>;
    'animation.growInX': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'animation.growInY': import("@antv/g2/lib/runtime").AnimationComponent<import("@antv/g2/lib/animation/types").Animation>;
    'interaction.elementHighlight': typeof import("@antv/g2/lib/interaction").ElementHighlight;
    'interaction.elementHighlightByX': typeof import("@antv/g2/lib/interaction").ElementHighlightByX;
    'interaction.elementHighlightByColor': typeof import("@antv/g2/lib/interaction").ElementHighlightByColor;
    'interaction.elementSelect': typeof import("@antv/g2/lib/interaction").ElementSelect;
    'interaction.elementSelectByX': typeof import("@antv/g2/lib/interaction").ElementSelectByX;
    'interaction.elementSelectByColor': typeof import("@antv/g2/lib/interaction").ElementSelectByColor;
    'interaction.fisheye': typeof import("@antv/g2/lib/interaction").Fisheye;
    'interaction.chartIndex': typeof import("@antv/g2/lib/interaction").ChartIndex;
    'interaction.tooltip': typeof import("@antv/g2/lib/interaction").Tooltip;
    'interaction.legendFilter': typeof import("@antv/g2/lib/interaction").LegendFilter;
    'interaction.legendHighlight': typeof import("@antv/g2/lib/interaction").LegendHighlight;
    'interaction.brushHighlight': typeof import("@antv/g2/lib/interaction").BrushHighlight;
    'interaction.brushXHighlight': typeof import("@antv/g2/lib/interaction").BrushXHighlight;
    'interaction.brushYHighlight': typeof import("@antv/g2/lib/interaction").BrushYHighlight;
    'interaction.brushAxisHighlight': typeof import("@antv/g2/lib/interaction").BrushAxisHighlight;
    'interaction.brushFilter': typeof import("@antv/g2/lib/interaction").BrushFilter;
    'interaction.brushXFilter': typeof import("@antv/g2/lib/interaction").BrushXFilter;
    'interaction.brushYFilter': typeof import("@antv/g2/lib/interaction").BrushYFilter;
    'interaction.sliderFilter': typeof import("@antv/g2/lib/interaction").SliderFilter;
    'interaction.scrollbarFilter': typeof import("@antv/g2/lib/interaction").ScrollbarFilter;
    'interaction.poptip': typeof import("@antv/g2/lib/interaction").Poptip;
    'interaction.treemapDrillDown': typeof import("@antv/g2/lib/interaction").TreemapDrillDown;
    'interaction.elementPointMove': typeof import("@antv/g2/lib/interaction").ElementPointMove;
    'composition.spaceLayer': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition").SpaceLayerOptions>;
    'composition.spaceFlex': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition").SpaceFlexOptions>;
    'composition.facetRect': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition").FacetRectOptions>;
    'composition.repeatMatrix': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2").RepeatMatrixComposition>;
    'composition.facetCircle': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2").FacetCircleComposition>;
    'composition.timingKeyframe': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition").TimingKeyframeOptions>;
    'labelTransform.overlapHide': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").OverlapHideOptions>;
    'labelTransform.overlapDodgeY': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").OverlapDodgeYOptions>;
    'labelTransform.overflowHide': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").OverflowHideOptions>;
    'labelTransform.contrastReverse': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").ContrastReverseOptions>;
    'labelTransform.overflowStroke': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").OverflowStrokeOptions>;
    'labelTransform.exceedAdjust': import("@antv/g2/lib/runtime").LabelTransformComponent<import("@antv/g2/lib/label-transform").ExceedAdjustOptions>;
    'data.venn': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").VennOptions>;
    'mark.boxplot': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark/boxplot").BoxPlotOptions>;
    'mark.gauge': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").GaugeOptions>;
    'mark.liquid': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").LiquidOptions>;
    'data.arc': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").ArcOptions>;
    'data.cluster': import("@antv/g2/lib/runtime").DataComponent<import("@antv/g2/lib/data").ClusterOptions>;
    'mark.forceGraph': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").ForceGraphOptions>;
    'mark.tree': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").TreeOptions>;
    'mark.pack': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/mark").PackOptions>;
    'mark.sankey': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").SankeyOptions>;
    'mark.chord': import("@antv/g2/lib/runtime").CompositeMarkComponent<import("@antv/g2/lib/mark").ChordOptions>;
    'mark.treemap': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/mark").TreemapOptions>;
    'composition.geoView': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition/geoView").GeoViewOptions>;
    'composition.geoPath': import("@antv/g2/lib/runtime").CompositionComponent<import("@antv/g2/lib/composition/geoPath").GeoPathOptions>;
}>;
