#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单数据库合并脚本
"""

import os
import sqlite3
import shutil
from datetime import datetime

def main():
    print("🔧 开始数据库合并...")
    
    # 1. 备份
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/db_merge_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    if os.path.exists("fucai3d.db"):
        shutil.copy2("fucai3d.db", f"{backup_dir}/fucai3d_root.db")
        print(f"✅ 备份根目录数据库")
    
    if os.path.exists("data/fucai3d.db"):
        shutil.copy2("data/fucai3d.db", f"{backup_dir}/fucai3d_data.db")
        print(f"✅ 备份data目录数据库")
    
    # 2. 分析数据库
    print("\n📊 分析数据库...")
    
    # 检查根目录数据库
    if os.path.exists("fucai3d.db"):
        root_size = os.path.getsize("fucai3d.db")
        print(f"根目录 fucai3d.db: {root_size:,} 字节")
        
        try:
            conn = sqlite3.connect("fucai3d.db")
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            root_count = cursor.fetchone()[0]
            print(f"  lottery_data记录数: {root_count}")
            conn.close()
        except:
            root_count = 0
            print("  无法读取lottery_data表")
    else:
        root_size = 0
        root_count = 0
        print("根目录 fucai3d.db: 不存在")
    
    # 检查data目录数据库
    if os.path.exists("data/fucai3d.db"):
        data_size = os.path.getsize("data/fucai3d.db")
        print(f"data/fucai3d.db: {data_size:,} 字节")
        
        try:
            conn = sqlite3.connect("data/fucai3d.db")
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM lottery_data")
            data_count = cursor.fetchone()[0]
            print(f"  lottery_data记录数: {data_count}")
            conn.close()
        except:
            data_count = 0
            print("  无法读取lottery_data表")
    else:
        data_size = 0
        data_count = 0
        print("data/fucai3d.db: 不存在")
    
    # 3. 决定合并策略
    print("\n🔄 执行合并...")
    
    if root_size == 0:
        print("✅ 根目录数据库不存在或为空，无需合并")
    elif data_size == 0:
        print("📁 data目录数据库不存在，移动根目录数据库")
        shutil.move("fucai3d.db", "data/fucai3d.db")
        print("✅ 已移动: fucai3d.db -> data/fucai3d.db")
    elif data_count >= root_count:
        print(f"✅ data目录数据库更完整 ({data_count} >= {root_count})，删除根目录版本")
        os.remove("fucai3d.db")
        print("🗑️ 已删除根目录的 fucai3d.db")
    else:
        print(f"⚠️ 根目录数据库更完整 ({root_count} > {data_count})，需要手动处理")
        return False
    
    # 4. 更新同步配置
    print("\n⚙️ 更新同步配置...")
    config_path = "src/sync/config.yaml"
    
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份配置
        backup_config = f"{config_path}.backup_{timestamp}"
        with open(backup_config, 'w', encoding='utf-8') as f:
            f.write(content)
        
        # 移除根目录数据库配置
        lines = content.split('\n')
        new_lines = []
        skip_lines = 0
        
        for line in lines:
            if skip_lines > 0:
                skip_lines -= 1
                continue
                
            if 'path: "fucai3d.db"' in line:
                skip_lines = 1  # 跳过下一行description
                continue
            elif 'description: "根目录预测数据库"' in line:
                continue
                
            new_lines.append(line)
        
        # 写入新配置
        with open(config_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_lines))
        
        print("✅ 已更新同步配置")
    
    # 5. 验证
    print("\n🔍 验证结果...")
    
    if os.path.exists("fucai3d.db"):
        print("❌ 根目录仍存在 fucai3d.db")
        return False
    
    if not os.path.exists("data/fucai3d.db"):
        print("❌ data/fucai3d.db 不存在")
        return False
    
    print("✅ 验证通过")
    print(f"\n🎉 数据库合并完成！")
    print(f"📦 备份位置: {backup_dir}")
    print("📊 现在只有一个从数据库: data/fucai3d.db")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("❌ 合并失败，请检查备份文件")
