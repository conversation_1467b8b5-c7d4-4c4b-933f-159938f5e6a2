#!/usr/bin/env python3
"""
XGBoost十位预测模型

基于XGBoost实现的十位数字预测模型，专注于独立位置预测。

特点：
- 高性能梯度提升算法
- 支持多分类概率输出
- 内置特征重要性分析
- 支持早停和交叉验证

Author: Augment Code AI Assistant
Date: 2025-01-14
"""

import sys
from pathlib import Path
import numpy as np
import logging
import pickle
import time
from typing import Dict, Tuple, Optional, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    import xgboost as xgb
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
    from sklearn.preprocessing import LabelEncoder
except ImportError as e:
    print(f"警告: 缺少必要的机器学习库: {e}")
    print("请安装: pip install xgboost scikit-learn")

# 导入基类
try:
    from src.predictors.base_independent_predictor import BaseIndependentPredictor
    from config import get_config
except ImportError as e:
    print(f"警告: 无法导入基类或配置: {e}")

class XGBTensModel(BaseIndependentPredictor):
    """XGBoost十位预测模型"""

    def __init__(self, db_path: str):
        """
        初始化XGBoost十位预测模型

        Args:
            db_path: 数据库路径
        """
        super().__init__("tens", db_path)
        
        # 加载XGBoost配置
        self._load_xgb_config()
        
        # 初始化模型相关属性
        self.xgb_model = None
        self.label_encoder = LabelEncoder()
        self.feature_importance_ = None
        self.training_metrics = {}
        
        self.logger.info("XGBoost十位预测模型初始化完成")
    
    def _load_xgb_config(self):
        """加载XGBoost配置"""
        try:
            config_loader = get_config()
            self.xgb_config = config_loader.get_model_config('xgboost')
        except Exception as e:
            self.logger.warning(f"配置加载失败，使用默认XGBoost配置: {e}")
            self.xgb_config = {
                'objective': 'multi:softprob',
                'num_class': 10,
                'max_depth': 6,
                'learning_rate': 0.1,
                'n_estimators': 200,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_alpha': 0.1,
                'reg_lambda': 1.0,
                'random_state': 42,
                'n_jobs': -1,
                'early_stopping_rounds': 20,
                'eval_metric': 'mlogloss'
            }
    
    def build_model(self) -> xgb.XGBClassifier:
        """
        构建XGBoost模型
        
        Returns:
            XGBoost分类器
        """
        try:
            # 创建XGBoost分类器
            self.xgb_model = xgb.XGBClassifier(
                objective=self.xgb_config['objective'],
                n_estimators=self.xgb_config['n_estimators'],
                max_depth=self.xgb_config['max_depth'],
                learning_rate=self.xgb_config['learning_rate'],
                subsample=self.xgb_config['subsample'],
                colsample_bytree=self.xgb_config['colsample_bytree'],
                reg_alpha=self.xgb_config['reg_alpha'],
                reg_lambda=self.xgb_config['reg_lambda'],
                random_state=self.xgb_config['random_state'],
                n_jobs=self.xgb_config['n_jobs'],
                eval_metric=self.xgb_config['eval_metric']
            )
            
            self.logger.info("XGBoost模型构建完成")
            return self.xgb_model
            
        except Exception as e:
            self.logger.error(f"XGBoost模型构建失败: {e}")
            raise
    
    def train(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """
        训练XGBoost模型
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            训练结果字典
        """
        try:
            start_time = time.time()
            self.logger.info(f"开始训练XGBoost模型，数据形状: X{X.shape}, y{y.shape}")
            
            # 构建模型
            if self.xgb_model is None:
                self.build_model()
            
            # 数据预处理
            X_processed, y_processed = self._preprocess_data(X, y)
            
            # 分割训练和验证集
            X_train, X_val, y_train, y_val = train_test_split(
                X_processed, y_processed, 
                test_size=self.data_config.get('validation_split', 0.2),
                random_state=42,
                stratify=y_processed
            )
            
            # 训练模型
            eval_set = [(X_train, y_train), (X_val, y_val)]
            
            self.xgb_model.fit(
                X_train, y_train,
                eval_set=eval_set,
                early_stopping_rounds=self.xgb_config.get('early_stopping_rounds', 20),
                verbose=False
            )
            
            # 计算训练指标
            train_pred = self.xgb_model.predict(X_train)
            val_pred = self.xgb_model.predict(X_val)
            
            train_accuracy = accuracy_score(y_train, train_pred)
            val_accuracy = accuracy_score(y_val, val_pred)
            
            # 获取特征重要性
            self.feature_importance_ = self.xgb_model.feature_importances_
            
            training_time = time.time() - start_time
            
            # 保存训练指标
            self.training_metrics = {
                'train_accuracy': train_accuracy,
                'val_accuracy': val_accuracy,
                'training_time': training_time,
                'n_estimators_used': self.xgb_model.n_estimators,
                'best_iteration': getattr(self.xgb_model, 'best_iteration', None),
                'feature_importance': self.feature_importance_.tolist() if self.feature_importance_ is not None else []
            }
            
            self.is_trained = True
            self.training_history['xgb'] = self.training_metrics
            
            self.logger.info(f"XGBoost训练完成: 训练准确率={train_accuracy:.4f}, 验证准确率={val_accuracy:.4f}, 耗时={training_time:.2f}s")
            
            return self.training_metrics
            
        except Exception as e:
            self.logger.error(f"XGBoost训练失败: {e}")
            raise
    
    def predict_probability(self, X: np.ndarray) -> np.ndarray:
        """
        预测概率分布
        
        Args:
            X: 特征矩阵
            
        Returns:
            概率分布数组，shape: (n_samples, 10)
        """
        if not self.is_trained or self.xgb_model is None:
            raise ValueError("模型尚未训练")
        
        try:
            # 数据预处理
            X_processed = self._preprocess_features(X)
            
            # 预测概率
            probabilities = self.xgb_model.predict_proba(X_processed)
            
            return probabilities
            
        except Exception as e:
            self.logger.error(f"XGBoost预测失败: {e}")
            raise
    
    def _preprocess_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """
        数据预处理
        
        Args:
            X: 特征矩阵
            y: 目标向量
            
        Returns:
            预处理后的特征矩阵和目标向量
        """
        # 处理缺失值
        X_processed = np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
        
        # 标签编码（确保标签是0-9的整数）
        y_processed = self.label_encoder.fit_transform(y)
        
        return X_processed, y_processed
    
    def _preprocess_features(self, X: np.ndarray) -> np.ndarray:
        """
        特征预处理（仅特征）
        
        Args:
            X: 特征矩阵
            
        Returns:
            预处理后的特征矩阵
        """
        return np.nan_to_num(X, nan=0.0, posinf=0.0, neginf=0.0)
    
    def get_feature_importance(self, top_k: Optional[int] = None) -> Dict[str, float]:
        """
        获取特征重要性
        
        Args:
            top_k: 返回前k个重要特征，None返回全部
            
        Returns:
            特征重要性字典
        """
        if self.feature_importance_ is None:
            return {}
        
        if not self.feature_names:
            feature_names = [f"feature_{i}" for i in range(len(self.feature_importance_))]
        else:
            feature_names = self.feature_names
        
        importance_dict = dict(zip(feature_names, self.feature_importance_))
        
        # 按重要性排序
        sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
        
        if top_k:
            sorted_importance = sorted_importance[:top_k]
        
        return dict(sorted_importance)

    def evaluate_model(self, X_test: np.ndarray, y_test: np.ndarray) -> Dict[str, Any]:
        """
        评估模型性能

        Args:
            X_test: 测试特征矩阵
            y_test: 测试目标向量

        Returns:
            评估结果字典
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练")

        try:
            # 预测
            y_pred = self.xgb_model.predict(X_test)
            y_pred_proba = self.predict_probability(X_test)

            # 计算指标
            accuracy = accuracy_score(y_test, y_pred)

            # Top3准确率
            top3_accuracy = self._calculate_top3_accuracy(y_test, y_pred_proba)

            # 平均置信度
            max_probabilities = np.max(y_pred_proba, axis=1)
            avg_confidence = np.mean(max_probabilities)

            # 分类报告
            class_report = classification_report(y_test, y_pred, output_dict=True)

            # 混淆矩阵
            conf_matrix = confusion_matrix(y_test, y_pred)

            evaluation_results = {
                'accuracy': accuracy,
                'top3_accuracy': top3_accuracy,
                'avg_confidence': avg_confidence,
                'classification_report': class_report,
                'confusion_matrix': conf_matrix.tolist(),
                'feature_importance': self.get_feature_importance(top_k=20)
            }

            self.logger.info(f"模型评估完成: 准确率={accuracy:.4f}, Top3准确率={top3_accuracy:.4f}")

            return evaluation_results

        except Exception as e:
            self.logger.error(f"模型评估失败: {e}")
            raise

    def _calculate_top3_accuracy(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> float:
        """计算Top3准确率"""
        top3_predictions = np.argsort(y_pred_proba, axis=1)[:, -3:]
        correct = 0

        for i, true_label in enumerate(y_true):
            if true_label in top3_predictions[i]:
                correct += 1

        return correct / len(y_true)

    def save_model(self, filepath: Optional[str] = None) -> str:
        """
        保存XGBoost模型
        
        Args:
            filepath: 保存路径
            
        Returns:
            实际保存路径
        """
        if not self.is_trained:
            raise ValueError("模型尚未训练，无法保存")
        
        if filepath is None:
            model_dir = Path(self.training_config.get('model_save_path', 'models/tens/'))
            model_dir.mkdir(parents=True, exist_ok=True)
            filepath = model_dir / "xgb_tens_model.pkl"
        
        try:
            # 保存模型和相关信息
            model_data = {
                'model': self.xgb_model,
                'label_encoder': self.label_encoder,
                'feature_names': self.feature_names,
                'feature_importance': self.feature_importance_,
                'training_metrics': self.training_metrics,
                'config': self.xgb_config
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            
            self.logger.info(f"XGBoost模型保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"XGBoost模型保存失败: {e}")
            raise
    
    def load_model(self, filepath: str) -> bool:
        """
        加载XGBoost模型
        
        Args:
            filepath: 模型文件路径
            
        Returns:
            是否加载成功
        """
        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)
            
            self.xgb_model = model_data['model']
            self.label_encoder = model_data['label_encoder']
            self.feature_names = model_data['feature_names']
            self.feature_importance_ = model_data['feature_importance']
            self.training_metrics = model_data['training_metrics']
            
            self.is_trained = True
            
            self.logger.info(f"XGBoost模型加载成功: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"XGBoost模型加载失败: {e}")
            return False
