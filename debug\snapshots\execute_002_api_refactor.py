#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API层动态期号获取重构

修改 src/web/routes/prediction.py 中的硬编码问题，实现动态期号获取。

执行ID: execute_002
创建时间: 2025-08-12 03:35:00
"""

import sqlite3
import os
from datetime import datetime

def get_latest_drawn_issue(conn):
    """动态获取最新已开奖期号"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT issue, hundreds, tens, units, draw_date
        FROM lottery_data 
        ORDER BY issue DESC 
        LIMIT 1
    """)
    result = cursor.fetchone()
    
    if result:
        return {
            'issue': result[0],
            'numbers': f"{result[1]}{result[2]}{result[3]}",
            'draw_date': result[4],
            'status': 'drawn'
        }
    return None

def calculate_next_issue(current_issue: str) -> str:
    """智能计算下一期期号"""
    try:
        # 解析期号格式 YYYYNNN
        year = int(current_issue[:4])
        number = int(current_issue[4:])
        
        # 简单递增
        next_number = number + 1
        
        # 处理年份切换
        if next_number > 365:
            year += 1
            next_number = 1
            
        return f"{year}{next_number:03d}"
    except:
        return str(int(current_issue) + 1)

def get_predictions_by_issue(conn, issue: str):
    """获取指定期号的预测数据"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT hundreds, tens, units, combined_probability, confidence_level
        FROM final_predictions
        WHERE issue = ?
        ORDER BY combined_probability DESC
        LIMIT 5
    """, (issue,))
    
    results = cursor.fetchall()
    predictions_list = []
    
    for result in results:
        predictions_list.append({
            'hundreds': result[0],
            'tens': result[1], 
            'units': result[2],
            'combined_probability': result[3],
            'confidence_level': result[4] if result[4] else '未知'
        })
    
    return predictions_list

def build_dashboard_response(latest_drawn, next_issue, next_predictions):
    """构建仪表盘响应数据"""
    current_data = {
        "issue": next_issue,
        "status": "predicting" if next_predictions else "ready"
    }
    
    if next_predictions:
        current_data["predictions"] = next_predictions
    
    return {
        "status": "success",
        "data": {
            "lastDrawn": latest_drawn,
            "current": current_data,
            "updateTime": datetime.now().strftime('%H:%M:%S')
        }
    }

def test_dynamic_api():
    """测试动态API功能"""
    print("🔧 测试动态API功能")
    print("=" * 40)
    
    try:
        # 连接数据库
        DB_PATH = "data/fucai3d.db"
        if not os.path.exists(DB_PATH):
            print(f"❌ 数据库文件不存在: {DB_PATH}")
            return False
        
        conn = sqlite3.connect(DB_PATH)
        
        # 1. 测试动态获取最新期号
        print("📊 测试动态获取最新期号:")
        latest_drawn = get_latest_drawn_issue(conn)
        if latest_drawn:
            print(f"  ✅ 最新开奖期号: {latest_drawn['issue']}")
            print(f"  ✅ 开奖号码: {latest_drawn['numbers']}")
            print(f"  ✅ 开奖日期: {latest_drawn['draw_date']}")
        else:
            print("  ❌ 无法获取最新期号")
            return False
        
        # 2. 测试期号计算
        print(f"\n🔢 测试期号计算:")
        next_issue = calculate_next_issue(latest_drawn['issue'])
        print(f"  ✅ 下一期期号: {next_issue}")
        
        # 3. 测试预测数据获取
        print(f"\n🎯 测试预测数据获取:")
        predictions = get_predictions_by_issue(conn, next_issue)
        print(f"  ✅ 预测数据条数: {len(predictions)}")
        
        if predictions:
            print("  前3个预测:")
            for i, pred in enumerate(predictions[:3], 1):
                h, t, u = pred['hundreds'], pred['tens'], pred['units']
                prob = pred['combined_probability']
                print(f"    {i}. {h}{t}{u} (概率: {prob:.1f}%)")
        
        # 4. 测试完整响应构建
        print(f"\n📋 测试完整响应构建:")
        response = build_dashboard_response(latest_drawn, next_issue, predictions)
        print(f"  ✅ 响应状态: {response['status']}")
        print(f"  ✅ 已开奖期号: {response['data']['lastDrawn']['issue']}")
        print(f"  ✅ 待预测期号: {response['data']['current']['issue']}")
        print(f"  ✅ 更新时间: {response['data']['updateTime']}")
        
        conn.close()
        
        # 5. 验证期号是否正确
        if latest_drawn['issue'] >= '2025213' and next_issue >= '2025214':
            print(f"\n🎉 动态API测试通过！")
            print(f"  ✅ 最新开奖: {latest_drawn['issue']}期 {latest_drawn['numbers']}")
            print(f"  ✅ 待预测: {next_issue}期")
            return True
        else:
            print(f"\n⚠️ 期号验证失败")
            print(f"  期望最新期号 >= 2025213，实际: {latest_drawn['issue']}")
            print(f"  期望预测期号 >= 2025214，实际: {next_issue}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_api_patch():
    """生成API修复补丁"""
    print("\n🔧 生成API修复补丁")
    print("=" * 40)
    
    # 新的API函数代码
    new_api_code = '''
def get_latest_drawn_issue(conn):
    """动态获取最新已开奖期号"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT issue, hundreds, tens, units, draw_date
        FROM lottery_data 
        ORDER BY issue DESC 
        LIMIT 1
    """)
    result = cursor.fetchone()
    
    if result:
        return {
            'issue': result[0],
            'numbers': f"{result[1]}{result[2]}{result[3]}",
            'draw_date': result[4],
            'status': 'drawn'
        }
    return None

def calculate_next_issue(current_issue: str) -> str:
    """智能计算下一期期号"""
    try:
        # 解析期号格式 YYYYNNN
        year = int(current_issue[:4])
        number = int(current_issue[4:])
        
        # 简单递增
        next_number = number + 1
        
        # 处理年份切换
        if next_number > 365:
            year += 1
            next_number = 1
            
        return f"{year}{next_number:03d}"
    except:
        return str(int(current_issue) + 1)

def get_predictions_by_issue(conn, issue: str):
    """获取指定期号的预测数据"""
    cursor = conn.cursor()
    cursor.execute("""
        SELECT hundreds, tens, units, combined_probability, confidence_level
        FROM final_predictions
        WHERE issue = ?
        ORDER BY combined_probability DESC
        LIMIT 5
    """, (issue,))
    
    results = cursor.fetchall()
    predictions_list = []
    
    for result in results:
        predictions_list.append({
            'hundreds': result[0],
            'tens': result[1], 
            'units': result[2],
            'combined_probability': result[3],
            'confidence_level': result[4] if result[4] else '未知'
        })
    
    return predictions_list

def build_dashboard_response(latest_drawn, next_issue, next_predictions):
    """构建仪表盘响应数据"""
    current_data = {
        "issue": next_issue,
        "status": "predicting" if next_predictions else "ready"
    }
    
    if next_predictions:
        current_data["predictions"] = next_predictions
    
    return {
        "status": "success",
        "data": {
            "lastDrawn": latest_drawn,
            "current": current_data,
            "updateTime": datetime.now().strftime('%H:%M:%S')
        }
    }
'''
    
    # 保存补丁文件
    patch_file = "debug/snapshots/execute_002_api_patch.py"
    with open(patch_file, 'w', encoding='utf-8') as f:
        f.write(f"# API修复补丁 - 动态期号获取\n")
        f.write(f"# 生成时间: {datetime.now().isoformat()}\n\n")
        f.write(new_api_code)
    
    print(f"✅ API修复补丁已生成: {patch_file}")
    return patch_file

def main():
    """主函数"""
    print("🔧 API层动态期号获取重构工具")
    print("=" * 50)
    
    try:
        # 1. 测试动态API功能
        if test_dynamic_api():
            print("\n✅ 动态API功能测试通过")
        else:
            print("\n❌ 动态API功能测试失败")
            return False
        
        # 2. 生成API修复补丁
        patch_file = generate_api_patch()
        
        print(f"\n📋 重构准备完成:")
        print(f"  ✅ 动态期号获取功能已验证")
        print(f"  ✅ API修复补丁已生成")
        print(f"  📁 补丁文件: {patch_file}")
        
        print(f"\n🔄 下一步: 应用补丁到 src/web/routes/prediction.py")
        return True
        
    except Exception as e:
        print(f"❌ 重构准备失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
