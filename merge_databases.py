#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库合并脚本

将根目录的 fucai3d.db 合并到 data/fucai3d.db，
统一数据库架构，简化同步系统。

作者: Augment Code AI Assistant
创建时间: 2025-08-12
"""

import os
import sqlite3
import shutil
from datetime import datetime
from typing import Dict, List, Any

def backup_databases():
    """备份现有数据库"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_dir = f"backups/database_merge_{timestamp}"
    os.makedirs(backup_dir, exist_ok=True)
    
    print("📦 备份现有数据库...")
    
    # 备份根目录数据库
    if os.path.exists("fucai3d.db"):
        shutil.copy2("fucai3d.db", f"{backup_dir}/fucai3d_root.db")
        print(f"✅ 已备份: fucai3d.db -> {backup_dir}/fucai3d_root.db")
    
    # 备份data目录数据库
    if os.path.exists("data/fucai3d.db"):
        shutil.copy2("data/fucai3d.db", f"{backup_dir}/fucai3d_data.db")
        print(f"✅ 已备份: data/fucai3d.db -> {backup_dir}/fucai3d_data.db")
    
    return backup_dir

def analyze_database(db_path: str) -> Dict[str, Any]:
    """分析数据库内容"""
    if not os.path.exists(db_path):
        return {"exists": False}
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表列表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        
        analysis = {
            "exists": True,
            "size": os.path.getsize(db_path),
            "tables": tables,
            "table_counts": {}
        }
        
        # 获取每个表的记录数
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                analysis["table_counts"][table] = count
            except Exception as e:
                analysis["table_counts"][table] = f"Error: {e}"
        
        # 特别检查关键表
        if "lottery_data" in tables:
            cursor.execute("SELECT MIN(issue), MAX(issue) FROM lottery_data")
            min_issue, max_issue = cursor.fetchone()
            analysis["lottery_data_range"] = f"{min_issue} - {max_issue}"
        
        if "final_predictions" in tables:
            cursor.execute("SELECT DISTINCT issue FROM final_predictions ORDER BY issue DESC LIMIT 3")
            issues = [row[0] for row in cursor.fetchall()]
            analysis["prediction_issues"] = issues
        
        conn.close()
        return analysis
        
    except Exception as e:
        return {"exists": True, "error": str(e)}

def merge_databases():
    """合并数据库"""
    print("\n🔄 开始数据库合并...")
    
    # 分析两个数据库
    root_db_analysis = analyze_database("fucai3d.db")
    data_db_analysis = analyze_database("data/fucai3d.db")
    
    print("\n📊 数据库分析结果:")
    print(f"根目录 fucai3d.db:")
    if root_db_analysis["exists"]:
        print(f"  大小: {root_db_analysis['size']:,} 字节")
        print(f"  表数量: {len(root_db_analysis.get('tables', []))}")
        print(f"  表: {root_db_analysis.get('tables', [])}")
        for table, count in root_db_analysis.get('table_counts', {}).items():
            print(f"    {table}: {count} 条记录")
    else:
        print("  不存在")
    
    print(f"\ndata/fucai3d.db:")
    if data_db_analysis["exists"]:
        print(f"  大小: {data_db_analysis['size']:,} 字节")
        print(f"  表数量: {len(data_db_analysis.get('tables', []))}")
        print(f"  表: {data_db_analysis.get('tables', [])}")
        for table, count in data_db_analysis.get('table_counts', {}).items():
            print(f"    {table}: {count} 条记录")
    else:
        print("  不存在")
    
    # 决定合并策略
    if not root_db_analysis["exists"]:
        print("\n✅ 根目录数据库不存在，无需合并")
        return True
    
    if not data_db_analysis["exists"]:
        print("\n📁 data目录数据库不存在，直接移动根目录数据库")
        shutil.move("fucai3d.db", "data/fucai3d.db")
        print("✅ 已移动: fucai3d.db -> data/fucai3d.db")
        return True
    
    # 两个数据库都存在，需要合并
    print("\n🔀 两个数据库都存在，开始合并...")
    
    # 比较数据库内容
    root_tables = set(root_db_analysis.get('tables', []))
    data_tables = set(data_db_analysis.get('tables', []))
    
    if root_tables == data_tables:
        # 表结构相同，比较数据
        print("📋 表结构相同，比较数据内容...")
        
        # 简单策略：使用更新的数据库（通常是data目录的）
        root_size = root_db_analysis.get('size', 0)
        data_size = data_db_analysis.get('size', 0)
        
        if data_size >= root_size:
            print(f"✅ data/fucai3d.db 更大或相等 ({data_size} >= {root_size})，保留data目录版本")
            os.remove("fucai3d.db")
            print("🗑️ 已删除根目录的 fucai3d.db")
        else:
            print(f"⚠️ 根目录版本更大 ({root_size} > {data_size})，需要手动检查")
            return False
    else:
        print("⚠️ 表结构不同，需要手动合并")
        print(f"根目录独有表: {root_tables - data_tables}")
        print(f"data目录独有表: {data_tables - root_tables}")
        return False
    
    return True

def update_sync_config():
    """更新同步系统配置"""
    print("\n⚙️ 更新同步系统配置...")
    
    config_path = "src/sync/config.yaml"
    if not os.path.exists(config_path):
        print("❌ 同步配置文件不存在")
        return False
    
    # 读取配置文件
    with open(config_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原配置
    backup_path = f"{config_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📦 已备份配置: {backup_path}")
    
    # 更新配置：移除根目录数据库
    lines = content.split('\n')
    new_lines = []
    skip_next = False
    
    for line in lines:
        if skip_next:
            skip_next = False
            continue
        
        if 'path: "fucai3d.db"' in line:
            # 跳过这个从数据库配置
            skip_next = True  # 跳过下一行的description
            continue
        elif 'description: "根目录预测数据库"' in line:
            continue
        
        new_lines.append(line)
    
    # 写入新配置
    new_content = '\n'.join(new_lines)
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ 已更新同步配置，移除根目录数据库")
    return True

def verify_system():
    """验证系统状态"""
    print("\n🔍 验证系统状态...")
    
    # 检查数据库文件
    if os.path.exists("fucai3d.db"):
        print("❌ 根目录仍存在 fucai3d.db")
        return False
    
    if not os.path.exists("data/fucai3d.db"):
        print("❌ data/fucai3d.db 不存在")
        return False
    
    # 检查数据库内容
    try:
        conn = sqlite3.connect("data/fucai3d.db")
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        
        print(f"✅ data/fucai3d.db 包含 {len(tables)} 个表: {tables}")
        
    except Exception as e:
        print(f"❌ 数据库验证失败: {e}")
        return False
    
    print("✅ 系统验证通过")
    return True

def main():
    """主函数"""
    print("🔧 福彩3D数据库合并工具")
    print("=" * 50)
    
    try:
        # 1. 备份数据库
        backup_dir = backup_databases()
        
        # 2. 合并数据库
        if not merge_databases():
            print("❌ 数据库合并失败，请手动处理")
            return False
        
        # 3. 更新配置
        if not update_sync_config():
            print("❌ 配置更新失败")
            return False
        
        # 4. 验证系统
        if not verify_system():
            print("❌ 系统验证失败")
            return False
        
        print("\n🎉 数据库合并完成！")
        print(f"📦 备份位置: {backup_dir}")
        print("📊 现在只有一个从数据库: data/fucai3d.db")
        print("⚙️ 同步配置已更新")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并过程失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
