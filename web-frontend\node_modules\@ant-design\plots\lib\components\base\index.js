"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseChart = void 0;
var react_1 = __importStar(require("react"));
var charts_util_1 = require("@ant-design/charts-util");
var useChart_1 = __importDefault(require("../../hooks/useChart"));
var core_1 = require("../../core");
exports.BaseChart = (0, react_1.forwardRef)(function (_a, ref) {
    var _b = _a.chartType, chartType = _b === void 0 ? 'Base' : _b, config = __rest(_a, ["chartType"]);
    var _c = config.containerStyle, containerStyle = _c === void 0 ? {
        height: 'inherit',
        flex: 1
    } : _c, _d = config.containerAttributes, containerAttributes = _d === void 0 ? {} : _d, className = config.className, loading = config.loading, loadingTemplate = config.loadingTemplate, errorTemplate = config.errorTemplate, onReady = config.onReady, rest = __rest(config, ["containerStyle", "containerAttributes", "className", "loading", "loadingTemplate", "errorTemplate", "onReady"]);
    var _e = (0, useChart_1.default)(core_1.Plots[chartType], __assign(__assign({}, rest), { onReady: function (chartInstance) {
            if (ref) {
                if (typeof ref === 'function') {
                    ref(chartInstance);
                }
                else {
                    ref.current = chartInstance;
                }
            }
            onReady === null || onReady === void 0 ? void 0 : onReady(chartInstance);
        } })), chart = _e.chart, container = _e.container;
    return (react_1.default.createElement(charts_util_1.ErrorBoundary, { errorTemplate: errorTemplate },
        loading && react_1.default.createElement(charts_util_1.ChartLoading, { loadingTemplate: loadingTemplate, theme: config.theme, loading: loading }),
        react_1.default.createElement("div", __assign({ className: className, style: containerStyle, ref: container }, containerAttributes))));
});
