/**
 * 计算文本在画布中的相关信息（例如它的宽度）
 * @see https://developer.mozilla.org/zh-CN/docs/Web/API/CanvasRenderingContext2D/measureText
 */
export declare const measureText: any;
/**
 * 计算文本在画布中的宽度
 * @param text 文本
 * @param font 字体
 */
export declare const measureTextWidth: (text: string, font?: any) => number;
/**
 * 计算文本在画布中的实际高度
 * @param text 文本
 * @param font 字体
 */
export declare const measureTextHeight: (text: string, font?: any) => number;
