"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidElement = void 0;
var isValidElement = function (jsxCode) {
    var basicReactPatterns = [/\breact\b/i, /\.jsx/, /children:\s*\[/, /\*#__PURE__\*/];
    var createElementPattern = /(\w+)?\.createElement\(\s*(['"`])([^'"`]+)\2/g;
    // G 元素集合
    var GElements = new Set([
        'g',
        'circle',
        'ellipse',
        'image',
        'rect',
        'line',
        'polyline',
        'polygon',
        'text',
        'path',
        'html',
        'mesh',
    ]);
    if (basicReactPatterns.some(function (pattern) { return pattern.test(jsxCode); })) {
        return true;
    }
    var matches = jsxCode.match(createElementPattern);
    if (!matches) {
        return false;
    }
    return matches.some(function (match) {
        var elementMatch = match.match(/\.createElement\(\s*(['"`])([^'"`]+)\1/);
        return elementMatch && !GElements.has(elementMatch[2].toLowerCase());
    });
};
exports.isValidElement = isValidElement;
