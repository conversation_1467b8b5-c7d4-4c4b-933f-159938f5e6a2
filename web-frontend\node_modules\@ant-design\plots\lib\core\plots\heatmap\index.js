"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        if (typeof b !== "function" && b !== null)
            throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Heatmap = void 0;
var base_1 = require("../../base");
var adaptor_1 = require("./adaptor");
var Heatmap = /** @class */ (function (_super) {
    __extends(Heatmap, _super);
    function Heatmap() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        /** 图表类型 */
        _this.type = 'heatmap';
        return _this;
    }
    Heatmap.getDefaultOptions = function () {
        return {
            type: 'view',
            legend: null,
            tooltip: {
                valueFormatter: '~s',
            },
            axis: {
                y: {
                    title: null,
                    grid: true,
                },
                x: {
                    title: null,
                    grid: true,
                },
            },
            children: [
                {
                    type: 'point',
                },
            ],
        };
    };
    Heatmap.prototype.getDefaultOptions = function () {
        return Heatmap.getDefaultOptions();
    };
    Heatmap.prototype.getSchemaAdaptor = function () {
        return adaptor_1.adaptor;
    };
    return Heatmap;
}(base_1.Plot));
exports.Heatmap = Heatmap;
