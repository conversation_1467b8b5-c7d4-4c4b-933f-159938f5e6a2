{"version": 3, "file": "coordinate.js", "sourceRoot": "src/", "sources": ["coordinate.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mCAA+C;AAC/C,uCAAuC;AAEvC,iCAAoD;AACpD,2CAoBsB;AAEtB;IAuCE;;;OAGG;IACH,oBAAY,OAA0B;QApCtC,QAAQ;QACA,YAAO,GAAY;YACzB,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,YAAY;QACJ,iBAAY,GAAG;YACrB,SAAS,wBAAA;YACT,SAAS,wBAAA;YACT,MAAM,qBAAA;YACN,MAAM,qBAAA;YACN,KAAK,oBAAA;YACL,SAAS,wBAAA;YACT,KAAK,oBAAA;YAC<PERSON>,SAAS,EAAE,mBAAM;YACjB,SAAS,EAAE,mBAAM;YACjB,OAAO,sBAAA;YACP,WAAW,EAAE,qBAAQ;YACrB,WAAW,EAAE,qBAAQ;YACrB,MAAM,qBAAA;YACN,KAAK,oBAAA;YACL,QAAQ,uBAAA;YACR,OAAO,sBAAA;YACP,WAAW,EAAE,qBAAQ;YACrB,WAAW,EAAE,qBAAQ;YACrB,kBAAkB,EAAE,4BAAe;SACpC,CAAC;QAOA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,2BAAM,GAAb,UAAc,OAAyB;QACrC,IAAI,CAAC,OAAO,GAAG,IAAA,cAAO,EAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,0BAAK,GAAZ;QACE,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;;OAGG;IACI,+BAAU,GAAjB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,0BAAK,GAAZ;QACE,IAAI,CAAC,MAAM,CAAC;YACV,eAAe,EAAE,EAAE;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,4BAAO,GAAd;QACQ,IAAA,KAAoB,IAAI,CAAC,OAAO,EAA9B,KAAK,WAAA,EAAE,MAAM,YAAiB,CAAC;QACvC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,8BAAS,GAAhB;QACQ,IAAA,KAA0B,IAAI,CAAC,OAAO,EAApC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAiB,CAAC;QAC7C,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;IACrD,CAAC;IAED;;;;OAIG;IACI,8BAAS,GAAhB;QAAiB,cAAuB;aAAvB,UAAuB,EAAvB,qBAAuB,EAAvB,IAAuB;YAAvB,yBAAuB;;QAC9B,IAAA,eAAe,GAAK,IAAI,CAAC,OAAO,gBAAjB,CAAkB;QACzC,IAAI,CAAC,MAAM,CAAC;YACV,eAAe,yCAAM,eAAe,qCAAM,IAAI,kBAAE;SACjD,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,wBAAG,GAAV,UAAW,MAAwB;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,2BAAM,GAAb,UAAc,MAAwB;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEO,iCAAY,GAApB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;IACf,oBAAoB;IACpB,8CAA8C;IACtC,4BAAO,GAAf,UAAgB,MAAc;;QAAd,uBAAA,EAAA,cAAc;QAC5B,IAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,yBAAI,IAAI,CAAC,OAAO,CAAC,eAAe,UAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAC5G,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,UAAC,CAAc,IAAK,OAAA,CAAC,CAAC,WAAW,EAAb,CAAa,CAAC,CAAC,CAAC,UAAC,CAAc,IAAK,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC;QAC5F,IAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,GAAG,GAAG,UAAC,SAAoB,EAAE,QAAe;YAAf,yBAAA,EAAA,eAAe;YAAK,OAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAA,cAAM,EAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAAzD,CAAyD,CAAC;;YAEjH,KAA8B,IAAA,oBAAA,SAAA,eAAe,CAAA,gDAAA,6EAAE;gBAApC,IAAA,KAAA,iCAAe,EAAd,MAAI,QAAA,EAAK,IAAI,cAAA;gBACvB,IAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAI,CAAC,CAAC;gBAClD,IAAI,iBAAiB,EAAE;oBACf,IAAA,KAA0B,IAAI,CAAC,OAAO,EAApC,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAiB,CAAC;oBAC7C,IAAM,WAAW,GAAG,iBAAiB,0BAAK,IAAI,WAAG,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;oBACtE,IAAI,IAAA,gBAAQ,EAAC,WAAW,CAAC,EAAE;wBACzB,sBAAsB;wBACtB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBAC5B;yBAAM;wBACL,2CAA2C;wBAC3C,IAAI,QAAQ,CAAC,MAAM,EAAE;4BACnB,IAAM,WAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAC/D,GAAG,CAAC,WAAS,CAAC,CAAC;4BACf,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;yBACrC;wBACD,IAAM,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,eAAQ,CAAC;wBAClD,GAAG,CAAC,SAAS,EAAE,MAAI,KAAK,UAAU,CAAC,CAAC,CAAC,kBAAkB;qBACxD;iBACF;aACF;;;;;;;;;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC/D,GAAG,CAAC,SAAS,CAAC,CAAC;SAChB;QAED,OAAO,eAAO,wCAAsB,UAAU,WAAE;IAClD,CAAC;IAED,oBAAoB;IACZ,0CAAqB,GAA7B,UAA8B,QAAmB,EAAE,MAAe;QAChE,IAAM,MAAM,GAAG,gBAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAI,MAAM;YAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC/B,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,gBAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE;YACV,gBAAI,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,UAAC,MAAe;YACrB,IAAM,OAAO,GAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACnD,gBAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC7C,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC;IACH,iBAAC;AAAD,CAAC,AA7LD,IA6LC;AA7LY,gCAAU"}