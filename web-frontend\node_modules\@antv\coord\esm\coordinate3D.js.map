{"version": 3, "file": "coordinate3D.js", "sourceRoot": "src/", "sources": ["coordinate3D.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AAC/C,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEvC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAE9E;IA0BE;;;OAGG;IACH,sBAAY,OAA4B;QAvBxC,QAAQ;QACA,YAAO,GAAc;YAC3B,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,GAAG;YACV,eAAe,EAAE,EAAE;SACpB,CAAC;QAEF,YAAY;QACJ,iBAAY,GAAG;YACrB,WAAW,aAAA;YACX,WAAW,aAAA;YACX,OAAO,SAAA;YACP,WAAW,aAAA;SACZ,CAAC;QAOA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAED;;;OAGG;IACI,6BAAM,GAAb,UAAc,OAA2B;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,4BAAK,GAAZ;QACE,OAAO,IAAI,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,iCAAU,GAAjB;QACE,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;OAEG;IACI,4BAAK,GAAZ;QACE,IAAI,CAAC,MAAM,CAAC;YACV,eAAe,EAAE,EAAE;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,8BAAO,GAAd;QACQ,IAAA,KAA2B,IAAI,CAAC,OAAO,EAArC,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,KAAK,WAAiB,CAAC;QAC9C,OAAO,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,gCAAS,GAAhB;QACQ,IAAA,KAAoC,IAAI,CAAC,OAAO,EAA9C,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,KAAK,WAAiB,CAAC;QACvD,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1E,CAAC;IAED;;;;OAIG;IACI,gCAAS,GAAhB;QAAiB,cAAyB;aAAzB,UAAyB,EAAzB,qBAAyB,EAAzB,IAAyB;YAAzB,yBAAyB;;QAChC,IAAA,eAAe,GAAK,IAAI,CAAC,OAAO,gBAAjB,CAAkB;QACzC,IAAI,CAAC,MAAM,CAAC;YACV,eAAe,yCAAM,eAAe,qCAAM,IAAI,kBAAE;SACjD,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACI,0BAAG,GAAV,UAAW,MAAwB;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;;;OAIG;IACI,6BAAM,GAAb,UAAc,MAAwB;QACpC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAEO,mCAAY,GAApB;QACE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,eAAe;IACf,oBAAoB;IACpB,8CAA8C;IACtC,8BAAO,GAAf,UAAgB,MAAc;;QAAd,uBAAA,EAAA,cAAc;QAC5B,IAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,yBAAI,IAAI,CAAC,OAAO,CAAC,eAAe,UAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;QAC5G,IAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,UAAC,CAAgB,IAAK,OAAA,CAAC,CAAC,WAAW,EAAb,CAAa,CAAC,CAAC,CAAC,UAAC,CAAgB,IAAK,OAAA,CAAC,CAAC,SAAS,EAAX,CAAW,CAAC;QAChG,IAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,IAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAM,GAAG,GAAG,UAAC,SAAsB,EAAE,QAAe;YAAf,yBAAA,EAAA,eAAe;YAClD,OAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAA3D,CAA2D,CAAC;;YAE9D,KAA8B,IAAA,oBAAA,SAAA,eAAe,CAAA,gDAAA,6EAAE;gBAApC,IAAA,KAAA,iCAAe,EAAd,MAAI,QAAA,EAAK,IAAI,cAAA;gBACvB,IAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAI,CAAC,CAAC;gBAClD,IAAI,iBAAiB,EAAE;oBACf,IAAA,KAAoC,IAAI,CAAC,OAAO,EAA9C,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,CAAC,OAAA,EAAE,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,KAAK,WAAiB,CAAC;oBACvD,IAAM,WAAW,GAAG,iBAAiB,0BAAK,IAAI,WAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;oBAEhF,IAAI,QAAQ,CAAC,WAAW,CAAC,EAAE;wBACzB,sBAAsB;wBACtB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;qBAC5B;yBAAM;wBACL,2CAA2C;wBAC3C,IAAI,QAAQ,CAAC,MAAM,EAAE;4BACnB,IAAM,WAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAC/D,GAAG,CAAC,WAAS,CAAC,CAAC;4BACf,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;yBACrC;wBACD,IAAM,SAAS,GAAG,MAAM,CAAC,WAA4B,CAAC,IAAI,QAAQ,CAAC;wBACnE,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;qBACtB;iBACF;aACF;;;;;;;;;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,MAAM,EAAE;YACnB,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC/D,GAAG,CAAC,SAAS,CAAC,CAAC;SAChB;QAED,OAAO,OAAO,wCAAsB,UAAU,WAAE;IAClD,CAAC;IAED,oBAAoB;IACZ,4CAAqB,GAA7B,UAA8B,QAAmB,EAAE,MAAe;QAChE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC7B,IAAI,MAAM;YAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC/B,QAAQ,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,EAA3B,CAA2B,CAAC,CAAC;QACrD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,UAAC,MAAe;YACrB,IAAM,OAAO,GAAY,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAE9D,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAC7C,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC;IACJ,CAAC;IACH,mBAAC;AAAD,CAAC,AAnLD,IAmLC"}